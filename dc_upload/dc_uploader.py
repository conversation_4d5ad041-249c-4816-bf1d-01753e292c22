#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DC文件上传工具
实现图片和任务上传功能
"""

import os
import requests
import mimetypes
import json
import asyncio
from pathlib import Path
from playwright.async_api import async_playwright
from pqdm.threads import pqdm
import threading


class DCUploader:
    """DC文件上传器"""

    def __init__(self, authorization_token):
        """
        初始化上传器
        
        Args:
            authorization_token: JWT认证令牌
        """
        self.base_url = "https://api.dc.zentaotech.com"
        self.headers = {
            "Authorization": authorization_token,
            "Client": "admin_login_scene:",
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "dnt": "1",
            "origin": "https://demo.dc.zentaotech.com",
            "referer": "https://demo.dc.zentaotech.com/",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "priority": "u=1, i"
        }
    
    def upload_image(self, file_path):
        """
        上传图片文件
        
        Args:
            file_path: 本地图片文件路径
            
        Returns:
            str: 上传成功返回文件URL，失败返回None
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件类型不支持
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取文件信息
        file_path = Path(file_path)
        file_name = file_path.name
        
        # 检查是否为图片文件
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type or not mime_type.startswith('image/'):
            raise ValueError(f"不支持的文件类型: {mime_type}，仅支持图片文件")
        
        # 准备上传URL
        upload_url = f"{self.base_url}/adminapi/file/upload"
        
        try:
            # 准备文件数据
            with open(file_path, 'rb') as file:
                files = {
                    'file': (file_name, file, mime_type)
                }
                
                # 发送POST请求
                response = requests.post(
                    upload_url,
                    headers=self.headers,
                    files=files,
                    timeout=30
                )
                
                # 检查响应状态
                response.raise_for_status()

                # 解析响应并返回fileUrl
                if response.content:
                    response_data = response.json()
                    if response_data.get('code') == 200 and 'data' in response_data:
                        return response_data['data'].get('fileUrl')

                return None
                
        except requests.exceptions.RequestException as e:
            print(f"上传失败: {str(e)}")
            return None
        except Exception as e:
            print(f"未知错误: {str(e)}")
            return None
    
    def search_question(self, question_no):
        """
        搜索题目

        Args:
            question_no: 题目编号

        Returns:
            dict: 搜索结果，包含题目列表和数量
        """
        try:
            search_url = f"{self.base_url}/adminapi/Question/list"
            params = {
                'page': 1,
                'size': 10,
                'question_status': 0,
                'subject_id': 1,
                'question_no': question_no
            }

            response = requests.get(
                search_url,
                headers=self.headers,
                params=params,
                timeout=30
            )

            response.raise_for_status()

            if response.content:
                response_data = response.json()
                if response_data.get('code') == 200:
                    return response_data.get('data', {})

            return None

        except requests.exceptions.RequestException as e:
            print(f"搜索题目失败: {str(e)}")
            return None
        except Exception as e:
            print(f"搜索题目未知错误: {str(e)}")
            return None

    def process_question_edit_or_create(self, quest_data):
        """
        处理题目编辑或新建的逻辑

        Args:
            quest_data: 题目数据字典，包含编号等信息

        Returns:
            dict: 包含处理结果的字典，包含target_url和action
        """
        question_no = quest_data.get('编号')
        if not question_no:
            return {
                'success': False,
                'error': '题目编号不存在',
                'target_url': None,
                'action': None
            }

        print(f"处理题目编号: {question_no}")

        # 1. 搜索相关的 question_id
        search_result = self.search_question(question_no)

        if not search_result:
            return {
                'success': False,
                'error': '搜索题目失败',
                'target_url': None,
                'action': None
            }

        data_list = search_result.get('data', [])
        total_count = len(data_list)

        target_url = ''
        action = ''

        if total_count == 0:
            # 2. 如果数量是0，直接新建
            target_url = 'https://demo.dc.zentaotech.com/question/add_question'
            action = 'create'
            print(f"题目不存在，需要新建: {question_no} -> {target_url}")
        elif total_count == 1:
            # 3. 如果数量是1，找第一个的 question_id
            question_id = data_list[0].get('question_id')
            target_url = f'https://demo.dc.zentaotech.com/question/edit?question_id={question_id}'
            action = 'edit'
            print(f"找到唯一题目，编辑: {question_no} -> {target_url}")
        else:
            # 4. 如果不是1，找question_no和json['编号']相同的
            exact_match = None
            for item in data_list:
                if item.get('question_no') == question_no:
                    exact_match = item
                    break

            if exact_match:
                question_id = exact_match.get('question_id')
                target_url = f'https://demo.dc.zentaotech.com/question/edit?question_id={question_id}'
                action = 'edit'
                print(f"找到精确匹配题目，编辑: {question_no} -> {target_url}")
            else:
                print(f"找到多个题目但无精确匹配，需要手动处理: {question_no}")
                print(f"搜索结果:", [{'question_id': item.get('question_id'), 'question_no': item.get('question_no')} for item in data_list])
                return {
                    'success': False,
                    'error': '找到多个题目但无精确匹配',
                    'target_url': None,
                    'action': None,
                    'search_results': data_list
                }

        # 确定question_id
        question_id = None
        if total_count == 1:
            question_id = data_list[0].get('question_id')
        elif total_count > 1 and exact_match:
            question_id = exact_match.get('question_id')

        return {
            'success': True,
            'target_url': target_url,
            'action': action,
            'question_id': question_id
        }

    def upload_quest(self, quest_data):
        """
        上传题目数据

        Args:
            quest_data: 题目数据字典，包含编号、学科、题型等信息

        Returns:
            bool: 上传成功返回True，失败返回False
        """
        return asyncio.run(self._upload_quest_async(quest_data))

    async def _upload_quest_async(self, quest_data, edit_mode=False):
        """异步上传题目数据"""
        try:
            # 首先处理题目编辑或新建的逻辑
            process_result = self.process_question_edit_or_create(quest_data)

            if not process_result['success']:
                print(f"处理题目失败: {process_result['error']}")
                return False

            target_url = process_result['target_url']
            action = process_result['action']

            # 将目标URL写入到题目数据中（可选）
            quest_data['_target_url'] = target_url
            quest_data['_action'] = action

            async with async_playwright() as p:
                # 启动Chrome浏览器
                browser = await p.chromium.launch(
                    headless=False,
                    channel="chromium"  # 使用系统安装的Chrome
                )
                context = await browser.new_context()

                # 注入authorization到localStorage
                page = await context.new_page()
                await page.goto("https://demo.dc.zentaotech.com")

                # 注入token到localStorage
                await page.evaluate(f"""
                    localStorage.setItem('Authorization', '{self.headers["Authorization"]}');
                    localStorage.setItem('Client', 'admin_login_scene:');
                """)

                # 导航到目标页面（新建或编辑）
                print(f"导航到页面: {target_url}")
                await page.goto(target_url)
                await page.wait_for_load_state('networkidle')

                # 等待页面完全加载
                await page.wait_for_timeout(2000)

                # 填写编号
                if "编号" in quest_data:
                    try:
                        await page.wait_for_selector('input[placeholder="请输入编号"]', timeout=10000)
                        await page.fill('input[placeholder="请输入编号"]', quest_data["编号"])
                        print(f"已填写编号: {quest_data['编号']}")
                    except Exception as e:
                        print(f"填写编号失败: {str(e)}")

                # 选择学科
                if "学科" in quest_data:
                    print(f"正在选择学科: {quest_data['学科']}")
                    await self._select_radio_option(page, quest_data["学科"])

                # 选择题型
                if "题型" in quest_data:
                    print(f"正在选择题型: {quest_data['题型']}")
                    await self._select_radio_option(page, quest_data["题型"])

                # 选择科目
                if "科目" in quest_data:
                    print(f"正在选择科目: {quest_data['科目']}")
                    await self._select_radio_option(page, quest_data["科目"])

                # 选择来源
                if "来源" in quest_data and quest_data["来源"]:
                    await self._select_source(page, quest_data["来源"])

                # 选择年份
                # if "年份" in quest_data:
                await self._select_year(page, str(2025))

                # 填写题目
                if "题干" in quest_data:
                    await self._fill_content_field(page, "题目", quest_data["题干"])

                # 填写分析点评
                if "分析点评" in quest_data:
                    await self._fill_content_field(page, "分析点评", quest_data["分析点评"])

                # 填写文字解答
                if "文字解答" in quest_data:
                    await self._fill_content_field(page, "文字解答", quest_data["文字解答"])

                # 处理选项（单选题）
                if "选项" in quest_data and quest_data["选项"]:
                    await self._fill_options(page, quest_data["选项"])

                # 填写答案
                if "答案" in quest_data:
                    # 检查是否是单选题
                    if "题型" in quest_data and quest_data["题型"] == "单选题":
                        await self._select_answer_option(page, quest_data["答案"])
                    else:
                        await self._fill_content_field(page, "答案", quest_data["答案"])

                # 点击提交按钮
                print("题目信息已填写完成，正在提交...")
                submit_success = await self._click_submit_button(page)

                if submit_success:
                    print("题目提交成功！")
                    await page.wait_for_timeout(3000)  # 等待3秒查看结果
                else:
                    print("未找到提交按钮，请手动提交")
                    await page.wait_for_timeout(5000)  # 等待5秒让用户手动操作

                await browser.close()
                return submit_success

        except Exception as e:
            print(f"上传题目失败: {str(e)}")
            return False

    async def _select_radio_option(self, page, text):
        """选择单选按钮选项"""
        try:
            # 查找所有.el-radio__label元素
            labels = await page.query_selector_all('.el-radio__label')
            for label in labels:
                label_text = await label.text_content()
                if label_text and label_text.strip() == text:
                    await label.click()
                    await page.wait_for_timeout(500)  # 等待选择生效
                    return True
            print(f"未找到选项: {text}")
            return False
        except Exception as e:
            print(f"选择选项失败 {text}: {str(e)}")
            return False

    async def _select_source(self, page, source_list):
        """选择来源（级联选择器）"""
        try:
            # 查找来源标签并点击
            source_label = await page.query_selector('label:has-text("来源")')
            if source_label:
                await source_label.click()
                await page.wait_for_timeout(1000)  # 等待弹窗出现

                # 按照source_list的顺序依次点击
                for i, source_item in enumerate(source_list):
                    # 重新查找.el-cascader-node元素
                    nodes = await page.query_selector_all('.el-cascader-node')
                    found = False
                    for node in nodes:
                        node_text = await node.text_content()
                        if node_text:
                            # 去掉所有空格进行比较
                            node_text_no_spaces = node_text.replace(' ', '')
                            source_item_no_spaces = source_item.replace(' ', '')
                            if node_text_no_spaces == source_item_no_spaces:
                                await node.click()
                                await page.wait_for_timeout(500)  # 等待下一级加载
                                found = True
                                print(f"已选择来源: {source_item} (匹配: {node_text})")
                                break

                    if not found:
                        print(f"未找到来源选项: {source_item}")
                        # 如果是第一级且没找到，选择第一个可用选项
                        if i == 0:
                            nodes = await page.query_selector_all('.el-cascader-node')
                            if nodes:
                                first_node = nodes[0]
                                first_node_text = await first_node.text_content()
                                await first_node.click()
                                await page.wait_for_timeout(500)
                                print(f"未找到指定来源，已选择第一个选项: {first_node_text}")
                                # 继续处理后续级别，但都选择第一个
                                for j in range(i + 1, len(source_list)):
                                    await page.wait_for_timeout(500)
                                    nodes = await page.query_selector_all('.el-cascader-node')
                                    if nodes:
                                        first_available = nodes[0]
                                        first_available_text = await first_available.text_content()
                                        await first_available.click()
                                        await page.wait_for_timeout(500)
                                        print(f"继续选择第一个可用选项: {first_available_text}")
                                return True
                            else:
                                print("没有可用的来源选项")
                                return False
                        else:
                            # 非第一级没找到，选择第一个可用选项
                            nodes = await page.query_selector_all('.el-cascader-node')
                            if nodes:
                                first_available = nodes[0]
                                first_available_text = await first_available.text_content()
                                await first_available.click()
                                await page.wait_for_timeout(500)
                                print(f"未找到指定选项，已选择第一个可用选项: {first_available_text}")
                            else:
                                print("没有可用的选项")
                                return False

                return True
            else:
                print("未找到来源标签")
                return False
        except Exception as e:
            print(f"选择来源失败: {str(e)}")
            return False


    async def _select_year(self, page, year=2025):
        """选择年份"""
        try:
            # 查找年份标签并点击
            year_label = await page.query_selector('label:has-text("年份")')
            if year_label:
                await year_label.click()
                await page.wait_for_timeout(1000)  # 等待下拉框出现

                # 查找年份选项
                items = await page.query_selector_all('.el-select-dropdown__item')
                for item in items:
                    item_text = await item.text_content()
                    if item_text and item_text.strip() == year:
                        await item.click()
                        await page.wait_for_timeout(500)
                        return True

                print(f"未找到年份选项: {year}")
                return False
            else:
                print("未找到年份标签")
                return False
        except Exception as e:
            print(f"选择年份失败: {str(e)}")
            return False

    async def _fill_content_field(self, page, field_name, content):
        """填写内容字段（题目、分析点评、文字解答、答案）"""
        try:
            # 查找对应的标签
            label = await page.query_selector(f'.el-form-item__label:has-text("{field_name}")')
            if label:
                # 获取标签的父元素
                parent = await label.query_selector('xpath=..')
                if parent:
                    # 在父元素下查找role="textbox"的元素
                    textbox = await parent.query_selector('[role="textbox"]')
                    if textbox:
                        # 先找到父元素下面的data-cke-tooltip-text="源"的元素并点击
                        source_button = await parent.query_selector('[data-cke-tooltip-text="源"]')
                        if source_button:
                            await source_button.click()
                            await page.wait_for_timeout(500)  # 等待切换到源模式
                            print(f"已点击{field_name}的源按钮")

                        # 然后点击父元素里的textarea元素并输入
                        textarea = await parent.query_selector('textarea')
                        if textarea:
                            # 先清空textarea的内容
                            await textarea.evaluate('textarea => textarea.value = ""')
                            await page.wait_for_timeout(200)  # 等待清空完成

                            # 然后点击并输入新内容
                            await textarea.click()
                            await page.keyboard.type(content)
                            await page.wait_for_timeout(500)

                            # 输入完成后再次点击源按钮切换回正常模式
                            if source_button:
                                await source_button.click()
                                await page.wait_for_timeout(500)  # 等待切换回正常模式
                                print(f"已切换{field_name}回正常模式")

                            print(f"已填写{field_name}: {content[:50]}...")
                            return True
                        else:
                            # 如果没有textarea，尝试原来的方式
                            await textbox.click()
                            await page.keyboard.press('Control+a')  # 全选
                            await page.keyboard.type(content)
                            await page.wait_for_timeout(500)
                            print(f"已填写{field_name}: {content[:50]}...")
                            return True
                    else:
                        print(f"未找到{field_name}的输入框")
                        return False
                else:
                    print(f"未找到{field_name}标签的父元素")
                    return False
            else:
                print(f"未找到{field_name}标签")
                return False
        except Exception as e:
            print(f"填写{field_name}失败: {str(e)}")
            return False

    async def _fill_options(self, page, options):
        """填写选项（单选题）"""
        try:
            for option in options:
                choice = option.get("choice", "")
                context = option.get("context", "")

                if not choice or not context:
                    continue

                # 查找对应的标签
                label = await page.query_selector(f'.el-form-item__label:has-text("{choice}")')
                if label:
                    # 获取标签的父元素
                    parent = await label.query_selector('xpath=..')
                    if parent:
                        # 在父元素下查找role="textbox"的元素
                        textbox = await parent.query_selector('[role="textbox"]')
                        if textbox:
                            # 先找到父元素下面的data-cke-tooltip-text="源"的元素并点击
                            source_button = await parent.query_selector('[data-cke-tooltip-text="源"]')
                            if source_button:
                                await source_button.click()
                                await page.wait_for_timeout(500)  # 等待切换到源模式
                                print(f"已点击选项 {choice} 的源按钮")

                            # 然后点击父元素里的textarea元素并输入
                            textarea = await parent.query_selector('textarea')
                            if textarea:
                                # 先清空textarea的内容
                                await textarea.evaluate('textarea => textarea.value = ""')
                                await page.wait_for_timeout(200)  # 等待清空完成

                                # 然后点击并输入新内容
                                await textarea.click()
                                await page.keyboard.type(context)
                                await page.wait_for_timeout(500)

                                # 输入完成后再次点击源按钮切换回正常模式
                                if source_button:
                                    await source_button.click()
                                    await page.wait_for_timeout(500)  # 等待切换回正常模式
                                    print(f"已切换选项 {choice} 回正常模式")

                                print(f"已填写选项 {choice}: {context}")
                            else:
                                # 如果没有textarea，尝试原来的方式
                                await textbox.click()
                                await page.keyboard.press('Control+a')  # 全选
                                await page.keyboard.type(context)
                                await page.wait_for_timeout(500)
                                print(f"已填写选项 {choice}: {context}")
                        else:
                            print(f"未找到选项 {choice} 的输入框")
                    else:
                        print(f"未找到选项 {choice} 标签的父元素")
                else:
                    print(f"未找到选项标签: {choice}")

            return True
        except Exception as e:
            print(f"填写选项失败: {str(e)}")
            return False

    async def _select_answer_option(self, page, answer):
        """选择答案选项（单选题）"""
        try:
            # 查找所有.el-radio__label元素
            labels = await page.query_selector_all('.el-radio__label')
            for label in labels:
                label_text = await label.text_content()
                if label_text and label_text.strip() == answer:
                    await label.click()
                    await page.wait_for_timeout(500)  # 等待选择生效
                    print(f"已选择答案: {answer}")
                    return True

            print(f"未找到答案选项: {answer}")
            return False
        except Exception as e:
            print(f"选择答案选项失败: {str(e)}")
            return False

    async def _click_submit_button(self, page):
        """点击提交按钮"""
        try:
            # 查找textContent为"提交"的button元素
            submit_button = await page.query_selector('button:has-text("提交")')
            if submit_button:
                await submit_button.click()
                await page.wait_for_timeout(1000)  # 等待提交处理
                return True
            else:
                # 如果没找到，尝试其他可能的选择器
                buttons = await page.query_selector_all('button')
                for button in buttons:
                    button_text = await button.text_content()
                    if button_text and button_text.strip() == "提交":
                        await button.click()
                        await page.wait_for_timeout(1000)
                        return True

                print("未找到提交按钮")
                return False
        except Exception as e:
            print(f"点击提交按钮失败: {str(e)}")
            return False


def process_question_edit_or_create_batch(folder_path, token):
    """
    批量处理题目编辑和新建的逻辑

    Args:
        folder_path: 包含JSON文件的文件夹路径
        token: JWT认证令牌
    """
    try:
        folder_path = Path(folder_path)
        if not folder_path.exists():
            print(f"文件夹不存在: {folder_path}")
            return

        if not folder_path.is_dir():
            print(f"路径不是文件夹: {folder_path}")
            return

        # 查找所有JSON文件
        json_files = list(folder_path.glob("*.json"))
        if not json_files:
            print(f"文件夹中没有找到JSON文件: {folder_path}")
            return

        print(f"找到 {len(json_files)} 个JSON文件，开始处理...")

        # 创建上传器实例
        uploader = DCUploader(token)

        success_count = 0
        failed_count = 0

        for json_file in json_files:
            try:
                # 读取JSON文件
                with open(json_file, "r", encoding="utf-8") as f:
                    quest_data = json.load(f)

                quest_id = quest_data.get('编号', '未知编号')
                print(f"\n处理文件: {json_file.name} (编号: {quest_id})")

                # 检查是否有数学公式错误，如果有则跳过
                if '_math_errors' in quest_data:
                    print(f"⚠️  跳过文件: {json_file.name} - 包含数学公式错误")
                    continue

                # 处理题目编辑或新建的逻辑
                process_result = uploader.process_question_edit_or_create(quest_data)

                if process_result['success']:
                    # 将结果写回JSON文件
                    quest_data['_target_url'] = process_result['target_url']
                    quest_data['_action'] = process_result['action']

                    with open(json_file, "w", encoding="utf-8") as f:
                        json.dump(quest_data, f, ensure_ascii=False, indent=2)

                    print(f"✓ 已处理: {json_file.name} -> {process_result['target_url']}")
                    success_count += 1
                else:
                    print(f"✗ 处理失败: {json_file.name} - {process_result['error']}")
                    failed_count += 1

                # 添加延迟避免请求过于频繁
                import time
                time.sleep(0.5)

            except json.JSONDecodeError as e:
                print(f"✗ JSON格式错误: {json_file.name} - {str(e)}")
                failed_count += 1
            except Exception as e:
                print(f"✗ 处理文件失败: {json_file.name} - {str(e)}")
                failed_count += 1

        print(f"\n批量处理完成:")
        print(f"成功: {success_count} 个")
        print(f"失败: {failed_count} 个")
        print(f"总计: {len(json_files)} 个")

    except Exception as e:
        print(f"批量处理过程中发生错误: {str(e)}")


def process_single_file(args):
    """
    处理单个JSON文件的函数，用于并行处理

    Args:
        args: 包含 (json_file_path, token) 的元组

    Returns:
        dict: 包含处理结果的字典
    """
    json_file_path, token = args
    json_file = Path(json_file_path)

    result = {
        'file_name': json_file.name,
        'success': False,
        'error': None,
        'quest_id': None
    }

    try:
        # 创建上传器实例
        uploader = DCUploader(token)

        # 读取JSON文件
        with open(json_file, "r", encoding="utf-8") as f:
            quest_data = json.load(f)

        quest_id = quest_data.get('编号', '未知编号')
        result['quest_id'] = quest_id

        # 检查是否有数学公式错误，如果有则跳过
        if '_math_errors' in quest_data:
            result['success'] = True  # 标记为成功但实际跳过
            result['error'] = "跳过 - 包含数学公式错误"
            print(f"[并行处理] ⚠️  跳过题目: {quest_id} ({json_file.name}) - 包含数学公式错误")
            return result

        print(f"[并行处理] 开始上传题目: {quest_id} ({json_file.name})")

        # 上传题目
        upload_success = uploader.upload_quest(quest_data)

        if upload_success:
            result['success'] = True
            print(f"[并行处理] ✓ 题目上传成功: {quest_id} ({json_file.name})")
        else:
            result['error'] = "上传失败"
            print(f"[并行处理] ✗ 题目上传失败: {quest_id} ({json_file.name})")

    except json.JSONDecodeError as e:
        result['error'] = f"JSON格式错误: {str(e)}"
        print(f"[并行处理] ✗ JSON格式错误: {json_file.name} - {str(e)}")
    except Exception as e:
        result['error'] = f"处理失败: {str(e)}"
        print(f"[并行处理] ✗ 处理文件失败: {json_file.name} - {str(e)}")

    return result


def main(folder_path=None, parallel_count=3, mode="upload"):
    """
    主函数，支持批量处理文件夹中的所有JSON文件

    Args:
        folder_path: 包含JSON文件的文件夹路径，如果为None则使用默认路径
        parallel_count: 并行处理的数量，默认为3
        mode: 处理模式，"upload"为上传模式，"process"为题目编辑/新建处理模式
    """
    # 示例JWT令牌（请替换为实际的令牌）
    token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM3NzQ1MDEsIm5iZiI6MTc1Mzc3NDUwMSwiZXhwIjoxNzUzODYwOTAxLCJkYXRhIjp7InVzZXJfaWQiOjIxLCJ1c2VyX25hbWUiOiJzaGl0aWx1cnUifX0.4IHq7qVo61EldpUfWfliiK1eKoSDRw3OUrEv0bdMqKc"

    # 如果是题目编辑/新建处理模式
    if mode == "process":
        if folder_path is None:
            folder_path = "/Users/<USER>/Downloads/book_01/html_vis"

        print(f"题目编辑/新建处理模式")
        print(f"处理文件夹: {folder_path}")
        process_question_edit_or_create_batch(folder_path, token)
        return

    # 创建上传器实例
    uploader = DCUploader(token)

    # 如果没有指定文件夹路径，使用默认路径或单个文件
    if folder_path is None:
        # 测试单个文件
        single_file_path = "/Users/<USER>/Downloads/book_01/html_vis/李-880题数二_高等数学 第五章 二重积分_50-6_81412.json"
        try:
            with open(single_file_path, "r", encoding="utf-8") as f:
                quest_data = json.load(f)

            # 检查是否有数学公式错误，如果有则跳过
            if '_math_errors' in quest_data:
                print("⚠️  跳过文件 - 包含数学公式错误")
                return

            print("开始上传题目...")
            result = uploader.upload_quest(quest_data)
            print(f"题目上传结果: {result}")

        except FileNotFoundError:
            print(f"未找到文件: {single_file_path}")
        except Exception as e:
            print(f"读取文件失败: {str(e)}")
        return

    # 批量处理文件夹中的所有JSON文件
    try:
        folder_path = Path(folder_path)
        if not folder_path.exists():
            print(f"文件夹不存在: {folder_path}")
            return

        if not folder_path.is_dir():
            print(f"路径不是文件夹: {folder_path}")
            return

        # 查找所有JSON文件
        all_json_files = list(folder_path.glob("*.json"))
        if not all_json_files:
            print(f"文件夹中没有找到JSON文件: {folder_path}")
            return

        # 过滤只包含"第一章"或"第二章"的文件
        json_files = []
        for json_file in all_json_files:
            if "第一章" in json_file.name or "第二章" in json_file.name:
                json_files.append(json_file)

        if not json_files:
            print(f"文件夹中没有找到包含'第一章'或'第二章'的JSON文件")
            print(f"总共有 {len(all_json_files)} 个JSON文件，但都不符合条件")
            return

        print(f"找到 {len(all_json_files)} 个JSON文件，其中 {len(json_files)} 个包含'第一章'或'第二章'")
        print(f"开始并行批量处理...")
        print(f"并行数量: {parallel_count}")

        # 准备并行处理的参数列表
        args_list = [(str(json_file), token) for json_file in json_files]

        # 使用pqdm进行并行处理
        print("\n开始并行处理...")
        results = pqdm(
            args_list,
            process_single_file,
            n_jobs=parallel_count,
            desc="上传题目",
            unit="个"
        )

        # 统计结果
        success_count = sum(1 for result in results if result['success'])
        failed_count = len(results) - success_count

        print(f"\n并行批量处理完成:")
        print(f"成功: {success_count} 个")
        print(f"失败: {failed_count} 个")
        print(f"总计: {len(results)} 个")

        # 显示失败的文件详情
        if failed_count > 0:
            print(f"\n失败文件详情:")
            for result in results:
                if not result['success']:
                    print(f"  ✗ {result['file_name']}: {result['error']}")

    except Exception as e:
        print(f"批量处理过程中发生错误: {str(e)}")


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        first_arg = sys.argv[1]

        # 检查是否是处理模式
        if first_arg == "process":
            folder_path = None
            if len(sys.argv) > 2:
                folder_path = sys.argv[2]

            print("题目编辑/新建处理模式")
            if folder_path:
                print(f"使用指定的文件夹路径: {folder_path}")
            else:
                print("使用默认文件夹路径")
            main(folder_path, mode="process")
        else:
            # 上传模式
            folder_path = first_arg
            parallel_count = 3  # 默认并行数量

            # 检查是否指定了并行数量
            if len(sys.argv) > 2:
                try:
                    parallel_count = int(sys.argv[2])
                    if parallel_count < 1:
                        parallel_count = 1
                    elif parallel_count > 10:
                        parallel_count = 10
                        print("警告: 并行数量限制为最大10个")
                except ValueError:
                    print("警告: 并行数量参数无效，使用默认值3")
                    parallel_count = 3

            print(f"上传模式")
            print(f"使用指定的文件夹路径: {folder_path}")
            print(f"并行处理数量: {parallel_count}")
            main(folder_path, parallel_count, mode="upload")
    else:
        print("未指定参数，使用默认单文件测试模式")
        print("使用方法:")
        print("  题目编辑/新建处理: python dc_uploader.py process [文件夹路径]")
        print("  题目上传: python dc_uploader.py <文件夹路径> [并行数量]")
        print("例如:")
        print("  python dc_uploader.py process /path/to/json/files")
        print("  python dc_uploader.py /path/to/json/files 5")
        main()
