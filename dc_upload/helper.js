

const fs = require('fs');
const path = require('path');

// 需要先安装这些依赖: npm install form-data axios mathjax-node
const FormData = require('form-data');
const axios = require('axios');
const mathjax = require('mathjax-node');

const cleanDir = (dirPath) => {
    // 删除所有非 json 文件
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;

        files.forEach(file => {
            const filePath = path.join(dirPath, file);
            const extname = path.extname(file);

            if (extname !== '.json') {
                fs.unlink(filePath, (err) => {
                    if (err) throw err;
                    console.log(`Deleted ${filePath}`);
                });
            }
        });
    });
}


const setSource = (filename) => {
    const content = fs.readFileSync(filename, 'utf8');
    const json = JSON.parse(content);
    const name = json['编号'].replace('880题数二', '880题数学二');
    const group = name.split('-')[1];
    const sourceArr = group.split('_');
    sourceArr.pop();
    json['来源'] = sourceArr;
    fs.writeFileSync(filename, JSON.stringify(json, null, 2), 'utf8');
    return json;
}

const setSources = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;

        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                setSource(filePath);
            }
        });
    });
}


// 需要先安装 xlsx: npm install xlsx
const XLSX = require('xlsx');

const setName = () => {
    const excelPath = '/Users/<USER>/Downloads/编号转换结果.xlsx';
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });

    // 跳过表头
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const filename = row[0]; // 第一列
        const newId = row[2];    // 第三列
        if (!filename || !newId) continue;
        const filePath = path.join(dirPath, filename);
        if (!fs.existsSync(filePath)) {
            console.log(`文件不存在: ${filePath}`);
            continue;
        }
        try {
            console.log(`正在处理文件: ${filePath}`);
            const content = fs.readFileSync(filePath, 'utf8');

            const json = JSON.parse(content);
            json['编号'] = newId;
            fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
            console.log(`已更新编号: ${filePath}`);
        } catch (err) {
            console.error(`处理失败: ${filePath}`, err);
        }
    }
}

// 打印所有 json 文件的 '题型' 字段
const printQuestionTypes = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    console.log(`${json['题型']}`);
                } catch (err) {
                    console.error(`读取失败: ${filePath}`, err);
                }
            }
        });
    });
}


// 处理所有单选题，提取并修改答案字段为 A-D
const processSingleChoice = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    if (json['题型'] === '选择题' && json['答案']) {
                        // 答案可能是 "A. xxx" 或 "A"
                        let ans = json['答案'];
                        let match = ans.match(/[A-D]/);
                        if (match) {
                            json['答案'] = match[0];
                            fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
                            console.log(`${file}: 答案已处理为 ${match[0]}`);
                        } else {
                            console.log(`${file}: 未找到 A-D 答案`);
                        }
                    }
                } catch (err) {
                    console.error(`处理失败: ${filePath}`, err);
                }
            }
        });
    });
}

// 处理所有选择题题型，修改为单选题
const processQuestionType = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    if (json['题型'] === '选择题') {
                        json['题型'] = '单选题'
                    }
                    if (json['题型'] === '计算题') {
                        json['题型'] = '解答题'
                    }
                    fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
                } catch (err) {
                    console.error(`处理失败: ${filePath}`, err);
                }
            }
        });
    });
}

// 处理图片链接，上传并替换为img标签
const processImageLinks = async () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    const imagePath = '/Users/<USER>/Downloads/book_01/images/book_01';

    // 你的JWT认证令牌，请替换为实际的令牌
    const authToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM2ODg0OTksIm5iZiI6MTc1MzY4ODQ5OSwiZXhwIjoxNzUzNzc0ODk5LCJkYXRhIjp7InVzZXJfaWQiOjIxLCJ1c2VyX25hbWUiOiJzaGl0aWx1cnUifX0.eqNfsxs9iN0khPdFsvptWkbOjCzuJ1WitozdDy6f6yY";

    // 实际的上传函数，基于dc_uploader.py的逻辑
    const uploadImage = async (filePath) => {
        try {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }

            const fileName = path.basename(filePath);
            const form = new FormData();

            // 添加文件到表单
            form.append('file', fs.createReadStream(filePath), fileName);

            // 请求头
            const headers = {
                ...form.getHeaders(),
                "authorization": authToken,
                "client": "admin_login_scene:",
                "accept": "application/json, text/plain, */*",
                "origin": "https://demo.dc.zentaotech.com",
                "referer": "https://demo.dc.zentaotech.com/"
            };

            // 发送上传请求
            const response = await axios.post(
                'https://api.dc.zentaotech.com/adminapi/file/upload',
                form,
                {
                    headers: headers,
                    timeout: 30000
                }
            );

            // 检查响应
            if (response.data && response.data.code === 200 && response.data.data) {
                const fileUrl = response.data.data.fileUrl;
                console.log(`上传成功: ${fileName} -> ${fileUrl}`);
                return fileUrl;
            } else {
                throw new Error(`上传失败: ${JSON.stringify(response.data)}`);
            }

        } catch (error) {
            console.error(`上传图片失败 ${filePath}:`, error.message);
            return null;
        }
    };

    const processValue = async (value) => {
        if (typeof value !== 'string') return value;

        // 匹配图片链接模式 - 匹配所有 ![]() 格式的链接
        const imageRegex = /!\[[^\]]*\]\(([^)]+)\)/g;

        let newValue = value;
        const matches = [...value.matchAll(imageRegex)];

        for (const match of matches) {
            const fullMatch = match[0];
            const imageUrl = match[1]; // 提取图片URL

            // 尝试从URL中提取文件ID（如果是原来的格式）
            const fileIdMatch = imageUrl.match(/\/([a-f0-9-]+)\.png_yjs$/);

            if (fileIdMatch) {
                // 如果是原来的格式，使用原来的逻辑
                const fileId = fileIdMatch[1];
                const localImagePath = path.join(imagePath, `${fileId}.png`);

                if (fs.existsSync(localImagePath)) {
                    try {
                        const imgUrl = await uploadImage(localImagePath);
                        if (imgUrl) {
                            const imgTag = `<img src="${imgUrl}" />`;
                            newValue = newValue.replace(fullMatch, imgTag);
                            console.log(`已替换图片: ${fileId} -> ${imgUrl}`);
                        } else {
                            console.log(`上传失败，保留原链接: ${fileId}`);
                        }
                    } catch (err) {
                        console.error(`上传失败: ${fileId}`, err);
                    }
                } else {
                    console.log(`图片文件不存在: ${localImagePath}`);
                }
            } else {
                // 如果不是原来的格式，直接替换为img标签（不上传）
                const imgTag = `<img src="${imageUrl}" />`;
                newValue = newValue.replace(fullMatch, imgTag);
                console.log(`已替换图片链接为img标签: ${imageUrl}`);
            }
        }

        return newValue;
    };

    const processObject = async (obj) => {
        if (Array.isArray(obj)) {
            const newArray = [];
            for (const item of obj) {
                newArray.push(await processObject(item));
            }
            return newArray;
        } else if (typeof obj === 'object' && obj !== null) {
            const newObj = {};
            for (const [key, value] of Object.entries(obj)) {
                newObj[key] = await processObject(value);
            }
            return newObj;
        } else {
            return await processValue(obj);
        }
    };

    const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.json'));

    for (const file of files) {
        const filePath = path.join(dirPath, file);
        try {
            console.log(`处理文件: ${file}`);
            const content = fs.readFileSync(filePath, 'utf8');
            const json = JSON.parse(content);

            const updatedJson = await processObject(json);

            fs.writeFileSync(filePath, JSON.stringify(updatedJson, null, 2), 'utf8');
            console.log(`已更新文件: ${file}`);
        } catch (err) {
            console.error(`处理失败: ${file}`, err);
        }
    }
};

// 替换JSON中所有值的转义字符为HTML标签
const replaceEscapeCharacters = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    
    const processValue = (value) => {
        if (typeof value !== 'string') return value;
        
        // 保护数学公式的函数
        const protectMathFormulas = (text) => {
            const mathFormulas = [];
            let index = 0;
            
            // 先保存所有的数学公式（$...$）
            const protectedText = text.replace(/\$[^$]*\$/g, (match) => {
                mathFormulas[index] = match;
                return `__MATH_FORMULA_${index++}__`;
            });
            
            return { protectedText, mathFormulas };
        };
        
        // 恢复数学公式的函数
        const restoreMathFormulas = (text, mathFormulas) => {
            return text.replace(/__MATH_FORMULA_(\d+)__/g, (match, index) => {
                return mathFormulas[index] || match;
            });
        };
        
        // 保护数学公式
        const { protectedText, mathFormulas } = protectMathFormulas(value);
        
        // 在保护数学公式后处理转义字符
        const processedText = protectedText
            .replace(/\n/g, '<br>')      // 换行符 -> <br>
            .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;')  // 制表符 -> 4个空格
        
        // 恢复数学公式
        return restoreMathFormulas(processedText, mathFormulas);
    };
    
    const processObject = (obj) => {
        if (Array.isArray(obj)) {
            return obj.map(item => processObject(item));
        } else if (typeof obj === 'object' && obj !== null) {
            const newObj = {};
            for (const [key, value] of Object.entries(obj)) {
                newObj[key] = processObject(value);
            }
            return newObj;
        } else {
            return processValue(obj);
        }
    };
    
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    
                    const updatedJson = processObject(json);
                    if (file === '李-880题数二_高等数学 第五章 二重积分_50-6_81412.json') {
                        console.log(`处理文件: ${file}`, updatedJson);
                    }
                    
                    fs.writeFileSync(filePath, JSON.stringify(updatedJson, null, 2), 'utf8');
                    console.log(`已处理转义字符替换: ${file}`);
                } catch (err) {
                    console.error(`处理失败: ${file}`, err);
                }
            }
        });
    });
};

// 替换JSON中所有值的 \bold 为 \boldsymbol（不误伤已有的 \boldsymbol）
const replaceBoldSymbols = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    
    const processValue = (value) => {
        if (typeof value !== 'string') return value;
        
        // 使用负向先行断言，确保 \bold 后面不是 symbol
        // 这样可以避免误伤 \boldsymbol
        return value.replace(/\\bold(?!symbol)/g, '\\boldsymbol');
    };
    
    const processObject = (obj) => {
        if (Array.isArray(obj)) {
            return obj.map(item => processObject(item));
        } else if (typeof obj === 'object' && obj !== null) {
            const newObj = {};
            for (const [key, value] of Object.entries(obj)) {
                newObj[key] = processObject(value);
            }
            return newObj;
        } else {
            return processValue(obj);
        }
    };
    
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    
                    const updatedJson = processObject(json);
                    
                    fs.writeFileSync(filePath, JSON.stringify(updatedJson, null, 2), 'utf8');
                    console.log(`已处理bold替换: ${file}`);
                } catch (err) {
                    console.error(`处理失败: ${file}`, err);
                }
            }
        });
    });
};

// 处理题目编辑和新建的逻辑
const processQuestionEditOrCreate = async (customDirPath = null) => {
    const dirPath = customDirPath || '/Users/<USER>/Downloads/book_01/html_vis';

    // 你的JWT认证令牌，请替换为实际的令牌
    const authToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM2ODg0OTksIm5iZiI6MTc1MzY4ODQ5OSwiZXhwIjoxNzUzNzc0ODk5LCJkYXRhIjp7InVzZXJfaWQiOjIxLCJ1c2VyX25hbWUiOiJzaGl0aWx1cnUifX0.eqNfsxs9iN0khPdFsvptWkbOjCzuJ1WitozdDy6f6yY";

    // 搜索题目的函数
    const searchQuestion = async (questionNo) => {
        try {
            const response = await axios.get(
                `https://api.dc.zentaotech.com/adminapi/Question/list?page=1&size=10&question_status=0&subject_id=1&question_no=${encodeURIComponent(questionNo)}`,
                {
                    headers: {
                        "authorization": authToken,
                        "client": "admin_login_scene:",
                        "accept": "application/json, text/plain, */*"
                    }
                }
            );

            if (response.data && response.data.code === 200) {
                return response.data.data;
            } else {
                throw new Error(`搜索失败: ${JSON.stringify(response.data)}`);
            }
        } catch (error) {
            console.error(`搜索题目失败 ${questionNo}:`, error.message);
            return null;
        }
    };

    // 处理单个JSON文件
    const processJsonFile = async (filePath) => {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const json = JSON.parse(content);
            const questionNo = json['编号'];

            if (!questionNo) {
                console.log(`跳过文件 ${filePath}: 没有编号字段`);
                return;
            }

            console.log(`处理题目编号: ${questionNo}`);

            // 1. 搜索相关的 question_id
            const searchResult = await searchQuestion(questionNo);

            if (!searchResult) {
                console.log(`搜索失败，跳过: ${questionNo}`);
                return;
            }

            const dataList = searchResult.data || [];
            const totalCount = dataList.length;

            let targetUrl = '';

            if (totalCount === 0) {
                // 2. 如果数量是0，直接新建
                targetUrl = 'https://demo.dc.zentaotech.com/question/add_question';
                console.log(`题目不存在，需要新建: ${questionNo} -> ${targetUrl}`);
            } else if (totalCount === 1) {
                // 3. 如果数量是1，找第一个的 question_id
                const questionId = dataList[0].question_id;
                targetUrl = `https://demo.dc.zentaotech.com/question/edit?question_id=${questionId}`;
                console.log(`找到唯一题目，编辑: ${questionNo} -> ${targetUrl}`);
            } else {
                // 4. 如果不是1，找question_no和json['编号']相同的
                const exactMatch = dataList.find(item => item.question_no === questionNo);
                if (exactMatch) {
                    const questionId = exactMatch.question_id;
                    targetUrl = `https://demo.dc.zentaotech.com/question/edit?question_id=${questionId}`;
                    console.log(`找到精确匹配题目，编辑: ${questionNo} -> ${targetUrl}`);
                } else {
                    console.log(`找到多个题目但无精确匹配，需要手动处理: ${questionNo}`);
                    console.log(`搜索结果:`, dataList.map(item => ({
                        question_id: item.question_id,
                        question_no: item.question_no
                    })));
                    return;
                }
            }

            // 将目标URL写入到JSON文件中（可选）
            json['_target_url'] = targetUrl;
            fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');

            console.log(`已处理: ${path.basename(filePath)} -> ${targetUrl}`);

        } catch (error) {
            console.error(`处理文件失败 ${filePath}:`, error.message);
        }
    };

    // 获取所有JSON文件并处理
    const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.json'));

    console.log(`开始处理 ${files.length} 个JSON文件...`);

    for (const file of files) {
        const filePath = path.join(dirPath, file);
        await processJsonFile(filePath);

        // 添加延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('所有文件处理完成！');
};

// 检查数学公式是否可以正确渲染
const checkMathFormulas = async () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';

    // 初始化 MathJax - 使用简化但正确的配置方式
    mathjax.config({
        displayMessages: false,
        displayErrors: true,
        undefinedCharError: false,
        MathJax: {
            tex2jax: {
                inlineMath: [['$', '$']],
                displayMath: [['$$', '$$']],
                processEscapes: true
            }
        }
    });

    // 确保 MathJax 完全初始化
    await new Promise((resolve) => {
        mathjax.start();
        // 给更多时间让 MathJax 完全初始化
        setTimeout(resolve, 1000);
    });
    
    // 提取数学公式的函数
    const extractMathFormulas = (text) => {
        if (typeof text !== 'string') return [];
        
        const formulas = [];
        
        // 匹配 $...$ 格式的公式
        const inlineMatches = text.match(/\$[^$]+\$/g);
        if (inlineMatches) {
            formulas.push(...inlineMatches.map(match => ({
                formula: match,
                type: 'inline',
                math: match.slice(1, -1) // 去掉 $ 符号
            })));
        }
        
        // 匹配 $$...$$ 格式的公式
        const displayMatches = text.match(/\$\$[^$]+\$\$/g);
        if (displayMatches) {
            formulas.push(...displayMatches.map(match => ({
                formula: match,
                type: 'display',
                math: match.slice(2, -2) // 去掉 $$ 符号
            })));
        }
        
        return formulas;
    };
    
    // 检查单个公式
    const checkSingleFormula = async (formula) => {
        return new Promise((resolve) => {
            // 直接使用原始公式，不做任何预处理
            const originalMath = formula.math;

            // 如果公式为空，跳过检查
            if (!originalMath || !originalMath.trim()) {
                resolve({
                    ...formula,
                    valid: true,
                    errors: null,
                    skipped: true,
                    reason: 'Empty formula'
                });
                return;
            }
            
            try {
                mathjax.typeset({
                    math: originalMath,
                    format: formula.type === 'inline' ? 'inline-TeX' : 'TeX',
                    svg: true
                }, (data) => {
                    if (data.errors && data.errors.length > 0) {
                        // 只过滤掉真正的字体/字符相关错误，保留语法错误
                        const filteredErrors = data.errors.filter(error => {
                            const errorStr = error.toString();
                            return !(
                                errorStr.includes('Unknown character: U+') &&
                                (errorStr.includes('in MathJax_Main') || errorStr.includes('Chinese'))
                            ) && !(
                                errorStr.includes('font') &&
                                errorStr.includes('not found')
                            );
                        });

                        resolve({
                            ...formula,
                            valid: filteredErrors.length === 0,
                            errors: filteredErrors.length > 0 ? filteredErrors : null,
                            originalMath: originalMath,
                            originalErrors: data.errors // 保留原始错误用于调试
                        });
                    } else {
                        resolve({
                            ...formula,
                            valid: true,
                            errors: null,
                            originalMath: originalMath
                        });
                    }
                });
            } catch (error) {
                resolve({
                    ...formula,
                    valid: false,
                    errors: [error.message],
                    originalMath: originalMath
                });
            }
        });
    };
    
    // 递归处理对象中的所有字符串值
    const processObject = async (obj, path = '') => {
        const results = [];
        
        if (Array.isArray(obj)) {
            for (let i = 0; i < obj.length; i++) {
                const itemResults = await processObject(obj[i], `${path}[${i}]`);
                results.push(...itemResults);
            }
        } else if (typeof obj === 'object' && obj !== null) {
            for (const [key, value] of Object.entries(obj)) {
                const itemResults = await processObject(value, path ? `${path}.${key}` : key);
                results.push(...itemResults);
            }
        } else if (typeof obj === 'string') {
            const formulas = extractMathFormulas(obj);
            for (const formula of formulas) {
                const result = await checkSingleFormula(formula);
                results.push({
                    ...result,
                    fieldPath: path,
                    originalText: obj
                });
            }
        }
        
        return results;
    };
    
    const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.json'));
    const allResults = [];
    
    console.log(`开始检查 ${files.length} 个文件的数学公式...`);
    
    for (const file of files) {
        const filePath = path.join(dirPath, file);
        try {
            console.log(`检查文件: ${file}`);
            const content = fs.readFileSync(filePath, 'utf8');
            const json = JSON.parse(content);
            
            // 检查所有数学公式
            const formulaResults = await processObject(json);
            
            // 统计结果
            const validCount = formulaResults.filter(r => r.valid).length;
            const invalidCount = formulaResults.filter(r => !r.valid).length;
            const skippedCount = formulaResults.filter(r => r.skipped).length;
            
            // 添加检查结果到JSON
            json['_math_check'] = {
                total_formulas: formulaResults.length,
                valid_formulas: validCount,
                invalid_formulas: invalidCount,
                skipped_formulas: skippedCount,
                is_math_valid: invalidCount === 0,
                check_time: new Date().toISOString()
            };
            
            // 如果有无效公式，添加详细错误信息
            if (invalidCount > 0) {
                json['_math_errors'] = formulaResults
                    .filter(r => !r.valid)
                    .map(r => ({
                        field: r.fieldPath,
                        formula: r.formula,
                        math: r.math,
                        cleanedMath: r.cleanedMath,
                        type: r.type,
                        errors: r.errors
                    }));
                
                console.log(`❌ ${file}: 发现 ${invalidCount} 个无效公式 (跳过 ${skippedCount} 个)`);
                formulaResults.filter(r => !r.valid).forEach(r => {
                    console.log(`   - 字段: ${r.fieldPath}`);
                    console.log(`   - 公式: ${r.formula}`);
                    if (r.cleanedMath !== r.math) {
                        console.log(`   - 清理后: ${r.cleanedMath}`);
                    }
                    console.log(`   - 错误: ${r.errors ? r.errors.join(', ') : '无错误信息'}`);
                });
            } else {
                const skippedText = skippedCount > 0 ? ` (跳过 ${skippedCount} 个中文公式)` : '';
                console.log(`✅ ${file}: 所有 ${validCount} 个公式都有效${skippedText}`);
            }
            
            // 保存更新后的JSON文件
            fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
            
            allResults.push({
                file: file,
                total: formulaResults.length,
                valid: validCount,
                invalid: invalidCount,
                skipped: skippedCount,
                errors: formulaResults.filter(r => !r.valid)
            });
            
        } catch (err) {
            console.error(`处理文件失败: ${file}`, err);
        }
    }
    
    // 输出总结
    const totalFiles = allResults.length;
    const totalFormulas = allResults.reduce((sum, r) => sum + r.total, 0);
    const totalValid = allResults.reduce((sum, r) => sum + r.valid, 0);
    const totalInvalid = allResults.reduce((sum, r) => sum + r.invalid, 0);
    const totalSkipped = allResults.reduce((sum, r) => sum + (r.skipped || 0), 0);
    const filesWithErrors = allResults.filter(r => r.invalid > 0).length;
    
    console.log('\n=== 数学公式检查总结 ===');
    console.log(`处理文件数: ${totalFiles}`);
    console.log(`总公式数: ${totalFormulas}`);
    console.log(`有效公式: ${totalValid}`);
    console.log(`无效公式: ${totalInvalid}`);
    console.log(`跳过公式: ${totalSkipped}`);
    console.log(`有错误的文件数: ${filesWithErrors}`);
    
    if (filesWithErrors > 0) {
        console.log('\n=== 有错误的文件列表 ===');
        allResults
            .filter(r => r.invalid > 0)
            .forEach(r => {
                console.log(`${r.file}: ${r.invalid} 个错误`);
            });
    }
};

const main = async () => {
    // cleanDir('/Users/<USER>/Downloads/book_01/html_vis');
    // setSources();
    // setName();
    // processQuestionType();
    // replaceBoldSymbols(); // 替换 \bold 为 \boldsymbol
    // replaceEscapeCharacters(); // 替换转义字符为HTML标签
    // await processImageLinks()
    await checkMathFormulas(); // 检查数学公式
}

main().catch(console.error)