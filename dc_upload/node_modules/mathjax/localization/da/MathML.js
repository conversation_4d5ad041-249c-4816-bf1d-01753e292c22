/*
 *  /MathJax-v2/localization/da/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("da","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"D\u00E5rlig mglyph: %1",BadMglyphFont:"D\u00E5rlig skrifttype: %1",MathPlayer:"MathJax var ikke i stand til at konfigurere MathPlayer.\n\nHvis MathPlayer ikke er installeret, skal du installere det f\u00F8rst.\nEllers, kan dine sikkerhedsindstillinger forhindrer ActiveX\nkontrolelementer i at k\u00F8re. Bruge dine Internetindstillinger under\ni menuen Funktioner og v\u00E6lg fanen Sikkerhed, og tryk derefter p\u00E5\nknappen Brugerdefineret niveau. Kontroller, at indstillingerne for\n'K\u00F8r ActiveX-objekter' og 'bin\u00E6r- og script-opf\u00F8rsel' er aktiveret.\n\nI \u00F8jeblikket vil du se fejlmeddelelser i stedet for\nformateret matematik",CantCreateXMLParser:"MathJax kan ikke oprette en XML-parser til MathML. Kontroller, at\n'Script ActiveX-objekter markeret sikre til scripting' sikkerhed\ner aktiveret (bruge elementet Internetindstillinger i menuen \nv\u00E6rkt\u00F8jer, og v\u00E6lg sikkerhedspanel, tryk derefter p\u00E5 \nBrugerdefineret niveau knappen for at unders\u00F8ge det).\n\nMathML ligninger vil ikke kunne behandles af MathJax",UnknownNodeType:"Ukendt nodetype: %1",UnexpectedTextNode:"Uventet tekst node: %1",ErrorParsingMathML:"Fejl under parsing af MathML",ParsingError:"Fejl under parsing af MathML: %1",MathMLSingleElement:"MathML skal v\u00E6re dannet af en enkelt element",MathMLRootElement:"MathML skal v\u00E6re dannet af et \u003Cmath\u003E element, ikke %1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/da/MathML.js");
