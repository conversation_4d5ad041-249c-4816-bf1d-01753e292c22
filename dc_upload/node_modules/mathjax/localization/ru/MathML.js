/*
 *  /MathJax-v2/localization/ru/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("ru","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"\u041D\u0435\u0432\u0435\u0440\u043D\u044B\u0439 mglyph\u02D0 %1",BadMglyphFont:"\u041F\u043B\u043E\u0445\u043E\u0439 \u0448\u0440\u0438\u0444\u0442: %1",MathPlayer:"MathJax \u043D\u0435\u00A0\u0441\u043C\u043E\u0433 \u043D\u0430\u0441\u0442\u0440\u043E\u0438\u0442\u044C MathPlayer.\n\n\u0415\u0441\u043B\u0438\u00A0MathPlayer \u043D\u0435\u00A0\u0443\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D, \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435. \u0415\u0441\u043B\u0438\u00A0\u0443\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D, \u0432\u0435\u0440\u043E\u044F\u0442\u043D\u043E, \u0412\u0430\u0448\u0438 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0431\u0435\u0437\u043E\u043F\u0430\u0441\u043D\u043E\u0441\u0442\u0438 \u043D\u0435\u00A0\u043F\u043E\u0437\u0432\u043E\u043B\u044F\u044E\u0442 \u0437\u0430\u043F\u0443\u0441\u043A ActiveX. \u0412\u00A0\u043C\u0435\u043D\u044E \u0418\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u044B|\u0421\u0432\u043E\u0439\u0441\u0442\u0432\u0430 \u043E\u0431\u043E\u0437\u0440\u0435\u0432\u0430\u0442\u0435\u043B\u044F \u0432\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0432\u043A\u043B\u0430\u0434\u043A\u0443 \u00AB\u0411\u0435\u0437\u043E\u043F\u0430\u0441\u043D\u043E\u0441\u0442\u044C\u00BB, \u043D\u0430\u0436\u043C\u0438\u0442\u0435 \u043A\u043D\u043E\u043F\u043A\u0443 \u00AB\u0414\u0440\u0443\u0433\u043E\u0439\u2026\u00BB \u0438\u00A0\u0443\u0431\u0435\u0434\u0438\u0442\u0435\u0441\u044C, \u0447\u0442\u043E\u00A0\u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u044B ActiveX \u0438\u00A0\u043F\u043E\u0432\u0435\u0434\u0435\u043D\u0438\u0435 \u0434\u0432\u043E\u0438\u0447\u043D\u043E\u0433\u043E \u043A\u043E\u0434\u0430 \u0438\u00A0\u0441\u0446\u0435\u043D\u0430\u0440\u0438\u0435\u0432 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u044B.\n\n\u0421\u0435\u0439\u0447\u0430\u0441 \u0412\u044B \u0434\u043E\u043B\u0436\u043D\u044B \u0432\u0438\u0434\u0435\u0442\u044C \u0441\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u044F \u043E\u0431\u00A0\u043E\u0448\u0438\u0431\u043A\u0430\u0445, \u0430\u00A0\u043D\u0435\u00A0\u0444\u043E\u0440\u043C\u0443\u043B\u044B.",CantCreateXMLParser:"MathJax \u043D\u0435\u00A0\u0441\u043C\u043E\u0433 \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u043F\u0430\u0440\u0441\u0435\u0440 XML \u0434\u043B\u044F\u00A0MathML. \u0423\u0431\u0435\u0434\u0438\u0442\u0435\u0441\u044C, \u0447\u0442\u043E\u00A0\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u044B ActiveX, \u043E\u0442\u043C\u0435\u0447\u0435\u043D\u043D\u044B\u0435 \u043A\u0430\u043A\u00A0\u0431\u0435\u0437\u043E\u043F\u0430\u0441\u043D\u044B\u0435 \u0434\u043B\u044F\u00A0\u0441\u043A\u0440\u0438\u043F\u0442\u043E\u0432, \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u044B (\u0412\u00A0\u043C\u0435\u043D\u044E \u0418\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442\u044B|\u0421\u0432\u043E\u0439\u0441\u0442\u0432\u0430 \u043E\u0431\u043E\u0437\u0440\u0435\u0432\u0430\u0442\u0435\u043B\u044F \u0432\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0432\u043A\u043B\u0430\u0434\u043A\u0443 \u00AB\u0411\u0435\u0437\u043E\u043F\u0430\u0441\u043D\u043E\u0441\u0442\u044C\u00BB, \u043D\u0430\u0436\u043C\u0438\u0442\u0435 \u043A\u043D\u043E\u043F\u043A\u0443 \u00AB\u0414\u0440\u0443\u0433\u043E\u0439\u2026\u00BB).\n\nMathJax \u043D\u0435\u00A0\u0441\u043C\u043E\u0436\u0435\u0442 \u0432\u044B\u0432\u0435\u0441\u0442\u0438 \u0444\u043E\u0440\u043C\u0443\u043B\u044B MathML.",UnknownNodeType:"\u041D\u0435\u0438\u0437\u043C\u0435\u0441\u0442\u043D\u044B\u0439 \u0442\u044D\u0433 %1",UnexpectedTextNode:"\u0422\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0439 \u0443\u0437\u0435\u043B %1 \u0432\u00A0\u044D\u0442\u043E\u043C \u043C\u0435\u0441\u0442\u0435 \u043D\u0435\u00A0\u043E\u0436\u0438\u0434\u0430\u0435\u0442\u0441\u044F",ErrorParsingMathML:"\u041E\u0448\u0438\u0431\u043A\u0430 \u0440\u0430\u0437\u0431\u043E\u0440\u0430 MathML",ParsingError:"\u041E\u0448\u0438\u0431\u043A\u0430 \u0440\u0430\u0437\u0431\u043E\u0440\u0430 MathML: %1",MathMLSingleElement:"MathML \u0434\u043E\u043B\u0436\u0435\u043D \u0441\u043E\u0434\u0435\u0440\u0436\u0430\u0442\u044C \u0442\u043E\u043B\u044C\u043A\u043E \u043E\u0434\u0438\u043D \u0442\u044D\u0433 \u003Cmath\u003E",MathMLRootElement:"\u041A\u043E\u0440\u043D\u0435\u0432\u044B\u043C \u0443\u0437\u043B\u043E\u043C MathML \u0434\u043E\u043B\u0436\u0435\u043D \u0431\u044B\u0442\u044C \u003Cmath\u003E, \u043D\u0435\u00A0%1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/ru/MathML.js");
