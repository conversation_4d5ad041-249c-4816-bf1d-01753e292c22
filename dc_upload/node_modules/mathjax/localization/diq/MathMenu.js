/*
 *  /MathJax-v2/localization/diq/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("diq","MathMenu",{version:"2.7.9",isLoaded:true,strings:{Show:"Zey tebir\u00EA matematik\u00EAni b\u0131mocne",MathMLcode:"MathML kod",OriginalMathML:"Original MathML",TeXCommands:"Direktif\u00EA TeXi",AsciiMathInput:"Dekerd\u0131\u015F\u00EA AsciiMathML",Original:"Formo oricinal",ErrorMessage:"Mesac\u00EA x\u0131rabi",Annotation:"Not",TeX:"TeX",StarMath:"StarMath",Maple:"Maple",ContentMathML:"Zerreka MathML",OpenMath:"OpenMath",texHints:"MathML' de sopan\u00EA TeX'i b\u0131vin",Settings:"Saz\u00EA Math",ZoomTrigger:"Zoom trigger",Hover:"V\u0131rderiyen",Click:"T\u0131knay\u0131\u015F",DoubleClick:"D\u0131rey-t\u0131knay\u0131\u015F",NoZoom:"Zoom mek",TriggerRequires:"\u0130cab\u00EA Triggeri:",Option:"We\u00E7inegi",Alt:"B\u0131n",Command:"Komuta",Control:"Qontrol",Shift:"Shift",ZoomFactor:"Faktora zoomi",Renderer:"Matematik v\u0131ra\u015Fto\u011F",MenuEvents:"Hedisey\u00EA menuyi",MouseEvents:"Faaliyet\u00EA meriy",MenuAndMouse:"Faaliyet\u00EA meriy u menuy",FontPrefs:"Tercih\u00E9 qelem",ForHTMLCSS:"Qand\u00E9 HTML-CSS:",Auto:"Otomatik",TeXLocal:"TeX (Lokal)",TeXWeb:"TeX (Web)",TeXImage:"TeX (res\u0131m)",STIXLocal:"STIX (Lokal)",STIXWeb:"STIX (Web)",AsanaMathWeb:"Asana Math (Web)",GyrePagellaWeb:"Gyre Pagella (Web)",GyreTermesWeb:"Gyre Termes (Web)",LatinModernWeb:"Latin Modern (Web)",NeoEulerWeb:"Neo Euler (Web)",ContextMenu:"Kontextmen\u00FC",Browser:"Browser",Locale:"Z\u0131wan",LoadLocale:"URL ra bar beno...",About:"Heqd\u00E9 MathJax",Help:"Pe\u015Ftia MathJaxi",localTeXfonts:"Lokal font\u00EA TeXi b\u0131karne",webTeXfonts:"Web font\u00EA TeXi b\u0131karn\u00EA",imagefonts:"Font\u00EA resimi b\u0131karne",localSTIXfonts:"Lokal font\u00EA STIXi b\u0131karne",webSVGfonts:"Web font\u00EA SVGy b\u0131karne",genericfonts:"Generik font\u00EA Unicodi b\u0131karne",wofforotffonts:"Font\u00EA WOFF yana OTF",eotffonts:"EOT fonti",svgfonts:"SVG fonti",NoOriginalForm:"Original form mewcud niyo",Close:"Racn\u00EA",EqSource:"Denklema \u00E7\u0131me ya MathJax'i"}});MathJax.Ajax.loadComplete("[MathJax]/localization/diq/MathMenu.js");
