/*
 *  /MathJax-v2/localization/fi/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("fi","MathMenu",{version:"2.7.9",isLoaded:true,strings:{MathMLcode:"MathML-koodi",OriginalMathML:"Alkuper\u00E4inen MathML",TeXCommands:"TeX-komennot",AsciiMathInput:"AsciiMathML-sy\u00F6te",Original:"Alku<PERSON>\u00E4inen muoto",ErrorMessage:"Vir<PERSON><PERSON><PERSON>",Annotation:"Huomaut<PERSON>",TeX:"TeX",StarMath:"StarMath",Maple:"Maple",ContentMathML:"Content MathML",OpenMath:"OpenMath",texHints:"N\u00E4yt\u00E4 TeX-vihjeet MathML:ss\u00E4",Settings:"Matematiikan asetukset",ZoomTrigger:"Loitonnustapa",Hover:"Kursorin vienti ylle",Click:"Napsautus",DoubleClick:"Kaksoisnapsautus",NoZoom:"Ei loitonnustapaa",TriggerRequires:"Tapa vaatii:",Option:"Option",Alt:"Alt",Command:"Command",Control:"Control",Shift:"Shift",ZoomFactor:"Loitonnuskerroin",MPHandles:"Anna MathPlayerin k\u00E4sitell\u00E4",MenuEvents:"Valikkotapahtumat",MouseEvents:"Hiiritapahtumat",MenuAndMouse:"Hiiri- ja valikkotapahtumat",FontPrefs:"Kirjasinasetukset",ForHTMLCSS:"HTML-CSS:lle:",Auto:"Automaattinen",TeXLocal:"TeX (paikallinen)",TeXWeb:"TeX (verkko)",TeXImage:"TeX (kuva)",STIXLocal:"STIX (paikallinen)",ContextMenu:"Ponnahdusvalikko",Browser:"Selain",Scale:"Skaalaa kaikki matematiikka...",Discoverable:"Korosta kun kursori tuodaan ylle",Locale:"Kieli",LoadLocale:"Lataa osoitteesta...",About:"Tietoja MathJaxista",Help:"MathJaxin ohje",localTeXfonts:"k\u00E4ytt\u00E4en paikallisia TeX-kirjasimia",webTeXfonts:"k\u00E4ytt\u00E4en verkon TeX-kirjasimia",imagefonts:"K\u00E4ytt\u00E4en kuvakirjasimia",localSTIXfonts:"k\u00E4ytt\u00E4en paikallisia STIX-kirjasimia",webSVGfonts:"k\u00E4ytt\u00E4en verkon SVG-kirjasimia",genericfonts:"k\u00E4ytt\u00E4en geneerisi\u00E4 unicode-kirjasimia",wofforotffonts:"woff- tai otf-kirjasimia",eotffonts:"eot-kirjasimia",svgfonts:"svg-kirjasimia",Close:"Sulje"}});MathJax.Ajax.loadComplete("[MathJax]/localization/fi/MathMenu.js");
