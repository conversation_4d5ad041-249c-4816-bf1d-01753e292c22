/*
 *  /MathJax-v2/localization/lt/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("lt","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"Netinkamas matematikos glifas: %1",BadMglyphFont:"Netinkamas \u0161riftas: %1",MathPlayer:"\u201EMathJax\u201C nepavyko nustatyti \u201EMathPlayer\u201C.\n\nJei \u201EMathPlayer\u201C dar ne\u012Fdiegtas, b\u016<PERSON><PERSON> tai padaryti. Kitaip saugumo nuostatos neleis veikti \u201EActiveX\u201C valdikliams. Adresu Priemoni\u0173 meniu \u003E Interneto nuostatos \u003E Saugumo kortel\u0117 paspaud\u0119 \u201ENaudotojo lyg\u012F\u201C, patikrinkite, ar \u012Fjungtos nuostatos \u201EPaleisti ActiveX valdikius\u201C bei \u201EDvejetain\u0117s ir scenarij\u0173 veiklos\u201C.\n\nPrie\u0161ingu atveju vietoj matematikos \u017Eenkl\u0173 bus rodomi klaid\u0173 prane\u0161imai.",CantCreateXMLParser:"\u201EMathJax\u201C nepavyksta sukurti \u201EMathML\u201C XML analizatoriaus. Patikrinkite, ar \u012Fjungta saugumo nuostata \u201EScenarij\u0173 ActiveX valdikliai pa\u017Eym\u0117ti kaip saug\u016Bs scenarijams vykdyti\u201C (Priemoni\u0173 meniu \u003E Interneto nuostatos \u003E Naudotojo lygis).\n\n\u201EMathJax\u201C neapdoros \u201EMathML\u201C lyg\u010Di\u0173.",UnknownNodeType:"Ne\u017Einoma mazgo atmaina: %1",UnexpectedTextNode:"Netik\u0117tas teksto mazgas: %1",ErrorParsingMathML:"Klaida nagrin\u0117jant \u201EMathML\u201C",ParsingError:"Klaida nagrin\u0117jant \u201EMathML\u201C: %1",MathMLSingleElement:"\u201EMathML\u201C formuotinas vienu d\u0117meniu",MathMLRootElement:"\u201EMathML\u201C formuotinas \u003Cmath\u003E d\u0117meniu, o ne %1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/lt/MathML.js");
