/*
 *  /MathJax-v2/localization/it/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("it","MathMenu",{version:"2.7.9",isLoaded:true,strings:{Show:"Mostra formula come",MathMLcode:"Codice MathML",OriginalMathML:"MathML originale",TeXCommands:"Comandi TeX",AsciiMathInput:"Input AsciiMathML",Original:"Modulo originale",ErrorMessage:"Messaggio d'errore",Annotation:"Annotation",TeX:"TeX",StarMath:"StarMath",Maple:"Maple",ContentMathML:"Content MathML",OpenMath:"OpenMath",texHints:"Aggiungi suggerimenti Tex a MathML",Settings:"Impostazioni formule",ZoomTrigger:"Attivazione zoom",Hover:"Sopra",Click:"Click",DoubleClick:"Doppio-Click",NoZoom:"Niente zoom",TriggerRequires:"L'attivazione richiede:",Option:"Option",Alt:"Alt",Command:"Command",Control:"Control",Shift:"Shift",ZoomFactor:"Fattore di zoom",Renderer:"Processore per le formule",MPHandles:"Affida a MathPlayer",MenuEvents:"Eventi menu",MouseEvents:"Eventi mouse",MenuAndMouse:"Eventi mouse e menu",FontPrefs:"Preferenze font",ForHTMLCSS:"Per HTML-CSS:",Auto:"Auto",TeXLocal:"TeX (locale)",TeXWeb:"TeX (web)",TeXImage:"TeX (immagini)",STIXLocal:"STIX (locale)",STIXWeb:"STIX (web)",AsanaMathWeb:"Asana Math (web)",GyrePagellaWeb:"Gyre Pagella (web)",GyreTermesWeb:"Gyre Termes (web)",LatinModernWeb:"Latin Modern (web)",NeoEulerWeb:"Neo Euler (web)",ContextMenu:"Menu contestuale",Browser:"Browser",Scale:"Scala tutte le formule...",Discoverable:"Evidenzia al passaggio",Locale:"Lingua",LoadLocale:"Scarica dall'URL ...",About:"Informazioni su MathJax",Help:"Aiuto di MathJax",localTeXfonts:"usare font TeX locale",webTeXfonts:"usare font Tex dal web",imagefonts:"usare font immagine",localSTIXfonts:"usare font STIX locale",webSVGfonts:"usare font SVG dal web",genericfonts:"usare generici font unicode",wofforotffonts:"font woff oppure otf",eotffonts:"font eot",svgfonts:"font svg",WebkitNativeMMLWarning:"Il tuo browser non sembra supportare MathML nativamente, perci\u00F2 il passaggio ora all'output MathML potrebbe rendere illegibili le formule della pagina.",MSIENativeMMLWarning:"Internet Explorer richiede il plugin MathPlayer per processare output MathML.",OperaNativeMMLWarning:"Il supporto di Opera a MathML \u00E8 limitato, perci\u00F2 passando ora all'output MathML potrebbe succedere che alcune espressioni siano rese in modo scadente.",SafariNativeMMLWarning:"L'implementazione di MathML del tuo browser non comprende tutte le caratteristiche usate da MathJax, perci\u00F2 alcune espressioni potrebbero non essere visualizzate perfettamente.",FirefoxNativeMMLWarning:"L'implementazione di MathML del tuo browser non comprende tutte le caratteristiche usate da MathJax, perci\u00F2 alcune espressioni potrebbero non essere visualizzate perfettamente.",MSIESVGWarning:"SVG non \u00E8 implementato nelle versioni precedenti IE9 oppure quando si sta emulando IE8 o precedenti. Passando all'output SVG le formule non saranno visualizzate correttamente.",LoadURL:"Scaricamento traduzione da questo indirizzo:",BadURL:"L'indirizzo dovrebbe puntare a un file Javascript con una traduzione di MathJax.  I nomi di file Javascript dovrebbero avere estensione '.js'",BadData:"Impossibile scaricare la traduzione da %1",SwitchAnyway:"Passare comunque a questo interprete?\n\n(Premi OK per cambiare, ANNULLA per continuare con la modalit\u00E1 corrente",ScaleMath:"Scala tutte le formule (comparate al testo circostante) del",NonZeroScale:"Il fattore di scala non deve essere zero",PercentScale:"Il fattore di scala deve essere in percentuale (es. 120%%)",IE8warning:"Questo disabiliter\u00E1 il menu di MathJax e la possibilit\u00E1 di zoom, puoi per\u00F2 accedere lo stesso al menu con Alt-Click su una formula.\n\nCambiare davvero le impostazioni di MathPlayer?",IE9warning:"Il menu contestuale di MathJax verr\u00E1 disabilitato, ma puoi sempre premere Alt-Click sopra una formula per accedervi comunque.",NoOriginalForm:"Modulo originale non disponibile",Close:"Chiudi",EqSource:"Codice sorgente formula MathJax",CloseAboutDialog:"Chiudi finestra di informazioni su MathJax",FastPreview:"Anteprima veloce",AssistiveMML:"MathML ausiliario",InTabOrder:"Includi nell'ordine di tabulazione"}});MathJax.Ajax.loadComplete("[MathJax]/localization/it/MathMenu.js");
