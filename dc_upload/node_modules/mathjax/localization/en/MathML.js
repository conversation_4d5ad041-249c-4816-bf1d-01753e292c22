/*
 *  /MathJax-v2/localization/en/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("en","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"Bad mglyph: %1",BadMglyphFont:"Bad font: %1",MathPlayer:"MathJax was not able to set up MathPlayer.\n\nIf MathPlayer is not installed, you need to install it first.\nOtherwise, your security settings may be preventing ActiveX\ncontrols from running. Use the Internet Options item under\nthe Tools menu and select the Security tab, then press the\nCustom Level button. Check that the settings for\n'Run ActiveX Controls', and 'Binary and script behaviors'\nare enabled.\n\nCurrently you will see error messages rather than\ntypeset mathematics",CantCreateXMLParser:"MathJax cannot create an XML parser for MathML. Check that\nthe 'Script ActiveX controls marked safe for scripting' security\nsetting is enabled (use the Internet Options item in the Tools\nmenu, and select the Security panel, then press the Custom Level\nbutton to check this).\n\nMathML equations will not be able to be processed by MathJax",UnknownNodeType:"Unknown node type: %1",UnexpectedTextNode:"Unexpected text node: %1",ErrorParsingMathML:"Error parsing MathML",ParsingError:"Error parsing MathML: %1",MathMLSingleElement:"MathML must be formed by a single element",MathMLRootElement:"MathML must be formed by a \u003Cmath\u003E element, not %1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/en/MathML.js");
