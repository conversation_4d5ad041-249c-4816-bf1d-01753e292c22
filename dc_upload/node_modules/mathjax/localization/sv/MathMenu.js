/*
 *  /MathJax-v2/localization/sv/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("sv","MathMenu",{version:"2.7.9",isLoaded:true,strings:{MathMLcode:"MathML-kod",OriginalMathML:"Ursprunglig MathML",TeXCommands:"TeX-kommandon",AsciiMathInput:"AsciiMathML-indata",Original:"Ursprungligt format",ErrorMessage:"Felmeddelande",TeX:"TeX",StarMath:"StarMath",OpenMath:"OpenMath",Click:"Klick",DoubleClick:"Dubbelklick",NoZoom:"Ingen zoom",Option:"Alternativ",Alt:"Alt",Command:"\u2318 (Cmd)",Control:"Ctrl",Shift:"Skift",MenuEvents:"Menyh\u00E4ndelser",MouseEvents:"Mush\u00E4ndelser",MenuAndMouse:"Mus- och menyh\u00E4ndelser",FontPrefs:"Teckensnittsinst\u00E4llningar",ForHTMLCSS:"F\u00F6r HTML-CSS:",Auto:"Auto",TeXLocal:"TeX (lokal)",TeXWeb:"TeX (webb)",TeXImage:"TeX (bild)",STIXLocal:"STIX (lokal)",STIXWeb:"STIX (webb)",AsanaMathWeb:"Asana Math (webb)",GyrePagellaWeb:"Gyre Pagella (webb)",GyreTermesWeb:"Gyre Termes (webb)",LatinModernWeb:"Latin Modern (webb)",NeoEulerWeb:"Neo Euler (webb)",Browser:"Webbl\u00E4sare",Discoverable:"Markera vid hovring",Locale:"Spr\u00E5k",LoadLocale:"L\u00E4s in fr\u00E5n URL ...",About:"Om MathJax",Help:"MathJax-hj\u00E4lp",eotffonts:"EOT-teckensnitt",svgfonts:"SVG-teckensnitt",MSIENativeMMLWarning:"Internet Explorer kr\u00E4ver insticksmodulen MathPlayer f\u00F6r att kunna bearbeta MathML-utdata.",NonZeroScale:"Skalan b\u00F6r inte vara noll",PercentScale:"Skalan b\u00F6r vara en procentsats (t.ex. 120%%)",Close:"St\u00E4ng",Show:"Visa matematik som",Annotation:"Anm\u00E4rkning",Maple:"Maple",ContentMathML:"Content MathML",texHints:"Visa TeX tips i MathML",Settings:"Matematikinst\u00E4llningar",ZoomTrigger:"Zoomutl\u00F6sare",Hover:"Hovra",TriggerRequires:"Utl\u00F6sare kr\u00E4ver:",ZoomFactor:"Zoom-faktor",Renderer:"Matematik-renderare",MPHandles:"L\u00E5t MathPlayer hantera:",ContextMenu:"Kontextuell meny",Scale:"Skala all matematik...",localTeXfonts:"anv\u00E4nder lokala TeX-typsnitt",webTeXfonts:"anv\u00E4nder webb-TeX-typsnitt",imagefonts:"anv\u00E4nder bild-typsnitt",localSTIXfonts:"anv\u00E4nder lokala STIX-typsnitt",webSVGfonts:"anv\u00E4nder webb-SVG-typsnitt",genericfonts:"anv\u00E4nder generiska Unicode-typsnitt",wofforotffonts:"WOFF- eller OTF-typsnitt",WebkitNativeMMLWarning:"Din webbl\u00E4sare verkar sakna inbyggt st\u00F6d f\u00F6r MathML, att byta till MathML-utdata kan d\u00E4rf\u00F6r g\u00F6ra att matematiken p\u00E5 sidan blir ol\u00E4sbar",OperaNativeMMLWarning:"Operas st\u00F6d f\u00F6r MathML \u00E4r begr\u00E4nsad, att byta till MathML-utdata kan d\u00E4rf\u00F6r g\u00F6ra att vissa uttryck \u00E5terges d\u00E5ligt.",SafariNativeMMLWarning:"Din webbl\u00E4sares inbyggda MathML implementerar inte alla de funktioner som anv\u00E4nds av MathJax, vissa uttryck kan d\u00E4rf\u00F6r \u00E5terges inkorrekt.",FirefoxNativeMMLWarning:"Din webbl\u00E4sares inbyggda MathML implementerar inte alla de funktioner som anv\u00E4nds av MathJax, vissa uttryck kan d\u00E4rf\u00F6r \u00E5terges inkorrekt.",MSIESVGWarning:"SVG implementeras inte i Internet Explorer f\u00F6re IE9 eller n\u00E4r den emulerar IE8 eller l\u00E4gre. Ett byte till SVG-utdata kommer att g\u00F6ra att matematiken inte visas korrekt.",LoadURL:"Ladda \u00F6vers\u00E4ttningsdata fr\u00E5n denna URL:",BadURL:'URL-adressen b\u00F6r g\u00E5 till en JavaScript-fil som definierar MathJax-\u00F6vers\u00E4ttningsdata. JavaScript-filnamn b\u00F6r avslutas med ".js"',BadData:"Misslyckades med att ladda \u00F6vers\u00E4ttningsdata fr\u00E5n %1",SwitchAnyway:"Byt renderare \u00E4nd\u00E5?\n\n(Tryck p\u00E5 OK f\u00F6r att byta, AVBRYT f\u00F6r att forts\u00E4tta med nuvarande renderare)",ScaleMath:"Skala alla matematik (j\u00E4mf\u00F6rt med omgivande text) med",IE8warning:"Detta kommer att inaktivera MathJax-menyn och zoom-funktionen, men du kan Alt-klicka p\u00E5 ett uttryck att f\u00E5 MathJax-menyn ist\u00E4llet.\n\nVill du verkligen \u00E4ndra MathPlayer-inst\u00E4llningarna?",IE9warning:"MathJax kontextuella meny kommer att vara inaktiverade, men du kan Alt-Klicka p\u00E5 ett uttryck f\u00F6r att f\u00E5 MathJax-menyn ist\u00E4llet.",NoOriginalForm:"Inget ursprungligt format tillg\u00E4ngligt",EqSource:"MathJax ekvationsk\u00E4lla",CloseAboutDialog:'St\u00E4ng dialogen "Om MathJax"',FastPreview:"Snabb f\u00F6rhandsvisning",AssistiveMML:"Assisterande MathML",InTabOrder:"Inkludera i flikordning"}});MathJax.Ajax.loadComplete("[MathJax]/localization/sv/MathMenu.js");
