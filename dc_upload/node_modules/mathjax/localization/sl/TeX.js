/*
 *  /MathJax-v2/localization/sl/TeX.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("sl","TeX",{version:"2.7.9",isLoaded:true,strings:{ExtraOpenMissingClose:"Dodaten uklepaj ali manjkajo\u010D zaklepaj",ExtraCloseMissingOpen:"Dodaten zaklepaj ali manjkajo\u010D uklepaj",MissingLeftExtraRight:"Manjka \\left ali dodatni \\right",MissingScript:"Manjka nadpisani ali podpisani argument",ExtraLeftMissingRight:"Dodaten \\left ali manjkajo\u010D \\right",Misplaced:"Narobe postavljen %1",MissingOpenForSub:"Manjka uklepaj za nadpisano",MissingOpenForSup:"Manjka uklepaj za nadpisano",AmbiguousUseOf:"Dvoumna uporaba %1",EnvBadEnd:"\\begin{%1} se je kon\u010Dal z \\end{%2}",EnvMissingEnd:"Manjka \\end{%1}",MissingBoxFor:"Manjka polje za %1",MissingCloseBrace:"Manjka zaklepaj",UndefinedControlSequence:"Nedefinirano kontrolno zaporedje %1",DoubleExponent:"Dvojni eksponent: pojasnite z oklepaji",DoubleSubscripts:"Dvojno podpisano: za pojasnitev uporabite oklepaje",DoubleExponentPrime:"Pra\u0161tevilo povzro\u010Da dvojni eksponent: pojasnite z oklepaji",CantUseHash1:"Znak # je makroparameter in ga v matemati\u010Dnem na\u010Dinu ne morete uporabljati",MisplacedMiddle:"%1 mora biti znotraj \\left in \\right",MisplacedLimits:"%1 je dovoljen samo pri operatorjih",MisplacedMoveRoot:"%1 se lahko pojavi samo v korenu",MultipleCommand:"Ve\u010Dkratni %1",IntegerArg:"Argument za %1 mora biti celo \u0161tevilo",NotMathMLToken:"%1 ni \u017Eetonski element",InvalidMathMLAttr:"Neveljaven atribut MathML: %1",UnknownAttrForElement:"%1 ni prepoznani atribut za %2",MaxMacroSub1:"Prese\u017Eeno najve\u010Dje \u0161tevilo makrosubstitucij MathJax; ali obstaja rekurzivni makroklic?",MaxMacroSub2:"Prese\u017Eeno najve\u010Dje \u0161tevilo substitucij MathJax; ali obstaja rekurzivno okolje LaTeX?",MissingArgFor:"Manjka argument za %1",ExtraAlignTab:"Dodatni zavihek za poravnavo v tekstu \\cases",BracketMustBeDimension:"Argument v oklepaju za %1 mora biti velikost",InvalidEnv:"Neveljavno ime okolja '%1'",UnknownEnv:"Neznano okolje '%1'",ExtraCloseLooking:"Dodatni zaklepaj pri iskanju %1",MissingCloseBracket:"Za argument za %1 ni mogo\u010De najti zaklepaja ']'",MissingOrUnrecognizedDelim:"Manjkajo\u010De ali neprepoznano lo\u010Dilo za %1",MissingDimOrUnits:"Manjka velikost ali enote pri %1",TokenNotFoundForCommand:"%1 za %2 ni bilo mogo\u010De najti",MathNotTerminated:"Matematika v tekstovnem polju ni prekinjena",IllegalMacroParam:"Nedovoljen makroparameter",MaxBufferSize:"Prese\u017Eena velikost notranjega medpomnilnika MathJax; ali obstaja rekurzivni makroklic?",CommandNotAllowedInEnv:"%1 v okolju %2 ni dovoljen",MultipleLabel:"Ve\u010Dkratno definirana oznaka '%1'",CommandAtTheBeginingOfLine:"%1 mora priti na za\u010Detek vrstice",IllegalAlign:"V %1 je dolo\u010Dena nedovoljena poravnava.",BadMathStyleFor:"Neveljaven matemati\u010Dni slog argumenta %1",PositiveIntegerArg:"Argument za %1 mora biti pozitivno celo \u0161tevilo",ErroneousNestingEq:"Napa\u010Dno gnezdenje struktur ena\u010Dbe",MultlineRowsOneCol:"Vrstice v okolju %1 morajo imeti natan\u010Dno en stolpec",MultipleBBoxProperty:"%1 je v %2 dolo\u010Den dvakrat",InvalidBBoxProperty:"'%1' ne izgleda kot barva, velikost odmika ali slog",ExtraEndMissingBegin:"Dodatni %1 ali manjkajo\u010D \\begingroup",GlobalNotFollowedBy:"%1 ne sledijo \\let, \\def ali \\newcommand",UndefinedColorModel:"Nedefiniran barvni model '%1'",ModelArg1:"Barvne vrednosti modela %1 zahtevajo tri \u0161tevilke",InvalidDecimalNumber:"Neveljavna decimalna \u0161tevilka",ModelArg2:"Barvne vrednosti za model %1 morajo biti med %2 in %3",InvalidNumber:"Neveljavna \u0161tevilka",NewextarrowArg1:"Prvi argument za %1 mora biti ime kontrolnega zaporedja",NewextarrowArg2:"Drugi argument za %1 morata biti dve celi \u0161tevili, lo\u010Deni z vejico",NewextarrowArg3:"Tretji argument za %1 mora biti \u0161tevilka unikodnega znaka",NoClosingChar:"Ni mogo\u010De najti zapiralnega %1",IllegalControlSequenceName:"Nedovoljeno ime kontrolnega zaporedja za %1",IllegalParamNumber:"Neveljavno \u0161tevilo parametrov v %1",MissingCS:"%1 mora slediti kontrolno zaporedje",CantUseHash2:"Nedovoljena uporaba znaka # v predlogi za %1",SequentialParam:"Parametri za %1 morajo biti o\u0161tevil\u010Deni zaporedno",MissingReplacementString:"Manjka nadomestni niz za definicijo %1",MismatchUseDef:"Uporaba %1 se ne ujema z njegovo definicijo",RunawayArgument:"Ube\u017Eni argument za %1?",NoClosingDelim:"Ni mogo\u010De najti zaklju\u010Dnega lo\u010Dila za %1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/sl/TeX.js");
