/*
 *  /MathJax-v2/localization/fr/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("fr","MathMenu",{version:"2.7.9",isLoaded:true,strings:{Show:"Afficher sous forme",MathMLcode:"Code MathML",OriginalMathML:"MathML d\u2019origine",TeXCommands:"Commandes TeX",AsciiMathInput:"Entr\u00E9e AsciiMathML",Original:"Format d\u2019origine",ErrorMessage:"Message d\u2019erreur",Annotation:"Annotation",TeX:"TeX",StarMath:"StarMath",Maple:"Maple",ContentMathML:"MathML de contenu",OpenMath:"OpenMath",texHints:"Afficher les aides TeX en MathML",Settings:"Param\u00E9trages des maths",ZoomTrigger:"D\u00E9clencheur de zoom",Hover:"Survol",Click:"Clic",DoubleClick:"Double-clic",NoZoom:"Pas de zoom",TriggerRequires:"Le d\u00E9clencheur n\u00E9cessite :",Option:"Option",Alt:"Alt",Command:"Commande",Control:"Contr\u00F4le",Shift:"Maj",ZoomFactor:"Facteur de grossissement d'\u00E9chelle",Renderer:"Outil de rendu math\u00E9matique",MPHandles:"Laisser MathPlayer g\u00E9rer :",MenuEvents:"\u00C9v\u00E9nements de menu",MouseEvents:"\u00C9v\u00E9nements de souris",MenuAndMouse:"\u00C9v\u00E9nements de souris et de menu",FontPrefs:"Pr\u00E9f\u00E9rences de police",ForHTMLCSS:"Pour HTML-CSS :",Auto:"Auto",TeXLocal:"TeX (local)",TeXWeb:"TeX (web)",TeXImage:"TeX (image)",STIXLocal:"STIX (local)",STIXWeb:"STIX (web)",AsanaMathWeb:"Asana Math (web)",GyrePagellaWeb:"Gyre Pagella (web)",GyreTermesWeb:"Gyre Termes (web)",LatinModernWeb:"Latin Modern (web)",NeoEulerWeb:"Neo Euler (web)",ContextMenu:"Menu contextuel",Browser:"Navigateur",Scale:"Mettre tous les maths \u00E0 l\u2019\u00E9chelle\u2026",Discoverable:"Surligner au survol",Locale:"Langue",LoadLocale:"Charger depuis l\u2019URL\u2026",About:"\u00C0 propos de MathJax",Help:"Aide de MathJax",localTeXfonts:"utiliser les polices TeX locales",webTeXfonts:"utiliser les polices TeX du web",imagefonts:"utiliser les polices Image",localSTIXfonts:"utiliser les polices STIX locales",webSVGfonts:"utiliser les polices SVG  du web",genericfonts:"utiliser les polices Unicode g\u00E9n\u00E9riques",wofforotffonts:"polices WOFF ou OTF",eotffonts:"polices EOT",svgfonts:"polices SVG",WebkitNativeMMLWarning:"Votre navigateur ne semble pas prendre en charge MathML en natif, donc basculer sur MathML en sortie pourrait rendre les math\u00E9matiques illisibles sur cette page.",MSIENativeMMLWarning:"Internet Explorer n\u00E9cessite le module MathPlayer pour traiter les sorties MathML.",OperaNativeMMLWarning:"La prise en charge de MathML par Opera est limit\u00E9e, donc passer sur du MathML en sortie pourrait rendre certaines expressions peu lisibles.",SafariNativeMMLWarning:"Le MathML natif de votre navigateur n\u2019impl\u00E9mente pas toutes les fonctionnalit\u00E9s utilis\u00E9s par MathJax, donc certaines expressions pourraient ne pas \u00EAtre affich\u00E9es correctement.",FirefoxNativeMMLWarning:"Le MathML natif de votre navigateur n\u2019impl\u00E9mente pas toutes les fonctionnalit\u00E9s de MathJax, donc certaines expressions pourraient \u00EAtre mal affich\u00E9es.",MSIESVGWarning:"SVG n\u2019est pas pris en charge par Internet Explorer avant la version 9 (ni dans l\u2019\u00E9mulation de IE8 ou inf\u00E9rieur, par cons\u00E9quent). Basculer sur SVG en sortie pourrait faire que les math\u00E9matiques s\u2019affichent mal.",LoadURL:"Charger les donn\u00E9es de traduction depuis cette URL :",BadURL:"L\u2019URL devrait \u00EAtre un fichier JavaScript qui d\u00E9finit les donn\u00E9es de traduction de MathJax. Les noms de fichier JavaScript devraient se terminer par '.js'",BadData:"\u00C9chec du chargement des donn\u00E9es de traduction depuis %1",SwitchAnyway:"Basculer tout de m\u00EAme le rendu\u202F?\n\n(Cliquez OK pour basculer, ANNULER pour continuer avec le rendu actuel)",ScaleMath:"Mettre \u00E0 l\u2019\u00E9chelle toutes les math\u00E9matiques (par rapport au texte environnant) de",NonZeroScale:"L\u2019\u00E9chelle ne devrait pas \u00EAtre z\u00E9ro",PercentScale:"L\u2019\u00E9chelle devrait \u00EAtre un pourcentage (par exemple, 120%%)",IE8warning:"Cela d\u00E9sactivera le menu MathJax et les fonctionnalit\u00E9s de zoom, mais \u00E0 la place, vous pouvez Alt-cliquer sur une expression pour obtenir le menu MathJax.",IE9warning:"Le menu contextuel MathJax sera d\u00E9sactiv\u00E9, mais \u00E0 la place, vous pouvez Alt-cliquer sur une expression pour obtenir le menu MathJax.",NoOriginalForm:"Aucun format d\u2019origine disponible",Close:"Fermer",EqSource:"Source de l\u2019\u00E9quation MathJax",CloseAboutDialog:"Fermer la bo\u00EEte de dialogue \u00C0 propos de MathJax",FastPreview:"Aper\u00E7u rapide",AssistiveMML:"MathML auxiliaire",InTabOrder:"Inclure dans l\u2019ordre des onglets"}});MathJax.Ajax.loadComplete("[MathJax]/localization/fr/MathMenu.js");
