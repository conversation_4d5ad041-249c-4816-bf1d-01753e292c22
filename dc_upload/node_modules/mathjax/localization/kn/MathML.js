/*
 *  /MathJax-v2/localization/kn/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("kn","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"\u0C95\u0CC6\u0C9F\u0CCD\u0C9F mglyph:  %1",BadMglyphFont:"\u0C95\u0CC6\u0C9F\u0CCD\u0C9F \u0CA4\u0CC8\u0CB2\u0CA6\u0CBE\u0CA8\u0CBF:         %1",UnknownNodeType:"\u0C97\u0CCA\u0CA4\u0CCD\u0CA4\u0CC1 \u0C87\u0CB0\u0CB2\u0CC7 \u0C87\u0CA6\u0CCD\u0CA6 \u0CA8\u0CCB\u0CA1\u0CCD \u0C9F\u0CC8\u0CAA\u0CCD: %1",UnexpectedTextNode:"\u0C8E\u0CA6\u0CB0\u0CC1 \u0CA8\u0CCB\u0CA6\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2\u0CA6\u0CCD\u0CA6 \u0CA8\u0CCB\u0CA1\u0CCD \u0C9F\u0CC8\u0CAA\u0CCD   :     %1",ErrorParsingMathML:"\u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0CAA\u0CBE\u0CB0\u0CCD\u0CB8\u0CC6 \u0CAE\u0CBE\u0CA1\u0CC1\u0CB5\u0CBE\u0C97 \u0CA4\u0CCD\u0CB0\u0CC1\u0C9F\u0CBF",ParsingError:"\u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0CAA\u0CBE\u0CB0\u0CCD\u0CB8\u0CC6 \u0CAE\u0CBE\u0CA1\u0CC1\u0CB5\u0CBE\u0C97 \u0CA4\u0CCD\u0CB0\u0CC1\u0C9F\u0CBF: %1",MathMLSingleElement:"\u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C92\u0C82\u0CA6\u0CC1 \u0C8E\u0CB2\u0CBF\u0CAE\u0CC6\u0C82\u0C9F\u0CCD \u0C87\u0C82\u0CA6 \u0CAE\u0CBE\u0CA1 \u0CAC\u0CC6\u0C95\u0CC1.",MathMLRootElement:"\u0CAE\u0CBE\u0CA4\u0CCD \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C92\u0C82\u0CA6\u0CC1  \u003Cmath\u003E \u0C9F\u0CCD\u0CAF\u0CBE\u0C97\u0CCD  \u0C87\u0C82\u0CA6 \u0CB6\u0CC1\u0CB0\u0CC1 \u0C86\u0C97\u0CAC\u0CC7\u0C95\u0CC1, %1    \u0C87\u0C82\u0CA6 \u0C85\u0CB2\u0CCD\u0CB2"}});MathJax.Ajax.loadComplete("[MathJax]/localization/kn/MathML.js");
