/*
 *  /MathJax-v2/localization/br/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("br","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"mglyph fall : %1",BadMglyphFont:"Font fall : %1",UnknownNodeType:"Seurt skoulm dianav : %1",UnexpectedTextNode:"Skoulm testenn dic'hortoz : %1",ErrorParsingMathML:"Fazi o tielfenna\u00F1 MathML",ParsingError:"Fazi o ielfenna\u00F1 MathML : %1",MathMLSingleElement:"MathML a rank beza\u00F1 stummet gant un elfenn hepken",MathMLRootElement:"Gant un elfenn \u003Cmath\u003E e rank beza\u00F1 stummet MathML, ha ket gant %1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/br/MathML.js");
