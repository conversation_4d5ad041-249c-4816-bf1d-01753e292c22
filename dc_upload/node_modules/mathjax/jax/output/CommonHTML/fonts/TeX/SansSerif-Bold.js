/*
 *  /MathJax-v2/jax/output/CommonHTML/fonts/TeX/SansSerif-Bold.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(b){var a="MathJax_SansSerif-Bold";b.FONTDATA.FONTS[a]={className:b.FONTDATA.familyName(a),centerline:250,ascent:750,descent:250,weight:"bold",32:[0,0,250,0,0],33:[694,0,367,110,256],34:[694,-442,558,37,420],35:[694,193,917,61,855],36:[750,56,550,49,488],37:[750,56,1029,61,966],38:[716,22,831,47,769],39:[694,-442,306,80,226],40:[750,249,428,79,366],41:[750,250,428,61,348],42:[750,-293,550,67,482],43:[617,116,856,61,794],44:[146,106,306,80,226],45:[273,-186,367,12,305],46:[146,0,306,80,226],47:[750,249,550,61,488],48:[715,22,550,43,506],49:[716,-1,550,76,473],50:[716,0,550,46,495],51:[716,22,550,46,503],52:[694,0,550,31,518],53:[694,22,550,37,494],54:[716,22,550,46,503],55:[695,11,550,46,503],56:[715,22,550,46,503],57:[716,22,550,46,503],58:[458,0,306,80,226],59:[458,106,306,80,226],61:[407,-94,856,61,794],63:[705,0,519,61,457],64:[704,11,733,61,671],65:[694,0,733,42,690],66:[694,-1,733,92,671],67:[704,11,703,61,647],68:[694,-1,794,92,732],69:[691,0,642,92,595],70:[691,0,611,92,564],71:[705,11,733,61,659],72:[694,0,794,92,702],73:[694,0,331,85,246],74:[694,22,519,46,427],75:[694,0,764,92,701],76:[694,0,581,92,534],77:[694,0,978,92,886],78:[694,0,794,92,702],79:[716,22,794,62,731],80:[694,0,703,92,641],81:[716,106,794,62,732],82:[694,0,703,92,654],83:[716,22,611,49,549],84:[688,0,733,40,692],85:[694,22,764,92,672],86:[694,-1,733,27,705],87:[694,0,1039,24,1014],88:[694,0,733,37,694],89:[694,0,733,24,708],90:[694,0,672,61,616],91:[750,250,343,79,318],93:[750,250,343,24,263],94:[694,-537,550,108,441],95:[-23,110,550,0,549],97:[475,11,525,31,472],98:[694,10,561,54,523],99:[475,11,489,37,457],100:[694,11,561,37,507],101:[474,10,511,30,480],102:[705,0,336,29,381],103:[469,206,550,17,534],104:[694,0,561,53,508],105:[695,0,256,46,208],106:[695,205,286,-71,232],107:[694,0,531,63,496],108:[694,0,256,54,201],109:[469,0,867,53,815],110:[468,0,561,53,508],111:[474,11,550,32,518],112:[469,194,561,54,523],113:[469,194,561,37,507],114:[469,0,372,54,356],115:[474,10,422,30,396],116:[589,10,404,20,373],117:[458,11,561,52,508],118:[458,0,500,26,473],119:[458,0,744,24,719],120:[458,0,500,24,475],121:[458,205,500,29,473],122:[458,0,476,31,442],126:[344,-198,550,92,457],160:[0,0,250,0,0],305:[458,0,256,54,201],567:[458,205,286,-71,232],768:[694,-537,0,-458,-218],769:[694,-537,0,-334,-93],770:[694,-537,0,-442,-109],771:[694,-548,0,-458,-93],772:[660,-560,0,-474,-77],774:[694,-552,0,-470,-80],775:[695,-596,0,-356,-194],776:[695,-595,0,-459,-91],778:[694,-538,0,-365,-119],779:[694,-537,0,-440,-94],780:[657,-500,0,-442,-109],915:[691,0,581,92,534],916:[694,0,917,60,856],920:[716,22,856,62,793],923:[694,0,672,41,630],926:[688,0,733,46,686],928:[691,0,794,92,702],931:[694,0,794,61,732],933:[715,0,856,62,793],934:[694,0,794,62,732],936:[694,0,856,61,794],937:[716,0,794,49,744],8211:[327,-240,550,0,549],8212:[327,-240,1100,0,1099],8216:[694,-443,306,81,226],8217:[694,-442,306,80,226],8220:[694,-443,558,138,520],8221:[694,-442,558,37,420]};b.fontLoaded("TeX/"+a.substr(8))})(MathJax.OutputJax.CommonHTML);
