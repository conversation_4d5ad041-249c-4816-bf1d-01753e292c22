/*
 *  /MathJax-v2/jax/output/SVG/fonts/TeX/Main/Bold/SuppMathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Main-bold"],{10815:[686,0,900,39,860,"39 655Q39 675 43 680T69 686Q110 684 225 684Q267 684 303 684T360 685T385 686Q401 686 405 680T409 651Q409 632 403 628T367 624H348H301V62H598V624H551H532Q502 624 496 628T490 651Q490 673 494 679T514 686Q518 686 558 685T675 684T792 685T836 686Q852 686 856 680T860 651Q860 632 854 628T818 624H799H752V62H799H809Q846 62 853 59T860 36V31V21Q860 6 850 2Q846 0 450 0H156Q75 0 60 1T40 11V18Q39 26 39 31Q39 54 44 58T82 63Q84 63 90 63T100 62H147V624H100H90Q53 624 46 627T39 650V655"],10927:[696,199,894,96,797,"796 54Q796 40 788 32T767 24Q741 24 735 57Q729 107 705 148T646 216T563 264T465 297T356 316T245 326T136 330H134Q96 330 96 360Q96 391 134 391H136Q193 392 239 394T347 403T457 421T556 453T643 502T703 571T735 664Q741 696 763 696Q781 696 789 686T797 667Q797 651 792 624T758 545T682 456Q605 396 481 365L462 360Q483 357 526 344T633 296T733 215Q767 173 781 128T796 54ZM119 -199Q96 -190 96 -169T116 -141Q121 -139 448 -139H775Q776 -140 779 -142T785 -146T791 -151T795 -158T797 -169Q797 -190 775 -199H119"],10928:[697,199,894,96,797,"127 24Q115 24 106 32T97 55Q97 95 124 156T211 265Q288 325 412 356L431 361Q410 363 367 376T259 425T160 506Q127 546 112 594T96 667Q96 679 104 688T126 697Q152 697 158 664Q164 614 188 573T247 505T331 456T429 424T539 405T649 395T758 391Q797 391 797 360Q797 330 761 330H758Q701 329 655 327T547 318T437 300T337 268T251 219T190 150T158 57Q151 24 127 24ZM119 -199Q96 -190 96 -169T116 -141Q121 -139 448 -139H775Q776 -140 779 -142T785 -146T791 -151T795 -158T797 -169Q797 -190 775 -199H119"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Main/Bold/SuppMathOperators.js");
