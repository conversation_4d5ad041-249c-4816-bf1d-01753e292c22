/*
 *  /MathJax-v2/jax/output/SVG/fonts/TeX/Fraktur/Bold/Other.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_Fraktur-bold"],{8216:[708,-411,254,53,187,"187 456Q187 437 169 424T138 411Q114 411 84 454T53 538Q53 565 75 597Q109 648 155 697L166 708L181 694L173 681Q124 610 124 577Q124 549 155 511T187 456"],8217:[692,-394,254,58,193,"125 524Q125 545 92 588T58 651Q58 661 61 667Q65 674 80 683T107 692Q131 692 162 645T193 564Q193 540 176 509T144 460T87 394L78 400L68 406L79 421Q125 489 125 524"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Fraktur/Bold/Other.js");
