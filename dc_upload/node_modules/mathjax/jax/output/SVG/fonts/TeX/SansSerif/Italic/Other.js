/*
 *  /MathJax-v2/jax/output/SVG/fonts/TeX/SansSerif/Italic/Other.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS["MathJax_SansSerif-italic"],{305:[444,0,239,74,258,"168 442T168 443T213 444T258 443T212 225T164 2V0H74V2Q75 7 121 224"],567:[444,204,267,-97,286,"-54 -96L-48 -104Q-41 -111 -27 -118T7 -126Q60 -126 82 -87Q85 -81 140 181L196 443Q196 444 241 444T286 443Q286 441 232 186T175 -75Q163 -120 122 -162T19 -204Q-13 -204 -41 -196T-83 -180T-96 -170Q-55 -96 -54 -96"],915:[691,0,542,87,646,"87 2Q88 4 160 346T234 689Q234 691 440 691T646 689Q643 686 629 611H475L321 612Q193 4 191 2V0H87V2"],916:[694,0,833,42,790,"273 343L510 694H617Q790 2 790 0H416L42 1L273 343ZM539 576Q536 597 536 600Q536 602 535 605Q534 607 534 607Q527 580 222 130L201 98H651L648 110Q645 123 639 149T627 198Q554 489 539 576"],920:[715,22,778,119,804,"119 260Q119 348 157 433T254 579T387 677T533 715Q701 715 772 574Q804 511 804 431Q804 315 744 209T586 41T384 -22Q262 -22 191 59T119 260ZM706 426Q706 524 655 582T525 640Q454 640 395 600T293 502Q256 447 237 383T218 266Q218 168 269 112T401 55Q518 55 612 166T706 426ZM283 349L293 397H473Q652 397 652 396Q647 374 642 347L632 299H452Q273 299 273 300Q278 322 283 349"],923:[694,0,611,28,582,"28 0L401 694H504V690Q505 686 543 345T582 1Q582 0 528 0H473V3Q472 6 460 113T435 359T422 558Q422 593 424 603L425 610L424 608Q414 572 343 431Q287 316 143 49L117 1L73 0H28"],926:[688,0,667,42,765,"193 687Q193 688 479 688H765V686Q764 685 755 642L747 600H461L175 601Q175 602 184 645L193 687ZM196 400Q196 401 418 401T640 400L622 315Q622 314 400 314T178 315L196 400ZM42 2Q43 3 51 44T60 87H64Q68 87 75 87T93 87T119 87T151 88T190 88T237 88T291 88T352 88H643Q638 66 634 44T627 13T624 2V0H42V2"],928:[691,0,708,86,768,"86 2Q88 4 160 346T233 689Q233 691 501 691Q768 691 768 689Q766 688 694 346T621 2V0H517V2Q518 3 582 304T646 609L648 615H321L190 0H86V2"],931:[694,0,722,55,813,"194 655L202 694H508Q813 694 813 693Q809 675 805 653L797 614H559L321 615Q327 606 405 478L485 347Q449 311 348 203T247 86Q247 84 294 84Q303 84 359 84T465 85H684Q684 84 675 42L666 0H360L55 1L195 154Q346 319 347 320L359 333L273 473Q187 614 186 614L187 620Q188 625 190 635T194 655"],933:[716,0,778,173,843,"357 637Q320 637 297 612T266 555H173Q178 576 188 598Q214 651 265 683T373 716Q497 716 497 542V509L504 526Q579 715 711 715Q773 715 808 677T843 589Q843 576 840 555H747L748 557Q748 559 748 563T749 574V580Q749 604 731 622Q715 638 693 638Q591 638 543 465Q531 425 506 309T462 98T441 2V0H337V2Q425 401 436 486Q438 504 438 526Q438 637 364 637H357"],934:[694,0,722,124,743,"124 308Q124 399 208 481T433 587Q437 587 437 589Q438 590 449 643L459 694H508Q557 694 557 693Q557 691 546 641T535 587Q543 587 562 583T614 565T674 531T722 472T743 387Q743 288 656 209T449 110L433 106Q411 3 410 2Q410 0 361 0H312L313 3Q313 5 324 56L335 107H331L321 108Q311 110 297 114T266 124T228 141T190 168Q124 225 124 308ZM227 315Q227 282 239 257T270 218T306 197T338 186L350 184H351L386 346Q420 507 420 509H419Q411 509 393 505T342 485T284 444Q227 387 227 315ZM642 381Q642 413 629 437T599 475T563 496T533 507T519 510Q518 510 484 348T450 184Q544 201 593 258T642 381"],936:[694,0,778,171,854,"325 556Q325 524 310 447T294 330Q294 289 304 260Q314 234 333 216T364 192T380 187L488 694H585V691Q584 689 531 438L478 188H479Q485 188 503 195T555 231T613 305Q637 352 654 435Q662 470 669 496T681 538T690 562T698 578T704 587Q719 609 733 615T772 621H802H854V619L838 546Q838 545 832 545Q775 539 749 418Q716 274 638 196Q616 173 590 156T543 131T503 117T473 110T460 106Q460 105 450 54T438 2V0H340V2Q341 3 351 54T362 106Q363 107 358 108T344 111T322 117T295 128T267 145T239 171T216 207T200 256T194 319Q194 356 203 408T213 483Q213 517 203 530T182 544T171 546Q184 609 187 619V621H239Q286 621 294 620T309 612Q325 596 325 556"],937:[716,0,722,44,769,"148 407Q148 475 182 534T269 633T386 694T511 716Q622 716 695 658T769 507Q769 461 747 409T699 321T628 225T562 136Q533 90 533 86Q542 85 557 85Q564 85 583 85T614 86Q695 86 695 85Q679 9 677 2V0H425Q426 3 433 30T447 72T480 131T549 241L554 248Q558 254 563 261T576 281T592 306T609 335T625 366T640 400T653 434T661 466T664 498Q664 562 618 601T497 640Q416 640 351 596T262 482Q250 441 250 392T276 237T302 70V56Q302 28 296 2V0H44V2L62 85Q62 86 143 86L225 85V88Q224 89 224 89T224 91T224 95T223 101T222 110T220 123T216 140T209 163T200 191T188 227Q148 344 148 407"],8211:[312,-236,500,50,565,"59 275L66 312H316Q565 312 565 311T558 275T549 238Q549 236 299 236Q50 236 50 237L52 242Q53 247 55 257T59 275"],8212:[312,-236,1000,50,1065,"59 275L66 312H566Q1065 312 1065 311T1058 275T1049 238Q1049 236 549 236Q50 236 50 237L52 242Q53 247 55 257T59 275"],8216:[694,-471,278,190,335,"309 567L299 520Q289 474 288 473Q288 471 239 471T190 473L192 480Q193 486 196 499T201 522L211 569L287 694H312L335 693L332 685Q328 677 321 661T307 630L279 570L294 569Q309 569 309 567"],8217:[694,-471,278,190,335,"228 647L237 694H286Q335 694 335 693L334 686Q332 680 330 668T325 644L315 597L239 471H214Q190 471 190 472L246 595Q246 596 231 596H217V598Q217 601 228 647"],8220:[694,-471,500,274,613,"393 567L383 520Q373 474 372 473Q372 471 323 471T274 473L276 480Q277 486 280 499T285 522L295 569L371 694H396L419 693L416 685Q412 677 405 661T391 630L363 570L378 569Q393 569 393 567ZM587 567L577 520Q567 474 566 473Q566 471 517 471T468 473L470 480Q471 486 474 499T479 522L489 569L565 694H590L613 693L610 685Q606 677 599 661T585 630L557 570L572 569Q587 569 587 567"],8221:[694,-471,500,133,472,"171 647L180 694H229Q278 694 278 693L276 686Q275 680 273 668T268 644L258 597L182 471H157Q133 471 133 472L189 595Q189 596 174 596H160V598Q160 601 171 647ZM365 647L374 694H423Q472 694 472 693L470 686Q469 680 467 668T462 644L452 597L376 471H351Q327 471 327 472L383 595Q383 596 368 596H354V598Q354 601 365 647"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/SansSerif/Italic/Other.js");
