/*
 *  /MathJax-v2/jax/output/SVG/fonts/TeX/AMS/Regular/MiscTechnical.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax.SVG.FONTDATA.FONTS.MathJax_AMS,{8994:[378,-122,778,55,722,"77 122Q68 122 63 126T57 135T55 142Q55 151 68 176T111 235T177 302T271 356T388 378Q451 378 508 355T602 300T668 233T709 174T722 142Q722 124 704 122Q692 122 685 134T658 179T606 243Q511 338 390 338Q354 338 320 329Q251 312 193 263T97 141Q87 123 77 122"],8995:[378,-142,778,54,722,"389 143Q324 143 266 164T171 215T107 277T67 330T55 358T60 371T77 378Q85 377 92 367T116 331T158 280Q256 182 389 182Q475 182 552 227T675 351Q688 378 704 378Q722 376 722 358Q722 352 710 330T670 276T605 215T511 164T389 143"]});MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/MiscTechnical.js");
