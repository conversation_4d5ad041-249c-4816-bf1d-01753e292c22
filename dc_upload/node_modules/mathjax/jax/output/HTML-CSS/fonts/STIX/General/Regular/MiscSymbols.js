/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscSymbols.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{9733:[655,66,870,60,810],9734:[655,66,870,60,810],9737:[583,79,762,50,712],9740:[634,131,581,54,553],9742:[676,0,1000,32,967],9746:[662,158,910,45,865],9761:[630,35,619,70,549],9785:[728,82,1150,170,980],9786:[728,82,1150,170,980],9787:[728,82,1150,170,980],9788:[623,122,837,46,791],9789:[728,82,641,40,601],9790:[728,82,641,40,601],9791:[702,198,603,65,538],9792:[638,135,603,65,538],9793:[638,135,603,65,538],9794:[634,131,660,54,620],9795:[732,176,970,66,904],9796:[793,140,970,63,866],9798:[760,110,840,60,780],9799:[730,110,632,76,576],9800:[760,110,964,25,939],9801:[644,139,781,43,738],9828:[609,99,685,34,651],9829:[603,105,685,34,651],9830:[609,105,685,41,643],9831:[603,99,685,34,651],9833:[714,125,390,45,345],9834:[714,125,560,50,510],9835:[842,125,840,40,721],9854:[775,271,1186,70,1116],9856:[669,23,1032,170,862],9857:[669,23,1032,170,862],9858:[669,23,1032,170,862],9859:[669,23,1032,170,862],9860:[669,23,1032,170,862],9861:[669,23,1032,170,862],9862:[687,42,1032,152,881],9863:[687,42,1032,152,881],9864:[687,42,1032,152,881],9865:[687,42,1032,152,881],9888:[1023,155,1510,25,1485],9893:[784,281,660,54,620],9898:[583,79,762,50,712],9899:[583,79,762,50,712],9900:[487,-14,565,46,519],9906:[638,135,603,65,538],9954:[773,80,700,94,606]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/MiscSymbols.js");
