/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/STIX/General/Bold/MiscTechnical.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{8962:[774,0,926,55,871],8968:[731,193,469,164,459],8969:[731,193,469,10,305],8970:[732,193,469,164,459],8971:[732,193,469,10,305],8976:[399,-108,750,65,685],8985:[399,-108,750,65,685],8994:[378,-129,1026,37,990],8995:[378,-129,1026,37,990],9001:[732,193,445,69,399],9002:[732,193,445,46,376],9014:[751,156,926,85,841],9021:[694,190,924,80,844],9023:[732,200,728,55,673],9135:[297,-209,315,0,315]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/MiscTechnical.js");
