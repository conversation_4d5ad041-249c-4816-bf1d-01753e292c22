/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathItalic.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{119860:[667,0,717,35,685],119861:[653,0,696,38,686],119862:[659,12,671,50,711],119863:[653,0,790,38,765],119864:[653,0,714,38,734],119865:[653,0,618,38,723],119866:[668,12,734,50,734],119867:[653,0,873,38,923],119868:[653,0,480,38,530],119869:[653,12,540,60,620],119870:[653,0,762,38,802],119871:[653,0,708,38,668],119872:[653,0,1005,38,1055],119873:[653,0,851,38,901],119874:[669,11,732,50,712],119875:[653,0,594,38,704],119876:[667,152,781,50,731],119877:[653,0,740,38,725],119878:[668,10,650,50,680],119879:[653,0,550,25,670],119880:[653,13,705,65,775],119881:[653,16,575,60,760],119882:[653,16,916,60,1101],119883:[653,0,790,25,810],119884:[653,0,535,35,695],119885:[653,0,772,60,802],119886:[441,10,502,40,472],119887:[668,11,470,45,450],119888:[441,11,415,40,400],119889:[668,12,532,40,527],119890:[441,11,445,40,410],119891:[668,187,555,40,615],119892:[441,187,492,20,492],119894:[616,11,311,50,257],119895:[616,187,389,-16,372],119896:[668,11,542,45,527],119897:[668,10,318,45,278],119898:[441,8,710,30,680],119899:[441,8,497,30,467],119900:[441,11,458,40,438],119901:[441,183,489,-30,474],119902:[441,183,458,40,463],119903:[441,0,408,30,393],119904:[441,11,440,50,390],119905:[567,9,313,40,283],119906:[441,9,474,30,444],119907:[458,9,506,72,479],119908:[460,9,775,72,748],119909:[441,9,550,30,510],119910:[440,183,496,30,496],119911:[450,14,499,42,467]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/MathItalic.js");
