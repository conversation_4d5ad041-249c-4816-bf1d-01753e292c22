/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscMathSymbolsB.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.STIXGeneral,{10624:[695,189,594,85,509],10625:[487,-14,565,46,519],10626:[566,59,503,110,393],10627:[719,213,596,108,477],10628:[719,213,596,119,488],10629:[719,213,463,70,393],10630:[719,213,463,70,393],10631:[719,214,511,115,367],10632:[719,214,511,144,396],10633:[719,213,511,100,352],10634:[719,213,511,159,411],10635:[719,213,469,188,447],10636:[719,213,469,22,281],10637:[719,213,469,188,447],10638:[719,213,469,22,281],10639:[719,213,469,188,447],10640:[719,213,469,22,281],10641:[719,213,400,73,357],10642:[719,213,400,73,357],10643:[649,143,685,34,591],10644:[649,143,685,94,651],10645:[649,143,685,86,643],10646:[649,143,685,42,599],10647:[719,213,488,188,466],10648:[719,213,488,22,300],10649:[661,155,211,50,161],10650:[662,156,511,177,334],10651:[547,72,685,42,662],10652:[584,0,685,50,634],10653:[584,0,685,50,634],10654:[547,0,685,11,675],10655:[396,0,685,24,643],10656:[517,13,685,57,654],10657:[609,-12,685,77,607],10658:[547,0,685,42,662],10659:[547,0,685,42,662],10660:[547,200,685,23,643],10661:[547,200,685,42,662],10662:[547,0,900,40,860],10663:[547,0,900,40,860],10664:[574,72,685,29,649],10665:[574,72,685,36,656],10666:[578,68,685,29,649],10667:[578,68,685,36,656],10668:[562,58,706,34,680],10669:[562,58,706,26,672],10670:[562,58,706,34,680],10671:[562,58,708,26,672],10672:[583,79,762,50,712],10673:[717,79,762,50,712],10674:[819,79,762,50,712],10675:[832,79,762,50,712],10676:[832,79,762,50,712],10677:[623,119,910,24,886],10678:[623,119,842,50,792],10679:[623,119,842,50,792],10680:[623,119,842,50,792],10681:[623,119,842,50,792],10682:[623,119,842,50,792],10683:[623,119,842,50,792],10684:[623,119,842,50,792],10685:[882,179,842,50,792],10686:[623,119,842,50,792],10687:[623,119,842,50,792],10688:[623,119,842,50,792],10689:[623,119,842,50,792],10690:[623,119,1091,50,1056],10691:[623,119,1091,50,1056],10692:[662,158,910,45,865],10693:[662,158,910,45,865],10694:[662,158,910,45,865],10695:[662,158,910,45,865],10696:[662,158,910,45,865],10697:[712,207,1046,64,982],10698:[1003,127,1145,35,1110],10699:[811,259,1145,35,1110],10700:[811,127,1145,35,1110],10701:[811,127,1165,15,1150],10702:[698,193,780,70,710],10703:[531,25,857,48,777],10704:[531,25,857,80,809],10705:[582,80,810,93,716],10706:[582,80,810,93,716],10707:[582,80,810,93,716],10708:[582,80,810,94,717],10709:[582,80,810,93,716],10710:[602,100,810,74,736],10711:[602,100,810,74,736],10712:[620,116,511,177,334],10713:[620,116,511,176,333],10714:[620,116,688,177,511],10715:[620,116,688,177,511],10716:[430,0,926,70,854],10717:[653,0,926,70,854],10718:[695,189,926,70,854],10719:[403,-103,1145,50,1095],10720:[662,157,910,45,865],10721:[512,8,667,24,613],10722:[414,0,790,64,726],10723:[662,156,685,47,637],10724:[842,156,685,47,637],10725:[662,156,685,48,637],10726:[584,78,798,60,738],10727:[695,189,628,48,580],10728:[811,127,1145,35,1110],10729:[811,127,1145,35,1110],10730:[744,241,762,32,730],10731:[795,289,790,45,745],10732:[743,241,762,50,712],10733:[743,241,762,50,712],10734:[747,243,762,97,665],10735:[747,243,762,97,665],10736:[747,243,762,32,730],10737:[747,243,762,32,730],10738:[747,243,762,65,697],10739:[747,243,762,65,697],10740:[521,13,926,55,871],10742:[765,80,520,94,426],10743:[662,80,520,94,426],10744:[695,325,602,85,517],10745:[695,325,602,85,517],10746:[532,25,685,64,621],10747:[532,25,685,64,621],10748:[713,213,459,77,394],10749:[713,213,459,65,382],10750:[540,36,762,93,669],10751:[316,-190,762,93,669]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Regular/MiscMathSymbolsB.js");
