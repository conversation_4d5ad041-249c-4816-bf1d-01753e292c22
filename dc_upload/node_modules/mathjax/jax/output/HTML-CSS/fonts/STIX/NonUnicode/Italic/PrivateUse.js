/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Italic/PrivateUse.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXNonUnicode-italic"],{57500:[756,218,753,37,787],57501:[756,218,706,42,732],57502:[756,218,624,42,724],57523:[681,207,500,-141,504],57684:[653,0,671,3,606],57685:[653,0,686,17,676],57686:[653,0,639,17,664],57687:[653,0,469,18,664],57688:[666,18,702,35,702],57689:[653,0,320,21,350],57690:[653,18,562,16,595],57691:[653,0,700,17,730],57692:[653,0,608,18,524],57693:[653,0,858,25,892],57694:[666,18,723,35,713],57695:[666,18,624,24,669],57696:[653,0,463,30,682],57697:[653,14,648,33,716],57698:[653,0,492,75,678],57699:[653,0,810,100,963],57700:[653,0,650,-24,770],57701:[653,0,458,42,658],57702:[441,11,566,40,521],57703:[683,11,598,27,558],57704:[441,11,504,40,480],57705:[683,0,331,28,519],57706:[441,220,599,9,572],57707:[683,0,588,27,543],57708:[683,0,545,25,530],57709:[683,0,306,27,356],57710:[441,0,857,27,812],57711:[441,0,588,27,543],57712:[441,11,534,40,494],57713:[441,218,628,5,588],57714:[441,218,604,40,574],57715:[442,0,332,27,467],57716:[441,11,502,34,468],57717:[633,10,323,20,328],57718:[428,13,588,40,556],57719:[428,0,395,45,462],57720:[428,0,639,56,690],57721:[428,0,557,14,554],57722:[428,218,471,5,538],57723:[428,0,540,21,530],57780:[676,14,500,86,578],57781:[677,0,500,223,469],57782:[676,0,500,35,574],57783:[676,14,500,44,544],57784:[676,0,500,52,547],57785:[676,14,500,49,626],57786:[684,14,500,83,617],57787:[662,8,500,146,616],57788:[676,14,500,81,560],57789:[676,21,500,51,579],57790:[683,10,536,45,527],57791:[674,0,660,28,632],57792:[662,0,662,60,627],57793:[662,0,562,60,665],57794:[674,0,660,28,632],57795:[662,0,639,60,664],57796:[662,0,698,25,760],57797:[662,0,700,60,735],57798:[676,14,780,75,755],57799:[662,0,433,50,503],57800:[662,0,631,60,715],57801:[674,0,664,20,624],57802:[662,0,890,60,918],57803:[662,14,724,60,752],57804:[662,0,722,47,754],57805:[676,14,780,75,755],57806:[662,0,700,60,735],57807:[662,0,538,60,624],57808:[676,14,780,75,755],57809:[662,0,654,21,706],57810:[662,0,585,72,659],57811:[676,0,593,83,725],57812:[662,0,736,52,736],57813:[662,0,722,20,795],57814:[681,0,712,105,805],57815:[676,0,795,39,795],57816:[463,10,586,47,616],57817:[683,215,535,-12,559],57818:[463,216,503,84,527],57819:[683,10,497,30,537],57820:[463,10,494,35,484],57821:[683,213,429,32,454],57822:[463,215,493,38,486],57823:[683,10,518,65,511],57824:[464,10,296,56,268],57825:[464,0,472,38,517],57826:[683,11,536,18,502],57827:[453,215,561,-9,536],57828:[464,14,376,41,416],57829:[683,215,434,43,464],57830:[463,10,533,45,505],57831:[453,10,565,45,589],57832:[462,216,534,-33,510],57833:[463,212,436,52,500],57834:[453,10,607,45,625],57835:[453,10,468,42,486],57836:[463,10,514,61,490],57837:[464,216,665,55,641],57838:[463,215,514,-72,552],57839:[461,216,654,75,705],57840:[454,10,630,50,636],57841:[463,10,462,45,467],57842:[683,12,534,45,525],57843:[684,216,648,48,630],57844:[463,216,536,38,518],57845:[453,10,795,40,811],57954:[460,11,570,56,514],57958:[460,0,570,100,415],57962:[460,0,570,59,487],57966:[461,217,570,40,513],57970:[450,217,570,17,542],57974:[450,218,570,23,536],57978:[668,10,570,28,553],57982:[450,217,570,40,543],57986:[668,10,570,50,519],57990:[460,217,570,23,526],58004:[756,218,753,37,754],58006:[756,218,698,42,686],58008:[756,218,678,52,716],58010:[756,218,830,42,793],58012:[756,218,706,42,724],58014:[756,217,624,42,724],58016:[756,217,768,52,766],58018:[756,218,825,42,863],58020:[756,218,429,-7,467],58022:[756,218,530,60,568],58024:[756,218,766,42,804],58026:[756,218,696,42,654],58028:[756,218,969,42,1007],58030:[756,218,799,42,837],58032:[756,218,764,52,739],58034:[756,217,581,14,710],58036:[756,217,764,52,739],58038:[756,218,699,42,682],58040:[756,218,557,52,576],58042:[756,218,532,19,647],58044:[756,218,706,67,771],58046:[756,218,605,56,730],58048:[756,218,831,62,956],58050:[756,218,737,27,755],58052:[756,218,492,1,647],58054:[756,218,686,62,714],58056:[756,240,565,42,533],58058:[756,240,530,47,530],58060:[756,240,477,33,501],58062:[756,240,586,14,581],58064:[756,240,490,8,475],58066:[756,240,582,-4,704],58068:[756,240,515,22,513],58070:[756,240,577,47,545],58072:[756,217,326,-9,454],58074:[755,240,550,-54,653],58076:[756,240,554,57,591],58078:[756,217,335,-14,449],58080:[756,240,823,32,791],58082:[756,240,565,32,545],58084:[756,240,533,42,519],58086:[756,217,581,-24,613],58088:[756,240,521,40,523],58090:[756,240,436,32,507],58092:[756,240,466,26,494],58094:[756,217,353,-22,441],58096:[756,240,537,21,505],58098:[756,218,506,72,545],58100:[756,217,775,72,793],58102:[756,240,566,32,584],58104:[756,218,530,32,575],58106:[756,240,499,40,507],58156:[756,218,613,42,612],58158:[756,218,595,-47,644],58160:[756,218,514,-58,634],58162:[756,218,536,40,522],58164:[756,218,478,29,491],58166:[756,218,440,11,482],58168:[756,218,512,32,536],58170:[756,218,529,20,519],58172:[756,217,326,-10,453],58174:[756,218,546,57,558],58176:[756,218,557,52,619],58178:[756,217,630,0,696],58180:[756,218,466,32,495],58182:[756,218,454,9,468],58184:[756,240,533,27,498],58186:[756,217,591,14,710],58188:[756,218,584,32,591],58190:[756,218,468,1,460],58192:[756,218,534,42,560],58194:[756,218,448,32,537],58196:[756,218,514,32,545],58198:[756,218,663,-2,690],58200:[756,218,632,4,700],58202:[756,218,668,32,736],58204:[756,217,733,42,758],58206:[756,218,602,32,590],58208:[756,218,666,42,778],58210:[756,217,889,32,897],58214:[756,240,444,7,482],58218:[756,240,528,-57,648],58222:[756,240,457,31,445],58226:[756,240,528,8,715],58228:[756,240,533,-16,559],58230:[756,218,533,42,525],58232:[756,218,533,35,506],58234:[756,218,477,42,539],58237:[756,218,710,-50,694],58239:[683,10,606,10,601],58241:[683,10,554,39,540],58243:[579,10,353,6,323],58245:[460,10,326,15,278],58247:[668,0,490,30,502],58249:[668,0,490,30,478]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/NonUnicode/Italic/PrivateUse.js");
