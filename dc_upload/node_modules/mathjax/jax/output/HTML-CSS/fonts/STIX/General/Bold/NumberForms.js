/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/STIX/General/Bold/NumberForms.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold"],{8531:[688,12,750,-7,763],8532:[688,12,750,28,763],8533:[688,12,750,-7,775],8534:[688,12,750,28,775],8535:[688,12,750,23,775],8536:[688,12,750,22,775],8537:[688,12,750,-7,758],8538:[688,12,750,49,758],8539:[688,12,750,-7,775],8540:[688,12,750,23,775],8541:[688,12,750,49,775],8542:[688,12,750,30,775]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/Bold/NumberForms.js");
