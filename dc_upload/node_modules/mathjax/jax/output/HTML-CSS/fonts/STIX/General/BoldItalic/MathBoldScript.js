/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/MathBoldScript.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["STIXGeneral-bold-italic"],{120016:[699,21,984,50,955],120017:[699,21,1060,55,985],120018:[699,21,912,60,877],120019:[699,21,991,60,906],120020:[699,21,826,95,791],120021:[699,21,1042,65,1025],120022:[699,21,834,82,799],120023:[699,21,1171,65,1154],120024:[699,21,997,47,977],120025:[699,224,906,19,886],120026:[699,21,1154,45,1130],120027:[699,21,1036,40,1015],120028:[699,21,1300,60,1245],120029:[699,21,1095,60,1078],120030:[699,21,809,72,749],120031:[699,21,1025,55,994],120032:[699,52,809,72,749],120033:[699,21,1048,55,973],120034:[699,21,816,81,781],120035:[699,21,1030,65,1025],120036:[699,21,964,60,904],120037:[699,21,1040,60,1024],120038:[699,21,1320,60,1306],120039:[699,21,1033,64,1010],120040:[699,224,989,60,963],120041:[699,21,996,50,976],120042:[462,14,942,35,865],120043:[699,14,646,60,624],120044:[462,14,764,35,683],120045:[699,14,949,28,912],120046:[462,14,726,35,648],120047:[699,205,768,25,749],120048:[462,224,819,27,771],120049:[699,14,838,55,758],120050:[698,14,558,40,534],120051:[698,224,840,41,823],120052:[699,14,810,55,730],120053:[699,14,650,43,632],120054:[462,14,1137,45,1057],120055:[462,14,851,45,771],120056:[462,14,848,35,780],120057:[462,205,885,25,770],120058:[462,205,913,35,833],120059:[462,0,677,40,648],120060:[557,14,562,51,449],120061:[669,14,618,47,612],120062:[449,14,842,31,762],120063:[458,14,732,40,670],120064:[458,14,1012,40,950],120065:[462,14,820,63,740],120066:[449,224,784,40,711],120067:[493,14,782,61,702]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/General/BoldItalic/MathBoldScript.js");
