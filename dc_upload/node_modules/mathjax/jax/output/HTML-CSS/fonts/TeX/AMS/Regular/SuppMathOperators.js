/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/SuppMathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_AMS,{10846:[813,97,611,55,555],10877:[636,138,778,83,694],10878:[636,138,778,83,694],10885:[762,290,778,55,722],10886:[762,290,778,55,722],10887:[635,241,778,82,693],10888:[635,241,778,82,693],10889:[761,387,778,57,718],10890:[761,387,778,57,718],10891:[1003,463,778,83,694],10892:[1003,463,778,83,694],10901:[636,138,778,83,694],10902:[636,138,778,83,694],10933:[752,286,778,82,693],10934:[752,286,778,82,693],10935:[761,294,778,57,717],10936:[761,294,778,57,717],10937:[761,337,778,57,718],10938:[761,337,778,57,718],10949:[753,215,778,84,694],10950:[753,215,778,83,694],10955:[783,385,778,82,693],10956:[783,385,778,82,693]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/AMS/Regular/SuppMathOperators.js");
