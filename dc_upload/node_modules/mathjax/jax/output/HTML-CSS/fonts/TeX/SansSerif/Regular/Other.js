/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/TeX/SansSerif/Regular/Other.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_SansSerif,{160:[0,0,250,0,0],305:[444,0,239,74,164],567:[444,205,267,-59,192],915:[691,0,542,87,499],916:[694,0,833,42,790],920:[716,21,778,56,722],923:[694,0,611,28,582],926:[688,0,667,42,624],928:[691,0,708,86,621],931:[694,0,722,55,666],933:[716,0,778,55,722],934:[694,0,722,55,666],936:[694,0,778,55,722],937:[716,0,722,44,677],8211:[312,-236,500,0,499],8212:[312,-236,1000,0,999],8216:[694,-471,278,90,189],8217:[694,-471,278,89,188],8220:[694,-471,500,174,467],8221:[694,-471,500,32,325]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/SansSerif/Regular/Other.js");
