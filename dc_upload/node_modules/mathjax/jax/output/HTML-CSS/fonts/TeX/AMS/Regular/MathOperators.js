/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/MathOperators.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_AMS,{8705:[846,21,500,56,444],8708:[860,166,556,55,497],8709:[587,3,778,54,720],8717:[440,1,429,102,456],8722:[270,-230,500,84,417],8724:[766,93,778,57,722],8726:[430,23,778,91,685],8733:[472,-28,778,56,722],8736:[694,0,722,55,666],8737:[714,20,722,55,666],8738:[551,51,722,55,666],8739:[430,23,222,91,131],8740:[750,252,278,-21,297],8741:[431,23,389,55,331],8742:[750,250,500,-20,518],8756:[471,82,667,24,643],8757:[471,82,667,23,643],8764:[365,-132,778,55,719],8765:[367,-133,778,56,722],8769:[467,-32,778,55,719],8770:[463,-34,778,55,720],8774:[652,155,778,54,720],8776:[481,-50,778,55,719],8778:[579,39,778,51,725],8782:[492,-8,778,56,722],8783:[492,-133,778,56,722],8785:[609,108,778,56,722],8786:[601,101,778,15,762],8787:[601,102,778,14,762],8790:[367,-133,778,56,722],8791:[721,-133,778,56,722],8796:[859,-133,778,56,723],8806:[753,175,778,83,694],8807:[753,175,778,83,694],8808:[752,286,778,82,693],8809:[752,286,778,82,693],8812:[750,250,500,74,425],8814:[708,209,778,82,693],8815:[708,209,778,82,693],8816:[801,303,778,82,694],8817:[801,303,778,82,694],8818:[732,228,778,56,722],8819:[732,228,778,56,722],8822:[681,253,778,44,734],8823:[681,253,778,83,694],8828:[580,153,778,83,694],8829:[580,154,778,82,694],8830:[732,228,778,56,722],8831:[732,228,778,56,722],8832:[705,208,778,82,693],8833:[705,208,778,82,693],8840:[801,303,778,83,693],8841:[801,303,778,82,691],8842:[635,241,778,84,693],8843:[635,241,778,82,691],8847:[539,41,778,83,694],8848:[539,41,778,64,714],8858:[582,82,778,57,721],8859:[582,82,778,57,721],8861:[582,82,778,57,721],8862:[689,0,778,55,722],8863:[689,0,778,55,722],8864:[689,0,778,55,722],8865:[689,0,778,55,722],8872:[694,0,611,55,555],8873:[694,0,722,55,666],8874:[694,0,889,55,833],8876:[695,1,611,-55,554],8877:[695,1,611,-55,554],8878:[695,1,722,-55,665],8879:[695,1,722,-55,665],8882:[539,41,778,83,694],8883:[539,41,778,83,694],8884:[636,138,778,83,694],8885:[636,138,778,83,694],8888:[408,-92,1111,55,1055],8890:[431,212,556,57,500],8891:[716,0,611,55,555],8892:[716,0,611,55,555],8901:[189,0,278,55,222],8903:[545,44,778,55,720],8905:[492,-8,778,146,628],8906:[492,-8,778,146,628],8907:[694,22,778,55,722],8908:[694,22,778,55,722],8909:[464,-36,778,56,722],8910:[578,21,760,83,676],8911:[578,22,760,83,676],8912:[540,40,778,84,694],8913:[540,40,778,83,693],8914:[598,22,667,55,611],8915:[598,22,667,55,611],8916:[736,22,667,56,611],8918:[541,41,778,82,693],8919:[541,41,778,82,693],8920:[568,67,1333,56,1277],8921:[568,67,1333,55,1277],8922:[886,386,778,83,674],8923:[886,386,778,83,674],8926:[734,0,778,83,694],8927:[734,0,778,82,694],8928:[801,303,778,82,693],8929:[801,303,778,82,694],8934:[730,359,778,55,719],8935:[730,359,778,55,719],8936:[730,359,778,55,719],8937:[730,359,778,55,719],8938:[706,208,778,82,693],8939:[706,208,778,82,693],8940:[802,303,778,82,693],8941:[801,303,778,82,693]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/AMS/Regular/MathOperators.js");
