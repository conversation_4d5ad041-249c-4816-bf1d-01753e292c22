/*
 *  /MathJax-v2/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/PUA.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_AMS,{57350:[430,23,222,-20,240],57351:[431,24,389,-20,407],57352:[605,85,778,55,719],57353:[434,6,667,37,734],57356:[752,284,778,82,693],57357:[752,284,778,82,693],57358:[919,421,778,82,694],57359:[801,303,778,82,694],57360:[801,303,778,82,694],57361:[919,421,778,82,694],57366:[828,330,778,82,694],57367:[752,332,778,82,694],57368:[828,330,778,82,694],57369:[752,333,778,82,693],57370:[634,255,778,84,693],57371:[634,254,778,82,691]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/AMS/Regular/PUA.js");
