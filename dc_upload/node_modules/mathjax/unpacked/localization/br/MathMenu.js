/*************************************************************
 *
 *  MathJax/localization/br/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("br","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "Diskouez ar formulenn evel",
          MathMLcode: "Kod MathML",
          OriginalMathML: "MathML orin",
          TeXCommands: "Urzhio\u00F9 TeX",
          Original: "Furmskrid orin",
          ErrorMessage: "Kemennadenn fazi",
          Annotation: "Notenn",
          TeX: "TeX",
          StarMath: "StarMath",
          OpenMath: "OpenMath",
          Settings: "Arventenno\u00F9 ar matematiko\u00F9",
          Click: "Klika\u00F1",
          DoubleClick: "Daouglika\u00F1",
          NoZoom: "Zoum ebet",
          Option: "Dibarzh",
          Command: "Urzhiad",
          Control: "Kontroll",
          ZoomFactor: "Feur zoum",
          ForHTMLCSS: "Evit HTML-CSS :",
          Auto: "Emgefre",
          TeXLocal: "TeX (lec'hel)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (skeudenn)",
          STIXLocal: "STIX (lec'hel)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "La\u00F1ser kemperzhel",
          Browser: "Merdeer",
          Locale: "Yezh",
          LoadLocale: "Karga\u00F1 diwar an URL ...",
          About: "Diwar-benn MathJax",
          Help: "Sikour MathJax",
          wofforotffonts: "fonto\u00F9 woff pe otf",
          eotffonts: "fonto\u00F9 eot",
          svgfonts: "Fonto\u00F9 svg",
          LoadURL: "Karga\u00F1 roadenno\u00F9 trei\u00F1 eus an URL-ma\u00F1 :",
          Close: "Serri\u00F1",
          EqSource: "Tarzh kevatalenn MathJax",
          AsciiMathInput: "Moned AsciiMathML",
          Maple: "Maple",
          ContentMathML: "Endalc'h MathML",
          texHints: "Diskwel ar skoazello\u00F9 Tex e MathML",
          ZoomTrigger: "Distegner Zoum",
          TriggerRequires: "An distegner en deus ezhomm :",
          Alt: "Erl",
          Shift: "Pennlizherenn",
          MPHandles: "Lezel MathPlayer da vera\u00F1 :",
          MenuEvents: "Darvoudo\u00F9 la\u00F1ser",
          MouseEvents: "Darvoudo\u00F9 logodenn",
          MenuAndMouse: "Darvoudo\u00F9 logodenn ha la\u00F1serio\u00F9",
          FontPrefs: "Penndibabo\u00F9 font",
          Scale: "Lakaat an holl jedado\u00F9 hervez ar skeuliad...",
          localTeXfonts: "Implijout ar fonto\u00F9 TeX lec'hel",
          webTeXfonts: "Implijout fonto\u00F9 TeX ar web",
          imagefonts: "Implijout ar fonto\u00F9 Skeudenn",
          localSTIXfonts: "Implijout ar fonto\u00F9 STIX lec'hel",
          webSVGfonts: "Implijout fonto\u00F9 SVG ar web",
          genericfonts: "Implijout ar fonto\u00F9 Unicode generek",
          BadData: "C'hwitet eo pellgardadur ar roadenno\u00F9 trei\u00F1 adalek %1",
          NonZeroScale: "Ne zlefe ket ar skeuliad beza\u00F1 par da zero",
          PercentScale: "Ar skeuliad a zlefe beza\u00F1 un dregantad (da skouer, 120 %%)",
          IE8warning: "Se a ziweredekay al la\u00F1ser hag ar arc'hwelio\u00F9 zouma\u00F1. En o flas avat e c'halli klika\u00F1 war ur bomm en ur boueza\u00F1 war ar bouton Alt\n evit kaout al la\u00F1ser MathjAX.",
          IE9warning: "Diweredekaet e vo al la\u00F1ser kemperzhel MathJax, met en e flas e c'hallot klika\u00F1 war ur bomm o terc'hel da boueza\u00F1 war ar bouton Alt evit kaout al la\u00F1ser MathJax.",
          NoOriginalForm: "N'haller kaout furmad orin ebet",
          CloseAboutDialog: "Serri\u00F1 ar voest kendivizout A-zivout MathJax",
          FastPreview: "Dalberzh prim",
          InTabOrder: "Enlakaat an urzh an ivinello\u00F9"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/br/MathMenu.js");
