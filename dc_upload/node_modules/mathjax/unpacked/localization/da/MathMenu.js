/*************************************************************
 *
 *  MathJax/localization/da/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("da","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "Vis matematik som",
          MathMLcode: "MathML-kode",
          OriginalMathML: "Oprindelig MathML",
          TeXCommands: "TeX-kommandoer",
          AsciiMathInput: "AsciiMathML-input",
          Original: "Oprindeligt format",
          ErrorMessage: "Fejlmeddelelse",
          Annotation: "Note",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Content MathML",
          OpenMath: "OpenMath",
          texHints: "Vis TeX-tips i MathML",
          Settings: "Matematikindstillinger",
          ZoomTrigger: "Zoomudl\u00F8ser",
          Hover: "Holde musen over",
          Click: "Klik",
          DoubleClick: "Dobbeltklik",
          NoZoom: "Ingen zoom",
          TriggerRequires: "Udl\u00F8ser kr\u00E6ver:",
          Option: "Alternativ (\u2325)",
          Alt: "Alt",
          Command: "Kommando (\u2318)",
          Control: "Ctrl",
          Shift: "Skift",
          ZoomFactor: "Zoomfaktor",
          Renderer: "Matematik-visningsmetode",
          MPHandles: "Lad MathPlayer h\u00E5ndtere:",
          MenuEvents: "Menuh\u00E6ndelser",
          MouseEvents: "Museh\u00E6ndelser",
          MenuAndMouse: "Muse- og menuh\u00E6ndelser",
          FontPrefs: "Skrifttype-indstillinger",
          ForHTMLCSS: "For HTML-CSS:",
          Auto: "Automatisk",
          TeXLocal: "TeX (lokal)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (billede)",
          STIXLocal: "STIX (lokal)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Kontekstmenu",
          Browser: "Browser",
          Scale: "Skaler al matematik...",
          Discoverable: "Fremh\u00E6v n\u00E5r musen holdes over",
          Locale: "Sprog",
          LoadLocale: "Indl\u00E6s fra URL ...",
          About: "Om MathJax",
          Help: "MathJax-hj\u00E6lp",
          localTeXfonts: "bruger lokale TeX-skrifttyper",
          webTeXfonts: "bruger web-TeX-skrifttype",
          imagefonts: "bruger billedskrifttyper",
          localSTIXfonts: "bruger lokale STIX-skrifttyper",
          webSVGfonts: "bruger web-SVG-skrifttyper",
          genericfonts: "bruger generiske Unicode-skrifttyper",
          wofforotffonts: "WOFF- eller OTF-skrifttyper",
          eotffonts: "EOT-skrifttyper",
          svgfonts: "SVG-skrifttyper",
          WebkitNativeMMLWarning: "Din browser har tilsyneladende ikke indbygget MathML-underst\u00F8ttelse, s\u00E5 et skift til MathML-output kan g\u00F8re matematikken p\u00E5 siden ul\u00E6selig",
          MSIENativeMMLWarning: "Internet Explorer kr\u00E6ver MathPlayer-pluginnet for at kunne behandle MathML-output.",
          OperaNativeMMLWarning: "Operas underst\u00F8ttelse af MathML er begr\u00E6nset, s\u00E5 et skift til MathML-output kan for\u00E5rsage at nogle udtryk bliver vist d\u00E5rligt.",
          SafariNativeMMLWarning: "Din browsers indbyggede MathML underst\u00F8tter ikke alle de funktioner, der anvendes af MathJax, s\u00E5 nogle udtryk gengives muligvis ikke korrekt.",
          FirefoxNativeMMLWarning: "Din browsers indbyggede MathML underst\u00F8tter ikke al den funktionalitet, der anvendes af MathJax, s\u00E5 nogle udtryk gengives muligvis ikke korrekt.",
          MSIESVGWarning: "SVG er ikke implementeret i Internet Explorer f\u00F8r IE9 eller n\u00E5r den emulerer IE8 eller tidligere versioner. Et skift til SVG-output vil for\u00E5rsage at matematikken ikke vises korrekt.",
          LoadURL: "Indl\u00E6s overs\u00E6ttelsesdata fra denne URL:",
          BadURL: "URL-adressen skal v\u00E6re for en JavaScript-fil, der definerer MathJax-overs\u00E6ttelsesdata. JavaScript-filnavne b\u00F8r slutte p\u00E5 '.js'",
          BadData: "Kunne ikke indl\u00E6se overs\u00E6ttelsesdata fra %1",
          SwitchAnyway: "Skift visningsmetode alligevel?\n\n(Tryk p\u00E5 OK for at skifte, ANNULLER for at forts\u00E6tte med den nuv\u00E6rende visningsmetode)",
          ScaleMath: "Skal\u00E9r al matematik (i forhold til omgivende tekst) med",
          NonZeroScale: "Skaleringen m\u00E5 ikke v\u00E6re nul",
          PercentScale: "Skaleringen skal v\u00E6re en procentsats (for eksempel 120\u00A0%%)",
          IE8warning: "Dette vil deaktivere menu- og zoom-funktionaliteterne i MathJax, men du kan i stedet Alt-klikke p\u00E5 et udtryk for at f\u00E5 MathJax-menuen.\n\nVil du virkelig \u00E6ndre indstillingerne for MathPlayer?",
          IE9warning: "MathJax-kontekstmenuen vil blive deaktiveret, men du kan i stedet Alt-klikke p\u00E5 et udtryk for at f\u00E5 MathJax-menuen.",
          NoOriginalForm: "Intet oprindeligt format til r\u00E5dighed",
          Close: "Luk",
          EqSource: "MathJax-ligningskilde",
          CloseAboutDialog: "Luk dialogen \"Om MathJax\"",
          FastPreview: "Hurtig forh\u00E5ndsvisning",
          AssistiveMML: "Sk\u00E6rml\u00E6serunderst\u00F8ttelse for MathML",
          InTabOrder: "Medtag i tabulatorr\u00E6kkef\u00F8lge"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/da/MathMenu.js");
