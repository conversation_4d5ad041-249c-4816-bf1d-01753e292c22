/*************************************************************
 *
 *  MathJax/localization/lki/MathML.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("lki","MathML",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          BadMglyph: "mglyph \u0646\u0627\u0645\u0646\u0627\u0633\u0628: %1",
          BadMglyphFont: "\u0642\u0644\u0645 \u0646\u0627\u0645\u0646\u0627\u0633\u0628: %1",
          MathPlayer: "MathJax \u0646\u062A\u0648\u0627\u0646\u0633\u062A MathPlayer \u0631\u0627 \u0631\u0627\u0647\u200C\u0627\u0646\u062F\u0627\u0632\u06CC \u06A9\u0646\u062F.\n\n\u0627\u06AF\u0631 MathPlayer \u0646\u0635\u0628 \u0646\u06CC\u0633\u062A\u060C \u0634\u0645\u0627 \u0646\u06CC\u0627\u0632 \u062F\u0627\u0631\u06CC\u062F \u06A9\u0647 \u0627\u0628\u062A\u062F\u0627 \u0622\u0646 \u0631\u0627 \u0646\u0635\u0628 \u06A9\u0646\u06CC\u062F.\n\u062F\u0631 \u063A\u06CC\u0631 \u0627\u06CC\u0646 \u0635\u0648\u0631\u062A\u060C \u062A\u0646\u0638\u06CC\u0645\u0627\u062A \u0627\u0645\u0646\u06CC\u062A\u06CC \u0634\u0645\u0627 \u0645\u0645\u06A9\u0646 \u0627\u0633\u062A \u06A9\u0647 \u0627\u0632 \u0627\u062C\u0631\u0627\u06CC\n\u06A9\u0646\u062A\u0631\u0644\u200C\u0647\u0627\u06CC \u0627\u06A9\u062A\u06CC\u0648\u0627\u06A9\u0633 \u062C\u0644\u0648\u06AF\u06CC\u0631\u06CC \u06A9\u0646\u062F. \u0627\u0632 \u06AF\u0632\u06CC\u0646\u0647\u200C\u0647\u0627\u06CC \u0627\u06CC\u0646\u062A\u0631\u0646\u062A\u06CC \u0645\u0648\u062C\u0648\u062F \u0632\u06CC\u0631\n\u0645\u0646\u0648\u06CC \u0627\u0628\u0632\u0627\u0631 \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u06A9\u0646\u06CC\u062F \u0648 \u0628\u0631\u06AF\u0647\u0654 \u0627\u0645\u0646\u06CC\u062A \u0631\u0627 \u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F\u060C \u0633\u067E\u0633 \u062F\u06A9\u0645\u0647\u0654\n\u0645\u0631\u062C\u0644\u0647\u0654 \u0633\u0641\u0627\u0631\u0634\u06CC \u0631\u0627 \u0641\u0634\u0627\u0631 \u062F\u0647\u06CC\u062F. \u0628\u0631\u0631\u0633\u06CC \u06A9\u0646\u06CC\u062F \u06A9\u0647 \u062A\u0646\u0638\u06CC\u0645\u0627\u062A \u0627\u062C\u0631\u0627\u06CC\n\u00AB\u0627\u062C\u0631\u0627\u06CC \u06A9\u0646\u062A\u0631\u0644\u200C\u0647\u0627\u06CC \u0627\u06A9\u062A\u06CC\u0648\u0627\u06A9\u0633\u00BB \u0648 \u00AB\u0631\u0641\u062A\u0627\u0631\u0647\u0627\u06CC \u062F\u0648\u062F\u0648\u06CC\u06CC \u0648 \u0627\u0633\u06A9\u0631\u06CC\u067E\u062A\u00BB \u0641\u0639\u0627\u0644\n\u0647\u0633\u062A\u0646\u062F.\n\n\u062F\u0631 \u062D\u0627\u0644 \u062D\u0627\u0636\u0631 \u0634\u0645\u0627 \u0628\u0647 \u062C\u0627\u06CC \u062D\u0631\u0648\u0641 \u0631\u06CC\u0627\u0636\u06CC \u067E\u06CC\u0627\u0645\u200C\u0647\u0627\u06CC \u062E\u0637\u0627\u06CC \u062E\u0648\u0627\u0647\u06CC\u062F \u062F\u06CC\u062F.",
          CantCreateXMLParser: "MathJax \u0646\u062A\u0648\u0627\u0633\u062A \u06CC\u06A9 \u062A\u062C\u0632\u06CC\u0647\u200C\u06AF\u0631 \u0627\u06A9\u0633\u200C\u0627\u0645\u200C\u0627\u0644 \u0628\u0631\u0627\u06CC MathML \u0627\u06CC\u062C\u0627\u062F \u06A9\u0646\u062F.\n\u0628\u0631\u0631\u0633\u06CC \u06A9\u0646\u06CC\u062F \u06A9\u0647 \u062A\u0646\u0638\u06CC\u0645\u0627\u062A \u0627\u0645\u0646\u06CC\u062A\u06CC \u00AB\u0627\u0633\u06A9\u0631\u06CC\u067E\u062A \u06A9\u0646\u062A\u0631\u0644\u200C\u0647\u0627\u06CC \u0627\u06A9\u062A\u06CC\u0648\u0627\u06A9\u0633\n\u0639\u0644\u0627\u0645\u062A\u200C\u06AF\u0630\u0627\u0631\u06CC\u200C\u0634\u062F\u0647 \u0628\u0647 \u0639\u0646\u0648\u0627\u0646 \u0627\u0645\u0646 \u0628\u0631\u0627\u06CC \u06A9\u062F\u0632\u0646\u06CC\u00BB \u0641\u0639\u0627\u0644 \u0627\u0633\u062A (\u0627\u0632\n\u06AF\u0632\u06CC\u0646\u0647\u0654 \u0627\u06CC\u0646\u062A\u0631\u0646\u062A\u06CC \u06AF\u0632\u06CC\u0646\u0647 \u062F\u0631 \u0645\u0646\u0648\u06CC \u0627\u0628\u0632\u0627\u0631\u0647\u0627 \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u06A9\u0646\u06CC\u062F \u0648 \u067E\u0646\u0644 \u0627\u0645\u0646\u06CC\u062A \u0631\u0627 \u0627\u0646\u062A\u062E\u0627\u0628\n\u06A9\u0646\u06CC\u062F \u0648 \u062F\u06A9\u0645\u0647\u0654 \u0645\u0631\u062D\u0644\u0647\u0654 \u0633\u0641\u0627\u0631\u0634\u06CC \u062A\u0627 \u0627\u06CC\u0646 \u0631\u0627 \u0628\u0631\u0631\u0633\u06CC \u06A9\u0646\u06CC\u062F).\n\n\u0645\u0639\u0627\u062F\u0644\u0647\u200C\u0647\u0627\u06CC MathML \u0646\u0645\u06CC\u200C\u062A\u0648\u0627\u0646\u0646\u062F \u062A\u0648\u0633\u0637 MathJax \u067E\u0631\u062F\u0627\u0632\u0634 \u06AF\u0631\u062F\u0646\u062F.",
          UnknownNodeType: "\u0646\u0648\u0639 \u06AF\u0631\u0647\u0654 \u0646\u0627\u0634\u0646\u0627\u062E\u062A\u0647: %1",
          UnexpectedTextNode: "\u06AF\u0631\u0647\u0654 \u0645\u062A\u0646\u06CC \u063A\u06CC\u0631\u0645\u0646\u062A\u0638\u0631\u0647:\u200C %1",
          ErrorParsingMathML: "\u062E\u0637\u0627\u06CC \u062A\u062C\u0632\u06CC\u0647\u0654 MathML",
          ParsingError: "\u062E\u0637\u0627\u06CC \u062A\u062C\u0632\u06CC\u0647\u0654 MathML\u200F: %1",
          MathMLSingleElement: "MathML \u0628\u0627\u06CC\u062F \u0628\u0647 \u062F\u0646\u0628\u0627\u0644 \u06CC\u06A9 \u0639\u0646\u0635\u0631 \u0648\u0627\u062D\u062F \u0628\u06CC\u0627\u06CC\u062F",
          MathMLRootElement: "MathML \u0628\u0627\u06CC\u062F \u062A\u0648\u0633\u0637 \u0639\u0646\u0635\u0631 \u003Cmath\u003E \u062A\u0634\u06A9\u06CC\u0644 \u0634\u062F\u0647 \u0628\u0627\u0634\u062F \u0646\u0647 %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/lki/MathML.js");
