/*************************************************************
 *
 *  MathJax/localization/kn/TeX.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("kn","TeX",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "\u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 \u0CAE\u0CC1\u0C82\u0CAD\u0CBE\u0C97 \u0CAC\u0CCD\u0CB0\u0CC7\u0CB8\u0CCD \u0C85\u0CA5\u0CB5 \u0C95\u0CA1\u0CBF\u0CAE\u0CC6 \u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CA6 \u0CAC\u0CCD\u0CB0\u0CC7\u0CB8\u0CCD",
          ExtraCloseMissingOpen: "\u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 \u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CA6 \u0CAC\u0CCD\u0CB0\u0CC7\u0CB8\u0CCD \u0C85\u0CA5\u0CB5 \u0C95\u0CA1\u0CBF\u0CAE\u0CC6 \u0CAE\u0CC1\u0C82\u0CAD\u0CBE\u0C97 \u0CAC\u0CCD\u0CB0\u0CC7\u0CB8\u0CCD",
          MissingLeftExtraRight: "\u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \\left \u0C85\u0CA5\u0CB5 \u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 \\right",
          MissingScript: "\u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \u0C89\u0C9A\u0CCD\u0C9A\u0CBE\u0C95\u0CCD\u0CB7\u0CB0 \u0C85\u0CA5\u0CB5 \u0CA8\u0CBF\u0CAE\u0CCD\u0CA8\u0CBE\u0C95\u0CCD\u0CB7\u0CB0",
          ExtraLeftMissingRight: "\u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 \\left \u0C85\u0CA5\u0CB5 \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \\right",
          Misplaced: "\u0CA4\u0CAA\u0CCD\u0CAA \u0CB8\u0CCD\u0CA5\u0CB3\u0CA6\u0CB2\u0CCD\u0CB2\u0CBF \u0C87\u0C9F\u0CCD\u0C9F\u0CBF\u0CA6\u0CCD\u0CA6\u0CC1  %1",
          MissingOpenForSub: "\u0CA8\u0CBF\u0CAE\u0CCD\u0CA8\u0CBE\u0C95\u0CCD\u0CB7\u0CB0\u0C95\u0CCD\u0C95\u0CC6 \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \u0CAE\u0CC1\u0C82\u0CAD\u0CBE\u0C97",
          MissingOpenForSup: "\u0C89\u0C9A\u0CCD\u0C9A\u0CBE\u0C95\u0CCD\u0CB7\u0CB0\u0C95\u0CCD\u0C95\u0CC6 \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \u0CAE\u0CC1\u0C82\u0CAD\u0CBE\u0C97",
          AmbiguousUseOf: "%1 \u0C87\u0CA8 \u0C85\u0CB8\u0CCD\u0CAA\u0CB7\u0CCD\u0C9F \u0C89\u0CAA\u0CAF\u0CCB\u0C97",
          EnvBadEnd: "\\begin{%1} \u0C87\u0CA8 \u0C85\u0C82\u0CA4 \\end{%2} \u0C92\u0C9F\u0CCD\u0C9F\u0CBF\u0C97\u0CC6 \u0CAE\u0CBE\u0CA6\u0CBF\u0CB9\u0CCB\u0C97\u0CBF\u0CA6\u0CC6",
          EnvMissingEnd: "\u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \\end{%1}",
          MissingBoxFor: "%1 \u0C87\u0C97\u0CC6 \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \u0CB8\u0C82\u0CA6\u0CC2\u0C95",
          MissingCloseBrace: "\u0C95\u0CA1\u0CBF\u0CAE\u0CC6 \u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CA6 \u0CAC\u0CCD\u0CB0\u0CC7\u0CB8\u0CCD",
          UndefinedControlSequence: "\u0CB8\u0CCD\u0CAA\u0CB7\u0CCD\u0C9F\u0CC0\u0C95\u0CB0\u0CBF\u0CB8\u0CA6 \u0CA8\u0CBF\u0CAF\u0C82\u0CA4\u0CCD\u0CB0\u0CA3 \u0C85\u0CA8\u0CC1\u0C95\u0CCD\u0CB0\u0CAE %1",
          DoubleExponent: "\u0C8E\u0CB0\u0CA1\u0CC1 \u0CB8\u0CCD\u0CA5\u0CB0\u0CA6\u0CCD\u0CA6\u0CC1 \u0C89\u0C9A\u0CCD\u0C9A\u0CBE\u0C95\u0CCD\u0CB7\u0CB0; \u0CAC\u0CCD\u0CB0\u0CB8\u0CC6\u0CB8\u0CCD \u0C87\u0CA8 \u0C92\u0C9F\u0CCD\u0C9F\u0CBF\u0C97\u0CC6 \u0CB8\u0CCD\u0CAA\u0CB7\u0CCD\u0C9F \u0CAE\u0CBE\u0CA1\u0CC1",
          DoubleSubscripts: "\u0C8E\u0CB0\u0CA1\u0CC1 \u0CB8\u0CCD\u0CA5\u0CB0\u0CA6\u0CCD\u0CA6\u0CC1 \u0CA8\u0CBF\u0CAE\u0CCD\u0CA8\u0CBE\u0C95\u0CCD\u0CB7\u0CB0; \u0CAC\u0CCD\u0CB0\u0CB8\u0CC6\u0CB8\u0CCD \u0C87\u0CA8 \u0C92\u0C9F\u0CCD\u0C9F\u0CBF\u0C97\u0CC6 \u0CB8\u0CCD\u0CAA\u0CB7\u0CCD\u0C9F \u0CAE\u0CBE\u0CA1\u0CC1",
          DoubleExponentPrime: "\u0CAA\u0CCD\u0CB0\u0CC8\u0CAE\u0CCD \u0C87\u0C82\u0CA6 \u0C86\u0C97\u0CBF \u0C8E\u0CB0\u0CA1\u0CC1 \u0CB8\u0CCD\u0CA4\u0CB0\u0CA6\u0CCD\u0CA6\u0CC1 \u0C89\u0C9A\u0CCD\u0C9A\u0CBE\u0C95\u0CCD\u0CB7\u0CB0 \u0CB5\u0CA8\u0CCD\u0CA8\u0CC1 \u0CB9\u0CC1\u0CA4\u0CCD\u0CA4\u0CBF\u0CB8\u0CBF\u0CA4\u0CC1\u0CA6\u0CC6. \u0CAC\u0CCD\u0CB0\u0CB8\u0CC6\u0CB8\u0CCD \u0C92\u0C9F\u0CCD\u0C9F\u0CBF\u0C97\u0CC6 \u0CB8\u0CCD\u0CAA\u0CB7\u0CCD\u0C9F \u0CAE\u0CBE\u0CA6\u0CBF.",
          CantUseHash1: "'macro \u0CAA\u0CCD\u0CAF\u0CBE\u0CB0\u0CBE\u0CAE\u0CC0\u0C9F\u0CB0\u0CCD \u0C95\u0CCD\u0CAF\u0CBE\u0CB0\u0CC6\u0C95\u0CCD\u0C9F\u0CB0\u0CCD \u0C85\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAE\u0CBE\u0CA4\u0CCD \u0CAE\u0CCB\u0CA1\u0CCD \u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0CAE\u0CBE\u0CA1\u0CB2\u0CBF\u0C95\u0CCD\u0C95\u0CC6 \u0C86\u0C97\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2.",
          MisplacedMiddle: "%1 \\left \u0CAE\u0CA4\u0CCD\u0CA4\u0CC1 \\right  \u0C87\u0CA8 \u0CAE\u0CA7\u0CCD\u0CAF\u0CA6\u0CB2\u0CCD\u0CB2\u0CBF \u0C87\u0CB0\u0CAC\u0CC7\u0C95\u0CC1",
          MisplacedLimits: "%1 \u0CAC\u0CB0\u0CBF \u0C86\u0CAA\u0CB0\u0CC7\u0C9F\u0CB0\u0CCD\u0CB8\u0CCD \u0C87\u0C97\u0CC6 \u0CAE\u0CBE\u0CA4\u0CCD\u0CB0 \u0CB9\u0CBE\u0C95\u0CAC\u0CB9\u0CC1\u0CA6\u0CC1.",
          MisplacedMoveRoot: "%1 \u0CAC\u0CB0\u0CBF root \u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0CAE\u0CBE\u0CA4\u0CCD\u0CB0 \u0CAC\u0CB0 \u0CAC\u0CB9\u0CC1\u0CA6\u0CC1.",
          MultipleCommand: " \u0C85\u0CA8\u0CC7\u0C95  %1.",
          IntegerArg: "%1 \u0C87\u0C97\u0CC6 \u0CAC\u0C82\u0CA1 \u0C86\u0CB0\u0CCD\u0C97\u0CCD\u0CAF\u0CC1\u0CAE\u0CC6\u0C82\u0C9F\u0CCD \u0C92\u0C82\u0CA6\u0CC1 \u0CAA\u0CC1\u0CB0\u0CCD\u0CA8\u0CBE\u0CA8\u0CCD\u0C95\u0CB5\u0CC7 \u0C87\u0CB0\u0CAC\u0CC6\u0C95\u0CC1.",
          NotMathMLToken: "%1 \u0C9F\u0CCB\u0C95\u0CA8\u0CCD \u0C8E\u0CB2\u0CBF\u0CAE\u0CC6\u0C82\u0C9F\u0CCD \u0C85\u0CB2\u0CCD\u0CB2.",
          InvalidMathMLAttr: "\u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0CAE\u0CBE\u0CA4 \u0C8E\u0C82 \u0C8E\u0CB2\u0CCD \u0C97\u0CC1\u0CA3 :    %1",
          UnknownAttrForElement: "%1 %2 \u0C87\u0C97\u0CC6 \u0CAE\u0CBE\u0CA8\u0CCD\u0CAF\u0CA4\u0CC6 \u0C97\u0CC1\u0CA3 \u0C85\u0CB2\u0CCD\u0CB2",
          MaxMacroSub1: "\u0CAE\u0CA5\u0CCD\u0C9C\u0C95\u0CCD\u0CB7\u0CCD  \u0C97\u0CB0\u0CBF\u0CB7\u0CCD\u0CA0 \u0CAE\u0CCD\u0CAF\u0CBE\u0C95\u0CCD\u0CB0\u0CCB \u0CAA\u0CB0\u0CCD\u0CAF\u0CBE\u0CAF \u0CAE\u0CC0\u0CB0\u0CBF\u0CA6\u0CC6 \u0CAE\u0CCD\u0CAF\u0CBE\u0C97\u0CCD\u0CA8\u0CC6\u0C9F\u0CCD; \u0C92\u0C82\u0CA6\u0CC1 \u0CAA\u0CC1\u0CA8\u0CB0\u0CBE\u0CB5\u0CB0\u0CCD\u0CA4\u0CBF\u0CA4 \u0CAE\u0CCD\u0CAF\u0CBE\u0C95\u0CCD\u0CB0\u0CCB \u0C95\u0CB0\u0CC6 \u0C87\u0CB2\u0CCD\u0CB2?",
          MaxMacroSub2: "\u0C97\u0CB0\u0CBF\u0CB7\u0CCD\u0CA0 \u0CAA\u0CB0\u0CCD\u0CAF\u0CBE\u0CAF \u0CAE\u0CC0\u0CB0\u0CBF\u0CA6\u0CC6 \u0CAE\u0CCD\u0CAF\u0CBE\u0C97\u0CCD\u0CA8\u0CC6\u0C9F\u0CCD; \u0C92\u0C82\u0CA6\u0CC1 \u0CAA\u0CC1\u0CA8\u0CB0\u0CBE\u0CB5\u0CB0\u0CCD\u0CA4\u0CBF\u0CA4 \u0CB2\u0CCD\u0CAF\u0CBE\u0C9F\u0CC6\u0C95\u0CCD\u0CB8\u0CCD \u0CAA\u0CB0\u0CBF\u0CB8\u0CB0\u0C95\u0CCD\u0C95\u0CC6 \u0C87\u0CB2\u0CCD\u0CB2?",
          MissingArgFor: "%1 \u0C87\u0C97\u0CC6 \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6",
          ExtraAlignTab: "\\cases \u0CAA\u0CA0\u0CCD\u0CAF\u0CA6\u0CB2\u0CCD\u0CB2\u0CBF \u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 \u0C85\u0CB2\u0CC8\u0CA8\u0CCD\u0CAE\u0CC6\u0C82\u0C9F\u0CCD \u0C9F\u0CCD\u0CAF\u0CBE\u0CAC\u0CCD.",
          BracketMustBeDimension: "%1 \u0C87\u0C97\u0CC6 \u0C87\u0CA6\u0CCD\u0CA6 \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6 \u0C85\u0CB3\u0CA4\u0CC6\u0CB5\u0CC6 \u0C87\u0CB0\u0CAC\u0CC6\u0C95\u0CC1.",
          InvalidEnv: "\u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0CAA\u0CB0\u0CCD\u0CAF\u0CBE\u0CB5\u0CB0\u0CA3 \u0CB9\u0CC6\u0CB8\u0CB0\u0CC1:  %1",
          UnknownEnv: "\u0C85\u0C9C\u0CCD\u0C9E\u0CBE\u0CA4 \u0CAA\u0CB0\u0CCD\u0CAF\u0CBE\u0CB5\u0CB0\u0CA3 '%1'",
          ExtraCloseLooking: "%1 \u0CB9\u0CC1\u0CA1\u0CC1\u0C95\u0CC1\u0CB5\u0CBE\u0C97 \u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 \u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CBF\u0CA6 \u0CAC\u0CCD\u0CB0\u0CC7\u0CB8\u0CCD",
          MissingCloseBracket: "%1 \u0C87\u0C97\u0CC6 \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6\u0C97\u0CC6 \u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CC6\u0CA6\u0CC6 ']' \u0CB8\u0CBF\u0C97\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2",
          MissingOrUnrecognizedDelim: "%1 \u0C87\u0C97\u0CC6 \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \u0C85\u0CA5\u0CB5 \u0C85\u0CB0\u0CCD\u0CA5 \u0C86\u0C97\u0CA6 \u0C87\u0CA6\u0CCD\u0CA6 \u0CAC\u0CCD\u0CB0\u0C9A\u0CCD\u0C95\u0CC6\u0CA4\u0CCD.",
          MissingDimOrUnits: "%1 \u0C87\u0C97\u0CC6 \u0CB8\u0CBF\u0C97\u0CA6\u0CC7 \u0C87\u0CA6\u0CCD\u0CA6 \u0C85\u0CB2\u0CBF\u0CA4\u0CC6 \u0C85\u0CA5\u0CB5 \u0C85\u0CA6\u0CB0 \u0C98\u0C9F\u0C95\u0C97\u0CB3\u0CC1.",
          TokenNotFoundForCommand: "%1 \u0C87\u0C97\u0CC6 %2 \u0CB8\u0CBF\u0C97\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2.",
          MathNotTerminated: "\u0C97\u0CA3\u0CBF\u0CA4\u0CB5\u0CA8\u0CCD\u0CA8\u0CC1 \u0CAA\u0CA5\u0CCD\u0CAF \u0CB8\u0C82\u0CA6\u0CC2\u0C95 \u0CB5\u0CB3\u0C97\u0CC6 \u0CAE\u0CC1\u0C97\u0CBF\u0CB8\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2",
          IllegalMacroParam: "\u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0CAE\u0CCD\u0CAF\u0CBE\u0C95\u0CCD\u0CB0\u0CCB \u0CAA\u0CCD\u0CAF\u0CBE\u0CB0\u0CBE\u0CAE\u0CC0\u0C9F\u0CB0\u0CCD \u0C87\u0C97\u0CC6 \u0C89\u0CB2\u0CCD\u0CB2\u0CC7\u0C96",
          MaxBufferSize: "\u0CAE\u0CBE\u0CA4 \u0C9C\u0C95\u0CCD\u0CB7\u0CCD  \u0C86\u0C82\u0CA4\u0CB0\u0CBF\u0C95 \u0CAC\u0CAB\u0CB0\u0CCD \u0C97\u0CBE\u0CA4\u0CCD\u0CB0\u0CA6 \u0CAE\u0CC0\u0CB0\u0CBF\u0CA6\u0CC6; \u0C92\u0C82\u0CA6\u0CC1 \u0CAA\u0CC1\u0CA8\u0CB0\u0CBE\u0CB5\u0CB0\u0CCD\u0CA4\u0CBF\u0CA4 \u0CAE\u0CCD\u0CAF\u0CBE\u0C95\u0CCD\u0CB0\u0CCB \u0C95\u0CB0\u0CC6 \u0C87\u0CB2\u0CCD\u0CB2?",
          CommandNotAllowedInEnv: "%2 \u0CAA\u0CB0\u0CCD\u0CAF\u0CBE\u0CB5\u0CB0\u0CA3\u0CA6\u0CB2\u0CCD\u0CB2\u0CBF  %1 \u0C85\u0CB5\u0C95\u0CBE\u0CB6 \u0C85\u0CB2\u0CCD\u0CB2",
          MultipleLabel: "%1 \u0C85\u0CA8\u0CC7\u0C95 \u0CB8\u0CB2 \u0CB5\u0CCD\u0CAF\u0CBE\u0C96\u0CCD\u0CAF\u0CBE\u0CA8\u0CBF\u0CB8\u0CB2\u0CC1 \u0C86\u0C97\u0CBF\u0C95\u0CCA\u0C82\u0CA1\u0CC1 \u0C89\u0C82\u0C9F\u0CC1",
          CommandAtTheBeginingOfLine: "%1 \u0CAA\u0C82\u0C95\u0CCD\u0CA4\u0CBF\u0CA6 \u0CAE\u0CCA\u0CA6\u0CB2\u0CC1 \u0CAC\u0CB0\u0CAC\u0CC7\u0C95\u0CC1",
          IllegalAlign: "%1 \u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0CB8\u0CBE\u0CB2\u0CC1\u0C97\u0CC2\u0CA1\u0CBF\u0C95\u0CC6 \u0CB9\u0CC7\u0CB3\u0CBF \u0CB9\u0CCB\u0C97\u0CBF\u0CA6\u0CC6",
          BadMathStyleFor: "%1 \u0C87\u0C97\u0CC6 \u0C95\u0CC6\u0C9F\u0CCD\u0C9F \u0C97\u0CA3\u0CBF\u0CA4\u0CA6\u0CCD\u0CA6\u0CC1 \u0CB6\u0CC8\u0CB2\u0CBF \u0C89\u0C82\u0C9F\u0CC1",
          PositiveIntegerArg: "%1 \u0C87\u0C97\u0CC6 \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6 \u0CA7\u0CA8\u0CBE\u0CA4\u0CCD\u0CAE\u0C95 \u0CAA\u0CC2\u0CB0\u0CCD\u0CA3\u0CBE\u0C82\u0C95 \u0C87\u0CB0\u0CAC\u0CC7\u0C95\u0CC1",
          ErroneousNestingEq: "\u0C8E\u0C95\u0CBC\u0CC1\u0C85\u0CA4\u0CBF\u0C92\u0CA8\u0CCD \u0CB0\u0C9A\u0CA8\u0CC6\u0C97\u0CB3 \u0CA4\u0CC3\u0CA4\u0CBF\u0CB5\u0CBE\u0CA6 \u0CA8\u0CC6\u0CB8\u0CCD\u0CA4\u0CBF\u0C82\u0C97\u0CCD",
          MultlineRowsOneCol: "%1 \u0CAA\u0CB0\u0CCD\u0CAF\u0CBE\u0CB5\u0CB0\u0CA3\u0CA6 \u0CB5\u0CB3\u0C97\u0CC6 \u0C87\u0CA6\u0CCD\u0CA6 \u0CB8\u0CBE\u0CB2\u0CC1\u0C97\u0CB3\u0CBF\u0C97\u0CC6 \u0C92\u0C82\u0CA6\u0CC7 \u0CB8\u0CCD\u0CA4\u0C82\u0CAD \u0C87\u0CB0\u0CAC\u0CC6\u0C95\u0CC1.",
          MultipleBBoxProperty: "%2 \u0C87\u0CA8 \u0CB5\u0CB3\u0C97\u0CC6 %1 \u0C8E\u0CB0\u0CA1\u0CC1 \u0CB8\u0CB2 \u0CB9\u0CC7\u0CB3\u0CBF \u0CB9\u0CCA\u0C97\u0CBF\u0CA6\u0CC6.",
          InvalidBBoxProperty: "'%1'  \u0C92\u0C82\u0CA6\u0CC1 \u0CAC\u0CA3\u0CCD\u0CA3, \u0C89\u0CAC\u0CCD\u0CAC\u0CBF\u0CB8\u0CB5 \u0C85\u0CB2\u0CBF\u0CA4\u0CC6, \u0C85\u0CA5\u0CB5\u0CBE \u0CB6\u0CC8\u0CB2\u0CBF \u0CB9\u0CBE\u0C97\u0CC6 \u0C95\u0CBE\u0CA8\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2.",
          ExtraEndMissingBegin: "\u0CB9\u0CC6\u0C9A\u0CCD\u0C9A\u0CBF\u0CA8 %1 \u0C85\u0CA5\u0CB5 \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \\begingroup",
          GlobalNotFollowedBy: "%1 \u0C87\u0CA8 \u0CAE\u0CC1\u0C82\u0CA6\u0CC6  \\let, \\def, \\def, \u0C85\u0CA5\u0CB5  \\newcommand \u0CAC\u0CB0\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2.",
          UndefinedColorModel: "\u0CAC\u0CA3\u0CCD\u0CA3\u0CA6 \u0CAE\u0CBE\u0CA6\u0CB0\u0CBF '%1' \u0CB9\u0CC7\u0CB3\u0CBF \u0CB9\u0CCB\u0C97\u0CB2\u0CBF\u0CB2\u0CCD\u0CB2.",
          ModelArg1: "%1 \u0CAE\u0CBE\u0CA6\u0CB0\u0CBF\u0C97\u0CC6 \u0CAC\u0CA3\u0CCD\u0CA3\u0CA6 \u0CE9 (3) \u0C85\u0C82\u0C95\u0C97\u0CB3\u0CC1 \u0CAC\u0CC6\u0C95\u0C97\u0CC1\u0CA4\u0CA6\u0CC6.",
          InvalidDecimalNumber: "\u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0CA6\u0CB6\u0CAE\u0CB3\u0CA8\u0CCD\u0CB6",
          ModelArg2: "%1 \u0CAE\u0CBE\u0CA6\u0CB0\u0CBF\u0C97\u0CC6 \u0CAC\u0CA3\u0CCD\u0CA3\u0CA6 \u0C85\u0CA8\u0CCD\u0C96\u0C97\u0CB3\u0CC1  %2 \u0CAE\u0CA4\u0CCD\u0CA4\u0CC1  %3 \u0C87\u0CA8 \u0CAE\u0CA7\u0CCD\u0CAF \u0CA6\u0CB2\u0CCD\u0CB2\u0CBF \u0C87\u0CB0\u0CAC\u0CC6\u0C95\u0CC1.",
          InvalidNumber: "\u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0C85\u0C82\u0C95.",
          NewextarrowArg1: "%1 \u0C87\u0C97\u0CC6 \u0C92\u0C82\u0CA6\u0CA8\u0CC7 \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6 \u0CA8\u0CBF\u0CAF\u0C82\u0CA4\u0CCD\u0CB0\u0CA3 \u0C85\u0CA8\u0CC1\u0C95\u0CCD\u0CB0\u0CAE \u0C87\u0CB0 \u0CAC\u0CC6\u0C95\u0CC1.",
          NewextarrowArg2: "%1 \u0C87\u0C97\u0CC6 \u0C8E\u0CB0\u0CA1\u0CA8\u0CC7  \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6 \u0C92\u0C82\u0CA6\u0CC1 \u0C95\u0CC6\u0CAE\u0CCD\u0CAE \u0CA6\u0CBF\u0C82\u0CA6 \u0CAC\u0CC7\u0CB0\u0CC6 \u0C86\u0CA6 \u0C8E\u0CB0\u0CA1\u0CC1 \u0CAA\u0CC2\u0CB0\u0CCD\u0CA3\u0CBE\u0C82\u0C95 \u0C87\u0CB0\u0CAC\u0CC6\u0C95\u0CC1.",
          NewextarrowArg3: "%1 \u0C87\u0C97\u0CC6 \u0CAE\u0CC2\u0CB0\u0CA8\u0CC7 \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6 \u0C92\u0C82\u0CA6\u0CC1 \u0CAF\u0CC1\u0CA8\u0CBF\u0C95\u0CCB\u0CA1\u0CCD \u0CB8\u0CE6\u0C95\u0CC7\u0CA4 \u0C87\u0CB0\u0CAC\u0CC6\u0C95\u0CC1.",
          NoClosingChar: "\u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CC1\u0CB5 %1 \u0CB8\u0CBF\u0C97\u0CC1\u0CA4 \u0C87\u0CB2\u0CCD\u0CB2.",
          IllegalControlSequenceName: "%1 \u0C87\u0C97\u0CC6 \u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0CA8\u0CBF\u0CAF\u0C82\u0CA4\u0CCD\u0CB0\u0CA3  \u0C85\u0CA8\u0CC1\u0C95\u0CCD\u0CB0\u0CAE",
          IllegalParamNumber: "%1 \u0C85\u0CB2\u0CCD\u0CB2\u0CBF \u0CA8\u0CBF\u0CAF\u0CA4\u0CBE\u0C82\u0C95\u0C97\u0CB3  \u0C92\u0C82\u0CA6\u0CC1 \u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0CB8\u0C82\u0C96\u0CCD\u0CAF\u0CBE  \u0CB9\u0CC7\u0CB3\u0CBF \u0CB9\u0CCB\u0C97\u0CBF\u0CA6\u0CCD\u0CA6\u0CC6",
          MissingCS: "%1 \u0C86\u0CA6\u0CAE\u0CC7\u0CB2\u0CC6 \u0C92\u0C82\u0CA6\u0CC1 \u0CA8\u0CBF\u0CAF\u0C82\u0CA4\u0CCD\u0CB0\u0CA3 \u0C95\u0CCD\u0CB0\u0CAE \u0CAC\u0CB0\u0CAC\u0CC7\u0C95\u0CC1",
          CantUseHash2: "%1 \u0C87\u0C97\u0CC6 \u0C9F\u0CC6\u0C82\u0CAA\u0CCD\u0CB2\u0CC6\u0C9F\u0CCD \u0C85\u0CB2\u0CCD\u0CB2\u0CBF  # \u0C87\u0CA8 \u0C85\u0C95\u0CCD\u0CB0\u0CAE \u0C89\u0CAA\u0CAF\u0CCB\u0C97",
          SequentialParam: " %1 \u0C87\u0C97\u0CC6 \u0CA8\u0CBF\u0CAF\u0CA4\u0CBE\u0C82\u0C95 \u0C92\u0C82\u0CA6\u0CC1 \u0C95\u0CCD\u0CB0\u0CAE \u0CAA\u0CCD\u0CB0\u0C95\u0CBE\u0CB0 \u0CB8\u0C82\u0C96\u0CCD\u0CAF\u0CBE \u0CA4\u0CC6\u0C95\u0CCA\u0CB3\u0CC1\u0CAC\u0CC7\u0C95\u0CC1",
          MissingReplacementString: "%1 \u0C87\u0CA8 \u0CB5\u0CCD\u0CAF\u0CBE\u0C96\u0CCD\u0CAF\u0CBE\u0CA6\u0CB2\u0CCD\u0CB2\u0CBF \u0C95\u0CBE\u0CA3\u0CC6\u0CAF\u0CBE\u0C97\u0CBF\u0CA6 \u0CAC\u0CA6\u0CB2\u0CBF \u0CB9\u0C97\u0CCD\u0C97",
          MismatchUseDef: "%1 \u0C87\u0CA8 \u0C89\u0CAA\u0CAF\u0CCB\u0C97 \u0C85\u0CA6\u0CB0 \u0CB5\u0CCD\u0CAF\u0CBE\u0C96\u0CCD\u0CAF\u0CB5\u0CA8\u0CCD\u0CA8\u0CC1  \u0CB8\u0CB0\u0CBF\u0C9C\u0CC7\u0CA1\u0CBF \u0C86\u0C97\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2",
          RunawayArgument: "%1 \u0C87\u0C97\u0CC6 \u0C93\u0CA6\u0CC1 \u0CB9\u0CCB\u0C97\u0CC1\u0CB5 \u0C9A\u0CB0\u0CCD\u0C9A\u0CC6 ?",
          NoClosingDelim: "%1 \u0C87\u0C97\u0CC6 \u0CAE\u0CC1\u0C9A\u0CCD\u0C9A\u0CB5 \u0CAC\u0CCD\u0CB0\u0CBE\u0C95\u0CC6\u0C9F\u0CCD \u0CB8\u0CBF\u0C97\u0CC1\u0CA6\u0CBF\u0CB2\u0CCD\u0CB2."
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/kn/TeX.js");
