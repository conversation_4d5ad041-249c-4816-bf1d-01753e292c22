/*************************************************************
 *
 *  MathJax/localization/sl/MathML.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("sl","MathML",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          BadMglyph: "Neveljaven mglyph: %1",
          BadMglyphFont: "Neveljavna pisava: %1",
          MathPlayer: "MathJax ni mogel nastaviti programa MathPlayer.\n\n\u010Ce MathPlayer ni name\u0161\u010Den, ga morate najprej namestiti. Sicer morda njegov zahon prepre\u010Dujejo va\u0161e varnostne nastavitve. Uporabite element Internetne mo\u017Enosti [Internet Options] pod menijem Orodja [Tools] in izberite zavihek Varnost [Security], nato pritisnite gumb Prilagojena raven [Custom Level]. Preverite, alo so nastavitve za 'Za\u017Eeni krmilne elemente ActiveX' in 'Binarna in skriptna vedenja' omogo\u010Dene.\n\nTrenutno boste namesto matematike videli sporo\u010Dila o napakah.",
          CantCreateXMLParser: "MathJax ne more ustvariti raz\u010Dlenjevalnika XML za MathML. Preverite, ali je varnostna nastavitev 'Krmilni elementi Script ActiveX ozna\u010Deni kot varni za skriptiranje' omogo\u010Dena (da to preverite, v meniju Orodja [Tools] uporabite element Internetne mo\u017Enosti [Internet Options], nato izberite plo\u0161\u010Do Varnost [Security], nato pritisnite gumb Prilagojena raven [Custom Level]).",
          UnknownNodeType: "Neznana vrsta vozli\u0161\u010Da: %1",
          UnexpectedTextNode: "Nepri\u010Dakovano tekstovno vozli\u0161\u010De: %1",
          ErrorParsingMathML: "Napaka pri raz\u010Dlenjevanju MathML",
          ParsingError: "Napaka pri raz\u010Dlenjevanju MathML: %1",
          MathMLSingleElement: "MathML mora biti oblikovan z enim samim elementom",
          MathMLRootElement: "MathML mora biti oblikovan z elementom \u003Cmath\u003E, ne s korenom %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/sl/MathML.js");
