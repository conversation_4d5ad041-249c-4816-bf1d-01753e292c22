/*************************************************************
 *
 *  MathJax/localization/he/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("he","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "\u05DC\u05D4\u05E6\u05D9\u05D2 \u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA \u05D1\u05EA\u05D5\u05E8",
          MathMLcode: "\u05E7\u05D5\u05D3 MathML",
          OriginalMathML: "MathML \u05DE\u05E7\u05D5\u05E8\u05D9",
          TeXCommands: "\u05E4\u05E7\u05D5\u05D3\u05D5\u05EA TeX",
          AsciiMathInput: "\u05E7\u05DC\u05D8 AsciiMathML",
          Original: "\u05D4\u05E6\u05D5\u05E8\u05D4 \u05D4\u05DE\u05E7\u05D5\u05E8\u05D9\u05EA",
          ErrorMessage: "\u05D4\u05D5\u05D3\u05E2\u05EA \u05E9\u05D2\u05D9\u05D0\u05D4",
          Annotation: "\u05E4\u05D9\u05E8\u05D5\u05E9",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Content MathML",
          OpenMath: "OpenMath",
          texHints: "\u05DC\u05D4\u05E6\u05D9\u05D2 \u05E8\u05DE\u05D6\u05D9\u05DD \u05E9\u05DC TeX \u05D1\u05BEMathML",
          Settings: "\u05D4\u05D2\u05D3\u05E8\u05D5\u05EA \u05DE\u05EA\u05DE\u05D8\u05D9\u05E7\u05D4",
          ZoomTrigger: "\u05DE\u05D4 \u05D2\u05D5\u05E8\u05DD \u05DC\u05E7\u05D9\u05E8\u05D5\u05D1",
          Hover: "\u05DE\u05E2\u05D1\u05E8 \u05E2\u05DB\u05D1\u05E8",
          Click: "\u05DC\u05D7\u05D9\u05E6\u05D4",
          DoubleClick: "\u05DC\u05D7\u05D9\u05E6\u05D4 \u05DB\u05E4\u05D5\u05DC\u05D4",
          NoZoom: "\u05DC\u05DC\u05D0 \u05E7\u05D9\u05E8\u05D5\u05D1",
          TriggerRequires: "\u05D4\u05E4\u05E2\u05DC\u05EA \u05D4\u05E7\u05D9\u05E8\u05D5\u05D1 \u05D3\u05D5\u05E8\u05E9\u05EA:",
          Option: "Option",
          Alt: "Alt",
          Command: "Command",
          Control: "Ctrl",
          Shift: "Shift",
          ZoomFactor: "\u05E8\u05DE\u05EA \u05E7\u05D9\u05E8\u05D5\u05D1",
          Renderer: "\u05DE\u05E6\u05D9\u05D2 \u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA",
          MPHandles: "\u05DC\u05D0\u05E4\u05E9\u05E8 \u05DC\u05BEMathPlayer \u05DC\u05D8\u05E4\u05DC \u05D1\u05BE:",
          MenuEvents: "\u05D0\u05D9\u05E8\u05D5\u05E2\u05D9 \u05EA\u05E4\u05E8\u05D9\u05D8",
          MouseEvents: "\u05D0\u05D9\u05E8\u05D5\u05E2\u05D9 \u05E2\u05DB\u05D1\u05E8",
          MenuAndMouse: "\u05D0\u05D9\u05E8\u05D5\u05E2\u05D9 \u05E2\u05DB\u05D1\u05E8 \u05D5\u05D0\u05D9\u05E8\u05D5\u05E2\u05D9 \u05EA\u05E4\u05E8\u05D9\u05D8",
          FontPrefs: "\u05D4\u05E2\u05D3\u05E4\u05D5\u05EA \u05D2\u05D5\u05E4\u05E0\u05D9\u05DD",
          ForHTMLCSS: "\u05E2\u05D1\u05D5\u05E8 HTML-CSS:",
          Auto: "\u05D0\u05D5\u05D8\u05D5\u05DE\u05D8\u05D9",
          TeXLocal: "TeX (\u05DE\u05E7\u05D5\u05DE\u05D9)",
          TeXWeb: "TeX (\u05D1\u05E8\u05E9\u05EA)",
          TeXImage: "TeX (\u05EA\u05DE\u05D5\u05E0\u05D4)",
          STIXLocal: "STIX (\u05DE\u05E7\u05D5\u05DE\u05D9)",
          STIXWeb: "STIX (\u05D1\u05E8\u05E9\u05EA)",
          AsanaMathWeb: "Asana Math (\u05D1\u05E8\u05E9\u05EA)",
          GyrePagellaWeb: "Gyre Pagella (\u05D1\u05E8\u05E9\u05EA)",
          GyreTermesWeb: "Gyre Termes (\u05D1\u05E8\u05E9\u05EA)",
          LatinModernWeb: "Latin Modern (\u05D1\u05E8\u05E9\u05EA)",
          NeoEulerWeb: "Neo Euler (\u05D1\u05E8\u05E9\u05EA)",
          ContextMenu: "\u05EA\u05E4\u05E8\u05D9\u05D8 \u05D4\u05B6\u05E7\u05E9\u05E8",
          Browser: "\u05D3\u05E4\u05D3\u05E4\u05DF",
          Scale: "\u05DC\u05D4\u05EA\u05D0\u05D9\u05DD \u05D0\u05EA \u05D4\u05D2\u05D5\u05D3\u05DC \u05E9\u05DC \u05DB\u05DC \u05D4\u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA...",
          Discoverable: "\u05DC\u05D4\u05D0\u05D9\u05E8 \u05D1\u05DE\u05E2\u05D1\u05E8 \u05E2\u05DB\u05D1\u05E8",
          Locale: "\u05E9\u05E4\u05D4",
          LoadLocale: "\u05DC\u05D8\u05E2\u05D5\u05DF \u05DE\u05DB\u05EA\u05D5\u05D1\u05EA...",
          About: "\u05D0\u05D5\u05D3\u05D5\u05EA MathJax",
          Help: "\u05E2\u05D6\u05E8\u05D4 \u05E9\u05DC MathJax",
          localTeXfonts: "\u05DE\u05E9\u05DE\u05E9\u05D9\u05DD \u05D2\u05D5\u05E4\u05E0\u05D9 TeX \u05DE\u05E7\u05D5\u05DE\u05D9\u05D9\u05DD",
          webTeXfonts: "\u05DE\u05E9\u05DE\u05E9\u05D9\u05DD \u05D2\u05D5\u05E4\u05E0\u05D9 TeX \u05DE\u05D4\u05E8\u05E9\u05EA",
          imagefonts: "\u05DE\u05E9\u05DE\u05E9\u05D9\u05DD \u05D2\u05D5\u05E4\u05E0\u05D9 \u05EA\u05DE\u05D5\u05E0\u05D4",
          localSTIXfonts: "\u05DE\u05E9\u05DE\u05E9\u05D9\u05DD \u05D2\u05D5\u05E4\u05E0\u05D9 STIX \u05DE\u05E7\u05D5\u05DE\u05D9\u05D9\u05DD",
          webSVGfonts: "\u05DE\u05E9\u05DE\u05E9\u05D9\u05DD \u05D2\u05D5\u05E4\u05E0\u05D9 SVG \u05DE\u05D4\u05E8\u05E9\u05EA",
          genericfonts: "\u05DE\u05E9\u05DE\u05E9\u05D9\u05DD \u05D2\u05D5\u05E4\u05E0\u05D9 \u05D9\u05D5\u05E0\u05D9\u05E7\u05D5\u05D3 \u05DB\u05DC\u05DC\u05D9\u05D9\u05DD",
          wofforotffonts: "\u05D2\u05D5\u05E4\u05E0\u05D9 woff \u05D0\u05D5 otf",
          eotffonts: "\u05D2\u05D5\u05E4\u05E0\u05D9 eot",
          svgfonts: "\u05D2\u05D5\u05E4\u05E0\u05D9 svg",
          WebkitNativeMMLWarning: "\u05D1\u05D3\u05E4\u05D3\u05E4\u05DF \u05E9\u05DC\u05DA \u05D0\u05D9\u05DF \u05EA\u05DE\u05D9\u05DB\u05D4 \u05DE\u05D5\u05D1\u05E0\u05D9\u05EA \u05D1\u05BEMathML, \u05D0\u05D6 \u05DE\u05E2\u05D1\u05E8 \u05DC\u05E4\u05DC\u05D8 MathML \u05E2\u05DC\u05D5\u05DC \u05DC\u05D4\u05E4\u05D5\u05DA \u05D0\u05EA \u05D4\u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA \u05DC\u05D1\u05DC\u05EA\u05D9\u05BE\u05E7\u05E8\u05D9\u05D0\u05D5\u05EA",
          MSIENativeMMLWarning: "\u05D0\u05D9\u05E0\u05D8\u05E8\u05E0\u05D8 \u05D0\u05E7\u05E1\u05E4\u05DC\u05D5\u05E8\u05E8 \u05D3\u05D5\u05E8\u05E9 \u05EA\u05D5\u05E1\u05E3 MathPlayer \u05DB\u05D3\u05D9 \u05DC\u05E2\u05D1\u05D3 \u05E4\u05DC\u05D8 MathML.",
          OperaNativeMMLWarning: "\u05D4\u05EA\u05DE\u05D9\u05DB\u05D4 \u05E9\u05DC \u05D0\u05D5\u05E4\u05E8\u05D4 \u05D1\u05BEMathML \u05DE\u05D5\u05D2\u05D1\u05DC\u05EA, \u05D0\u05D6 \u05DE\u05E2\u05D1\u05E8 \u05DC\u05BEMathML \u05E2\u05DC\u05D5\u05DC \u05DC\u05D2\u05E8\u05D5\u05DD \u05DC\u05D7\u05DC\u05E7 \u05DE\u05D4\u05D1\u05D9\u05D8\u05D5\u05D9\u05D9\u05DD \u05DC\u05D4\u05D9\u05D5\u05EA \u05DE\u05D5\u05E6\u05D2\u05D9\u05DD \u05D1\u05D0\u05D5\u05E4\u05DF \u05D2\u05E8\u05D5\u05E2.",
          SafariNativeMMLWarning: "\u05D4\u05EA\u05DE\u05D9\u05DB\u05D4 \u05D4\u05DE\u05D5\u05D1\u05E0\u05D9\u05EA \u05E9\u05DC \u05D4\u05D3\u05E4\u05D3\u05E4\u05DF \u05E9\u05DC\u05DA \u05D1\u05BEMathML \u05D0\u05D9\u05E0\u05D4 \u05DE\u05DE\u05DE\u05E9\u05EA \u05D0\u05EA \u05DB\u05DC \u05D4\u05D9\u05DB\u05D5\u05DC\u05D5\u05EA \u05E9\u05BEMathJax \u05DE\u05E9\u05EA\u05DE\u05E9 \u05D1\u05D4\u05DF, \u05D0\u05D6 \u05D9\u05D9\u05EA\u05DB\u05DF \u05E9\u05D1\u05D9\u05D8\u05D5\u05D9\u05D9\u05DD \u05D0\u05D7\u05D3\u05D9\u05DD \u05DC\u05D0 \u05D9\u05D5\u05E6\u05D2\u05D5 \u05DB\u05E8\u05D0\u05D5\u05D9.",
          FirefoxNativeMMLWarning: "\u05D4\u05EA\u05DE\u05D9\u05DB\u05D4 \u05D4\u05DE\u05D5\u05D1\u05E0\u05D9\u05EA \u05E9\u05DC \u05D4\u05D3\u05E4\u05D3\u05E4\u05DF \u05E9\u05DC\u05DA \u05D1\u05BEMathML \u05D0\u05D9\u05E0\u05D4 \u05DE\u05DE\u05DE\u05E9\u05EA \u05D0\u05EA \u05DB\u05DC \u05D4\u05D9\u05DB\u05D5\u05DC\u05D5\u05EA \u05E9\u05BEMathJax \u05DE\u05E9\u05EA\u05DE\u05E9 \u05D1\u05D4\u05DF, \u05D0\u05D6 \u05D9\u05D9\u05EA\u05DB\u05DF \u05E9\u05D1\u05D9\u05D8\u05D5\u05D9\u05D9\u05DD \u05D0\u05D7\u05D3\u05D9\u05DD \u05DC\u05D0 \u05D9\u05D5\u05E6\u05D2\u05D5 \u05DB\u05E8\u05D0\u05D5\u05D9.",
          MSIESVGWarning: "\u05EA\u05DE\u05D9\u05DB\u05D4 \u05D1\u05BESVG \u05D0\u05D9\u05E0\u05D4 \u05DE\u05DE\u05D5\u05DE\u05E9\u05EA \u05D1\u05D0\u05D9\u05E0\u05D8\u05E8\u05E0\u05D8 \u05D0\u05E7\u05E1\u05E4\u05DC\u05D5\u05E8\u05E8 \u05DC\u05E4\u05E0\u05D9 \u05D2\u05E8\u05E1\u05D4 9 \u05D0\u05D5 \u05DB\u05D0\u05E9\u05E8 \u05D4\u05D3\u05E4\u05D3\u05E4\u05DF \u05E4\u05D5\u05E2\u05DC \u05D1\u05DE\u05E6\u05D1 \u05EA\u05D0\u05D9\u05DE\u05D5\u05EA \u05DC\u05D2\u05E8\u05E1\u05D4 8 \u05D5\u05DE\u05D8\u05D4. \u05DE\u05E2\u05D1\u05E8 \u05DC\u05E4\u05DC\u05D8 \u05D1\u05BESVG \u05D9\u05D2\u05E8\u05D5\u05DD \u05DC\u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA \u05DE\u05EA\u05DE\u05D8\u05D9\u05D5\u05EA \u05DC\u05D0 \u05D4\u05D9\u05D5\u05EA \u05DE\u05D5\u05E6\u05D2\u05D5\u05EA \u05DB\u05E8\u05D0\u05D5\u05D9.",
          LoadURL: "\u05D8\u05E2\u05D9\u05E0\u05EA \u05E0\u05EA\u05D5\u05E0\u05D9 \u05EA\u05E8\u05D2\u05D5\u05DD \u05DE\u05D4\u05DB\u05EA\u05D5\u05D1\u05EA \u05D4\u05D1\u05D0\u05D4:",
          BadURL: "\u05D4\u05DB\u05EA\u05D5\u05D1\u05EA \u05E6\u05E8\u05D9\u05DB\u05D4 \u05DC\u05DB\u05E4\u05E0\u05D5\u05EA \u05DC\u05E7\u05D5\u05D1\u05E5 \u05D2'\u05D0\u05D5\u05D5\u05D4 \u05E1\u05E7\u05E8\u05D9\u05E4\u05D8 \u05E9\u05DE\u05D2\u05D3\u05D9\u05E8 \u05E0\u05EA\u05D5\u05E0\u05D9 \u05EA\u05E8\u05D2\u05D5\u05DD \u05E9\u05DC MathJax. \u05E7\u05D5\u05D1\u05E5 \u05D4\u05D2'\u05D0\u05D5\u05D5\u05E1 \u05E1\u05E7\u05E8\u05D9\u05E4\u05D8 \u05D0\u05DE\u05D5\u05E8 \u05DC\u05D4\u05E1\u05EA\u05D9\u05D9\u05DD \u05D1\u05BEjs.",
          BadData: "\u05E0\u05DB\u05E9\u05DC\u05D4 \u05D8\u05E2\u05D9\u05E0\u05EA \u05E0\u05EA\u05D5\u05E0\u05D9 \u05EA\u05E8\u05D2\u05D5\u05DD \u05DE\u05BE%1",
          SwitchAnyway: "\u05DC\u05E9\u05E0\u05D5\u05EA \u05D0\u05EA \u05D4\u05DE\u05E6\u05D9\u05D2 \u05D1\u05DB\u05DC \u05D6\u05D0\u05EA?\n\n(\u05D9\u05E9 \u05DC\u05DC\u05D7\u05D5\u05E5 \u05D0\u05D9\u05E9\u05D5\u05E8 \u05DC\u05DE\u05E2\u05D1\u05E8 \u05D0\u05D5 \u05D1\u05D9\u05D8\u05D5\u05DC \u05DC\u05D4\u05DE\u05E9\u05DA \u05E2\u05DD \u05D4\u05E6\u05D9\u05D2 \u05D4\u05E0\u05D5\u05DB\u05D7\u05D9)",
          ScaleMath: "\u05DC\u05D4\u05EA\u05D0\u05D9\u05DD \u05D0\u05EA \u05D4\u05D2\u05D5\u05D3\u05DC \u05E9\u05DC \u05DB\u05DC \u05D4\u05E0\u05D5\u05E1\u05D7\u05D0\u05D5\u05EA (\u05D9\u05D7\u05E1\u05D9\u05EA \u05DC\u05D8\u05E7\u05E1\u05D8 \u05D4\u05E1\u05DE\u05D5\u05DA) \u05D1\u05BE:",
          NonZeroScale: "\u05D4\u05D2\u05D5\u05D3\u05DC \u05DC\u05D0 \u05D9\u05DB\u05D5\u05DC \u05DC\u05D4\u05D9\u05D5 \u05D0\u05E4\u05E1",
          PercentScale: "\u05D4\u05D2\u05D5\u05D3\u05DC \u05D0\u05DE\u05D5\u05E8 \u05DC\u05D4\u05D9\u05D5\u05EA \u05D1\u05D0\u05D7\u05D5\u05D6\u05D9\u05DD (\u05DC\u05DE\u05E9\u05DC 120%%)",
          IE8warning: "\u05D6\u05D4 \u05D9\u05DB\u05D1\u05D4 \u05D0\u05EA \u05EA\u05E4\u05E8\u05D9\u05D8 MathJax \u05D5\u05D0\u05EA \u05D0\u05E4\u05E9\u05E8\u05D5\u05D9\u05D5\u05EA \u05D4\u05E7\u05D9\u05E8\u05D5\u05D1, \u05D0\u05D1\u05DC \u05D0\u05E4\u05E9\u05E8 \u05DC\u05E2\u05E9\u05D5\u05EA \u05DC\u05D7\u05D9\u05E6\u05D4 \u05E2\u05DD Alt \u05E2\u05DC \u05D1\u05D9\u05D8\u05D5\u05D9 \u05DB\u05D3\u05D9 \u05DC\u05E7\u05D1\u05DC \u05D8\u05EA \u05EA\u05E4\u05E8\u05D9\u05D8 M\uFFFDathJax \u05D1\u05DE\u05E7\u05D5\u05DD \u05D6\u05D4.\n\n\u05D4\u05D0\u05DD \u05D1\u05D0\u05DE\u05EA \u05DC\u05E9\u05E0\u05D5\u05EA \u05D0\u05EA \u05D4\u05D4\u05D2\u05D3\u05E8\u05D5\u05EA \u05E9\u05DC MathJax?",
          IE9warning: "\u05EA\u05E4\u05E8\u05D9\u05D8 \u05D4\u05D4\u05E7\u05E9\u05E8 \u05E9\u05DC MathJax \u05D9\u05DB\u05D5\u05D1\u05D4, \u05D0\u05D1\u05DC \u05D0\u05E4\u05E9\u05E8 \u05DC\u05E2\u05E9\u05D5\u05EA \u05DC\u05D7\u05D9\u05E6\u05D4 \u05E2\u05DD Alt \u05E2\u05DC \u05D1\u05D9\u05D8\u05D5\u05D7 \u05DB\u05D3\u05D9 \u05DC\u05E7\u05D1\u05DC \u05D0\u05EA \u05EA\u05E4\u05E8\u05D9\u05D8 MathJax.",
          NoOriginalForm: "\u05D4\u05E6\u05D5\u05E8\u05D4 \u05D4\u05DE\u05E7\u05D5\u05E8\u05D9\u05EA \u05D0\u05D9\u05E0\u05D4 \u05D6\u05DE\u05D9\u05E0\u05D4",
          Close: "\u05E1\u05D2\u05D9\u05E8\u05D4",
          EqSource: "\u05DE\u05E7\u05D5\u05E8 \u05D4\u05DE\u05E9\u05D5\u05D5\u05D0\u05D4 \u05E9\u05DC MathJax",
          CloseAboutDialog: "\u05E1\u05D2\u05D9\u05E8\u05EA \u05D7\u05DC\u05D5\u05DF \u05D3\u05D5\u05BE\u05E9\u05D9\u05D7 \u05D0\u05D5\u05D3\u05D5\u05EA MathJax",
          FastPreview: "\u05EA\u05E6\u05D5\u05D2\u05D4 \u05DE\u05E7\u05D3\u05D9\u05DE\u05D4 \u05DE\u05D4\u05D9\u05E8\u05D4",
          AssistiveMML: "MathML \u05E0\u05D2\u05D9\u05E9",
          InTabOrder: "\u05DC\u05DB\u05DC\u05D5\u05DC \u05D1\u05E1\u05D3\u05E8 \u05D4\u05D8\u05D0\u05D1\u05D9\u05DD"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/he/MathMenu.js");
