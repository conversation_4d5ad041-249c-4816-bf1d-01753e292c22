/*************************************************************
 *
 *  MathJax/localization/zh-hans/MathML.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("zh-hans","MathML",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          BadMglyph: "\u9519\u8BEF\u7684\u79FB\u52A8\u5B57\u5F62\uFF1A%1",
          BadMglyphFont: "\u9519\u8BEF\u5B57\u4F53\uFF1A%1",
          MathPlayer: "MathJax\u65E0\u6CD5\u542F\u7528MathPlayer\u3002\n\n\u5982\u679C\u8FD8\u6CA1\u6709\u5B89\u88C5MathPlayer\uFF0C\u60A8\u5FC5\u987B\u5148\u5B89\u88C5MathPlayer\u3002\u5982\u679C\u60A8\u7684\u5B89\u5168\u8BBE\u7F6E\u6709\u53EF\u80FD\u4F1A\u963B\u6B62ActiveX\u7684\u8FD0\u884C\uFF0C\u8BF7\u5728\u5DE5\u5177\u83DC\u5355\u4E0B\u7684Internet \u9009\u9879\u4E2D\u9009\u62E9\u5B89\u5168\u6807\u7B7E\u7136\u540E\u70B9\u51FB\u81EA\u5B9A\u4E49\u7EA7\u522B\uFF0C\u68C0\u67E5\u201C\u4E8C\u8FDB\u5236\u6587\u4EF6\u548C\u811A\u672C\u884C\u4E3A\u201D\u548C\u201C\u8FD0\u884C ActiveX \u63A7\u4EF6\u201D\u662F\u5426\u5DF2\u542F\u7528\u3002\n\n\u76EE\u524D\u60A8\u770B\u5230\u7684\u53EA\u80FD\u662F\u9519\u8BEF\u4FE1\u606F\u800C\u975E\u6392\u7248\u6570\u5F0F\u3002",
          CantCreateXMLParser: "MathJax\u65E0\u6CD5\u4E3AMathML\u521B\u5EFAXML\u89E3\u6790\u5668\u3002\u8BF7\u68C0\u67E5\u5B89\u5168\u8BBE\u7F6E\n\u201C\u5BF9\u6807\u8BB0\u4E3A\u53EF\u5B89\u5168\u6267\u884C\u811A\u672C\u7684 Active \u63A7\u4EF6\u6267\u884C\u811A\u672C\u201D\n\u662F\u5426\u5DF2\u5F00\u542F\uFF08\u5728\u5DE5\u5177\u4E2D\u5F00\u542FInternet \u9009\u9879\u7136\u540E\u9009\u62E9\u5B89\u5168\n\u9762\u677F\uFF0C\u70B9\u81EA\u5B9A\u4E49\u7EA7\u522B\u6309\u94AE\u627E\u5230\u8FD9\u4E2A\u9009\u9879\u5E76\u5F00\u542F\uFF09\u3002\n\nMathJax\u5C06\u65E0\u6CD5\u5904\u7406MathML\u7B49\u5F0F",
          UnknownNodeType: "\u672A\u77E5\u7684\u8282\u70B9\u7C7B\u578B\uFF1A%1",
          UnexpectedTextNode: "\u610F\u5916\u7684\u6587\u672C\u8282\u70B9\uFF1A%1",
          ErrorParsingMathML: "\u89E3\u6790MathML\u65F6\u51FA\u9519",
          ParsingError: "\u89E3\u6790MathML\u65F6\u51FA\u9519\uFF1A%1",
          MathMLSingleElement: "MathML\u5FC5\u987B\u7528\u5355\u4E2A\u5143\u7D20\u5EFA\u7ACB",
          MathMLRootElement: "MathML\u5FC5\u987B\u7528\u003Cmath\u003E\u5143\u7D20\u5EFA\u7ACB\uFF0C\u800C\u4E0D\u662F%1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/zh-hans/MathML.js");
