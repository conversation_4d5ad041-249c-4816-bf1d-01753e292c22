/*************************************************************
 *
 *  MathJax/localization/fa/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("fa","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "\u0646\u0645\u0627\u06CC\u0634 \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A \u0628\u0647 \u0639\u0646\u0648\u0627\u0646",
          MathMLcode: "\u06A9\u062F MathML",
          OriginalMathML: "MathML \u0627\u0635\u0644\u06CC",
          TeXCommands: "\u062F\u0633\u062A\u0648\u0631\u0627\u062A \u062A\u06A9",
          AsciiMathInput: "\u0648\u0631\u0648\u062F\u06CC AsciiMathML",
          Original: "\u062D\u0627\u0644\u062A \u0627\u0635\u0644\u06CC",
          ErrorMessage: "\u067E\u06CC\u0627\u0645 \u062E\u0637\u0627",
          Annotation: "\u06CC\u0627\u062F\u062F\u0627\u0634\u062A",
          TeX: "\u062A\u06A9",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "MathML \u0645\u062D\u062A\u0648\u0627",
          OpenMath: "OpenMath",
          texHints: "\u0646\u0645\u0627\u06CC\u0634 \u0646\u06A9\u062A\u0647\u200C\u0647\u0627\u06CC \u062A\u06A9 \u062F\u0631 MathML",
          Settings: "\u062A\u0646\u0638\u06CC\u0645\u0627\u062A \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A",
          ZoomTrigger: "\u0645\u0627\u0634\u0647\u0654 \u06A9\u0648\u0686\u06A9/\u0628\u0632\u0631\u06AF\u200C\u0646\u0645\u0627\u06CC\u06CC",
          Hover: "\u0642\u0631\u0627\u0631\u062F\u0627\u062F\u0646 \u0645\u0648\u0634\u0648\u0627\u0631\u0647 \u0628\u0631 \u0631\u0648\u06CC \u0622\u0646",
          Click: "\u06A9\u0644\u06CC\u06A9",
          DoubleClick: "\u062F\u0648\u0628\u0627\u0631 \u06A9\u0644\u06CC\u06A9",
          NoZoom: "\u0628\u062F\u0648\u0646 \u06A9\u0648\u0686\u06A9/\u0628\u0632\u0631\u06AF\u200C\u0646\u0645\u0627\u06CC\u06CC",
          TriggerRequires: "\u062A\u063A\u06CC\u06CC\u0631 \u062D\u0627\u0644\u062A \u0646\u06CC\u0627\u0632\u0645\u0646\u062F \u0627\u0633\u062A \u0628\u0647:",
          Option: "\u06AF\u0632\u06CC\u0646\u0647",
          Alt: "\u062F\u06AF\u0631\u0633\u0627\u0632",
          Command: "\u062F\u0633\u062A\u0648\u0631",
          Control: "\u0645\u0647\u0627\u0631",
          Shift: "\u062A\u0628\u062F\u06CC\u0644",
          ZoomFactor: "\u0636\u0631\u06CC\u0628 \u06A9\u0648\u0686\u06A9/\u0628\u0632\u0631\u06AF\u200C\u0646\u0645\u0627\u06CC\u06CC",
          Renderer: "\u0627\u0631\u0627\u0626\u0647\u200C\u062F\u0647\u0646\u062F\u0647\u0654 \u0631\u06CC\u0627\u0636\u06CC",
          MPHandles: "\u0627\u062C\u0627\u0632\u0647\u200C\u0628\u062F\u0647 MathPlayer \u0645\u062F\u06CC\u0631\u06CC\u062A \u06A9\u0646\u062F:",
          MenuEvents: "\u0631\u0648\u06CC\u062F\u0627\u062F\u0647\u0627\u06CC \u0645\u0646\u0648",
          MouseEvents: "\u0631\u0648\u06CC\u062F\u0627\u062F\u0647\u0627\u06CC \u0645\u0648\u0634\u0648\u0627\u0631\u0647",
          MenuAndMouse: "\u0631\u0648\u06CC\u062F\u0627\u062F\u0647\u0627\u06CC \u0645\u0648\u0634\u0648\u0627\u0631\u0647 \u0648 \u0645\u0646\u0648",
          FontPrefs: "\u062A\u0631\u062C\u06CC\u062D\u0627\u062A \u0642\u0644\u0645",
          ForHTMLCSS: "\u0628\u0631\u0627\u06CC \u0627\u0686\u200C\u062A\u06CC\u200C\u0627\u0645\u200C\u0627\u0644-\u0633\u06CC\u200C\u0627\u0633\u200C\u0627\u0633:",
          Auto: "\u062E\u0648\u062F\u06A9\u0627\u0631",
          TeXLocal: "\u062A\u06A9 (\u0645\u062D\u0644\u06CC)",
          TeXWeb: "\u062A\u06A9 (\u0648\u0628)",
          TeXImage: "\u062A\u06A9 (\u0646\u06AF\u0627\u0631\u0647)",
          STIXLocal: "STIX (\u0645\u062D\u0644\u06CC)",
          STIXWeb: "STIX (\u0648\u0628\u200C\u0633\u0627\u06CC\u062A)",
          AsanaMathWeb: "\u0631\u06CC\u0627\u0636\u06CC \u0622\u0633\u0627\u0646\u0627 (\u0648\u0628\u200C\u0633\u0627\u06CC\u062A)",
          GyrePagellaWeb: "\u06AF\u0631\u06CC \u067E\u0627\u06AF\u0644\u0627 (\u0648\u0628\u200C\u0633\u0627\u06CC\u062A)",
          GyreTermesWeb: "\u06AF\u06CC\u0631 \u062A\u0631\u0645\u0632 (\u0648\u0628\u200C\u0633\u0627\u06CC\u062A)",
          LatinModernWeb: "\u0644\u0627\u062A\u06CC\u0646 \u0645\u062F\u0631\u0646 (\u0648\u0628\u200C\u0633\u0627\u06CC\u062A)",
          NeoEulerWeb: "\u0646\u0626\u0648 \u0627\u0648\u06CC\u0644\u0631 (\u0648\u0628\u200C\u0633\u0627\u06CC\u062A)",
          ContextMenu: "\u0645\u0646\u0648 \u0645\u062A\u0646\u06CC",
          Browser: "\u0645\u0631\u0648\u0631\u06AF\u0631",
          Scale: "\u0645\u0642\u06CC\u0627\u0633\u200C\u062F\u0647\u06CC \u0647\u0645\u0647\u0654 \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A ...",
          Discoverable: "\u0628\u0631\u062C\u0633\u062A\u0647\u200C\u0634\u062F\u0646 \u0628\u0627 \u0642\u0631\u0627\u0631\u062F\u0627\u062F\u0646 \u0645\u0648\u0634\u0648\u0627\u0631\u0647 \u0628\u0631 \u0631\u0648\u06CC\u0634",
          Locale: "\u0632\u0628\u0627\u0646",
          LoadLocale: "\u0628\u0627\u0631\u06AF\u06CC\u0631\u06CC \u0627\u0632 \u0646\u0634\u0627\u0646\u06CC \u0627\u06CC\u0646\u062A\u0631\u0646\u062A\u06CC ...",
          About: "\u062F\u0631\u0628\u0627\u0631\u0647\u0654 MathJax",
          Help: "\u0631\u0627\u0647\u0646\u0645\u0627\u06CC MathJax",
          localTeXfonts: "\u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u062A\u06A9 \u0645\u062D\u0644\u06CC",
          webTeXfonts: "\u0628\u0627 \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 \u0642\u0644\u0645 \u0648\u0628\u06CC \u062A\u06A9",
          imagefonts: "\u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u062A\u0635\u0648\u06CC\u0631\u06CC",
          localSTIXfonts: "\u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC STIX \u0645\u062D\u0644\u06CC",
          webSVGfonts: "\u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u0648\u0628\u06CC SVG",
          genericfonts: "\u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u0639\u0645\u0648\u0645\u06CC \u06CC\u0648\u0646\u06CC\u06A9\u062F",
          wofforotffonts: "\u0642\u0644\u0645\u200C\u0647\u0627\u06CC woff \u06CC\u0627 otf",
          eotffonts: "\u0642\u0644\u0645\u200C\u0647\u0627\u06CC eot",
          svgfonts: "\u0642\u0644\u0645\u200C\u0647\u0627\u06CC svg",
          WebkitNativeMMLWarning: "\u0628\u0647 \u0646\u0638\u0631 \u0645\u0631\u0648\u0631\u06AF\u0631 \u0634\u0645\u0627 \u0627\u0632 MathML \u0628\u0647\u200C\u0635\u0648\u0631\u062A \u0645\u062D\u0644\u06CC \u067E\u0634\u062A\u06CC\u0628\u0627\u0646\u06CC \u0646\u0645\u06CC\u200C\u06A9\u0646\u062F\u060C \u0628\u0646\u0627\u0628\u0631\u0627\u06CC\u0646 \u0631\u0641\u062A\u0646 \u0628\u0647 \u062D\u0627\u0644\u062A \u062E\u0631\u0648\u062C\u06CC MathML \u0645\u0645\u06A9\u0646 \u0627\u0633\u062A \u06A9\u0647 \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A \u0645\u0648\u062C\u0648\u062F \u062F\u0631 \u0635\u0641\u062D\u0647 \u063A\u06CC\u0631\u0642\u0627\u0628\u0644 \u062E\u0648\u0627\u0646\u062F\u0646 \u0634\u0648\u0646\u062F.",
          MSIENativeMMLWarning: "\u0627\u06CC\u0646\u062A\u0631\u0646\u062A \u0627\u06A9\u0633\u067E\u0644\u0648\u0631\u0631 \u0628\u0647 \u0627\u0641\u0632\u0627\u06CC\u0647\u0654 MathPlayer \u0628\u0631\u0627\u06CC \u067E\u0631\u062F\u0627\u0632\u0634 \u062E\u0631\u0648\u062C\u06CC MathML \u0646\u06CC\u0627\u0632\u0645\u0646\u062F \u0627\u0633\u062A.",
          OperaNativeMMLWarning: "\u067E\u0634\u062A\u06CC\u0628\u0627\u0646\u06CC \u0627\u067E\u0631\u0627 \u0627\u0632 MathML \u0645\u062D\u062F\u0648\u062F \u0627\u0633\u062A \u0628\u0646\u0627\u0628\u0631\u0627\u06CC\u0646 \u0627\u0646\u062A\u062E\u0627\u0628 \u062E\u0631\u0648\u062C\u06CC MathML \u0645\u0645\u06A9\u0646 \u0627\u0633\u062A \u0628\u0627\u0639\u062B \u0646\u0645\u0627\u06CC\u0634 \u0636\u0639\u06CC\u0641 \u0628\u0639\u0636\u06CC \u0627\u0632 \u0639\u0628\u0627\u0631\u062A\u200C\u0647\u0627 \u0634\u0648\u062F.",
          SafariNativeMMLWarning: "\u0645\u0631\u0648\u0631\u06AF\u0631 \u0634\u0645\u0627 \u0628\u0647\u200C\u0635\u0648\u0631\u062A \u0645\u062D\u0644\u06CC \u0647\u0645\u0647\u0654 \u0642\u0627\u0628\u0644\u06CC\u062A\u200C\u0647\u0627\u06CC \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0634\u062F\u0647 \u062A\u0648\u0633\u0637 MathJax \u0631\u0627 \u067E\u06CC\u0627\u062F\u0647\u200C\u0633\u0627\u0632\u06CC \u0646\u0645\u06CC\u200C\u06A9\u0646\u062F \u0628\u0646\u0627\u0628\u0631\u0627\u06CC\u0646 \u0628\u0639\u0636\u06CC \u0627\u0632 \u0639\u0628\u0627\u0631\u0627\u062A \u0645\u0645\u06A9\u0646 \u0627\u0633\u062A \u0628\u0647 \u062F\u0631\u0633\u062A\u06CC \u0646\u0645\u0627\u06CC\u0634 \u062F\u0627\u062F\u0647 \u0646\u0634\u0646\u062F.",
          FirefoxNativeMMLWarning: "\u0645\u0631\u0648\u0631\u06AF\u0631 \u0634\u0645\u0627 \u0628\u0647\u200C\u0635\u0648\u0631\u062A \u0645\u062D\u0644\u06CC \u0647\u0645\u0647\u0654 \u0642\u0627\u0628\u0644\u06CC\u062A\u200C\u0647\u0627\u06CC \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0634\u062F\u0647\u0654 MathML \u062A\u0648\u0633\u0637 MathJax \u0631\u0627 \u067E\u06CC\u0627\u062F\u0647\u200C\u0633\u0627\u0632\u06CC \u0646\u0645\u06CC\u200C\u06A9\u0646\u062F \u0628\u0646\u0627\u0628\u0631\u0627\u06CC\u0646 \u0628\u0639\u0636\u06CC \u0627\u0632 \u0639\u0628\u0627\u0631\u062A\u200C\u0647\u0627 \u0645\u0645\u06A9\u0646 \u0627\u0633\u062A \u0628\u0647 \u062E\u0648\u0628\u06CC \u062F\u06CC\u062F\u0647 \u0646\u0634\u0648\u0646\u062F.",
          MSIESVGWarning: "SVG \u062F\u0631 \u0627\u06CC\u0646\u062A\u0631\u0646\u062A \u0627\u06A9\u0633\u067E\u0644\u0648\u0631\u0631 \u0642\u0628\u0644 \u0627\u0632 \u0646\u0633\u062E\u0647\u0654 \u06F9 \u0622\u0646 \u06CC\u0627 \u0648\u0642\u062A\u06CC \u06A9\u0647 \u0634\u0628\u06CC\u0647\u200C\u0633\u0627\u0632\u06CC \u0646\u0633\u062E\u0647\u200C\u0647\u0627\u06CC \u0642\u0628\u0644\u06CC \u0631\u0627 \u0645\u06CC\u200C\u06A9\u0646\u062F \u067E\u06CC\u0627\u062F\u0647\u200C\u0633\u0627\u0632\u06CC \u0646\u0634\u062F\u0647\u200C\u0627\u0633\u062A. \u0627\u0646\u062A\u062E\u0627\u0628 \u062E\u0631\u0648\u062C\u06CC SVG \u0645\u0645\u06A9\u0646 \u0627\u0633\u062A \u0628\u0627\u0639\u062B \u0634\u0648\u062F \u06A9\u0647 \u0628\u0639\u0636\u06CC \u0627\u0632 \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A \u0628\u0647 \u062F\u0631\u0633\u062A\u06CC \u0646\u0645\u0627\u06CC\u0634 \u062F\u0627\u062F\u0647 \u0646\u0634\u0648\u0646\u062F.",
          LoadURL: "\u0628\u0627\u0631\u06AF\u06CC\u0631\u06CC \u062F\u0627\u062F\u0647\u200C\u0647\u0627\u06CC \u062A\u0631\u062C\u0645\u0647 \u0627\u0632 \u0627\u06CC\u0646 \u0646\u0634\u0627\u0646\u06CC \u0627\u06CC\u0646\u062A\u0631\u0646\u062A\u06CC:",
          BadURL: "\u0646\u0634\u0627\u0646\u06CC \u0627\u06CC\u0646\u062A\u0631\u0646\u062A\u06CC \u0628\u0631\u0627\u06CC \u067E\u0631\u0648\u0646\u062F\u0647\u0654 \u062C\u0627\u0648\u0627\u0627\u0633\u06A9\u0631\u067E\u062A\u06CC \u0627\u0633\u062A \u06A9\u0647 \u062F\u0627\u062F\u0647\u200C\u0647\u0627\u06CC \u062A\u0631\u062C\u0645\u0647\u200C\u0627\u06CC MathJax \u0631\u0627 \u062A\u0639\u0631\u06CC\u0641 \u0645\u06CC\u200C\u06A9\u0646\u062F. \u067E\u0631\u0648\u0646\u062F\u0647\u200C\u0647\u0627\u06CC \u062C\u0627\u0648\u0627\u0627\u0633\u06A9\u0631\u06CC\u067E\u062A \u0628\u0627 \u067E\u0633\u0648\u0646\u062F '.js' \u062A\u0645\u0627\u0645 \u0645\u06CC\u200C\u0634\u0648\u0646\u062F.",
          BadData: "\u0634\u06A9\u0633\u062A \u062F\u0631 \u0628\u0627\u0631\u06AF\u06CC\u0631\u06CC \u062F\u0627\u062F\u0647\u200C\u0647\u0627\u06CC \u062A\u0631\u062C\u0645\u0647\u200C\u0627\u06CC \u0627\u0632 %1",
          SwitchAnyway: "(\u0628\u0631 \u00AB\u0628\u0627\u0634\u062F\u00BB \u0641\u0634\u0627\u0631 \u062F\u0647\u06CC\u062F \u062A\u0627 \u0627\u0646\u062A\u062E\u0627\u0628 \u0634\u0648\u062F \u0648 \u0628\u0631 \u00AB\u0644\u063A\u0648\u00BB \u0641\u0634\u0627\u0631 \u062F\u0647\u06CC\u062F \u062A\u0627 \u0646\u0645\u0627\u06CC\u0634\u200C\u062F\u0647\u0646\u062F\u0647\u0654 \u0641\u0639\u0644\u06CC \u0627\u062F\u0627\u0645\u0647 \u06CC\u0627\u0628\u062F)",
          ScaleMath: "\u0645\u0642\u06CC\u0627\u0633 \u0647\u0645\u0647\u0654 \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A (\u062F\u0631 \u0645\u0642\u0627\u06CC\u0633\u0647 \u0628\u0627 \u0645\u062A\u0646 \u0627\u0637\u0631\u0627\u0641) \u062A\u0648\u0633\u0637",
          NonZeroScale: "\u0645\u0642\u06CC\u0627\u0633 \u0646\u0628\u0627\u06CC\u062F \u0635\u0641\u0631 \u0628\u0627\u0634\u062F",
          PercentScale: "\u0645\u0642\u06CC\u0627\u0633 \u0628\u0627\u06CC\u062F \u062F\u0631\u0635\u062F\u06CC \u0628\u0627\u0634\u062F (\u0628\u0631\u0627\u06CC \u0646\u0645\u0648\u0646\u0647 \u06F1\u06F2\u06F0\u066A)",
          IE8warning: "\u0627\u06CC\u0646 \u0645\u0646\u0648\u06CC MathJax \u0648 \u0642\u0627\u0628\u0644\u06CC\u062A\u200C\u0647\u0627\u06CC \u06A9\u0648\u0686\u06A9/\u0628\u0632\u0631\u06AF\u200C\u0646\u0645\u0627\u06CC\u06CC \u0631\u0627 \u063A\u06CC\u0631\u0641\u0639\u0627\u0644 \u0645\u06CC\u200C\u06A9\u0646\u062F \u0648\u0644\u06CC \u0634\u0645\u0627 \u0645\u06CC\u200C\u062A\u0648\u0627\u0646\u06CC\u062F \u0628\u0627 Alt-Click \u0628\u0631 \u0631\u0648\u06CC \u0639\u0628\u0627\u0631\u062A \u0645\u0646\u0648\u06CC MathJax \u0631\u0627 \u0628\u062F\u0633\u062A \u0622\u0648\u0631\u06CC\u062F.\n\n\u0648\u0627\u0642\u0639\u0627\u064B \u0645\u06CC\u200C\u062E\u0648\u0627\u0647\u06CC\u062F \u062A\u0646\u0638\u06CC\u0645\u0627\u062A MathPlayer \u0631\u0627 \u062A\u063A\u06CC\u06CC\u0631 \u062F\u0647\u06CC\u062F\u061F",
          IE9warning: "\u0645\u0646\u0648\u06CC \u0645\u062A\u0646\u06CC MathJax \u063A\u06CC\u0631\u0641\u0639\u0627\u0644 \u062E\u0648\u0627\u0647\u062F \u0634\u062F \u0648\u0644\u06CC \u0634\u0645\u0627 \u0645\u06CC\u200C\u062A\u0648\u0627\u0646\u062F \u0628\u0627 Alt-Click \u0628\u0631 \u0631\u0648\u06CC \u0639\u0628\u0627\u0631\u062A \u0645\u0646\u0648\u06CC MathJax \u0631\u0627 \u0628\u062F\u0633\u062A \u0628\u06CC\u0627\u0648\u0631\u06CC\u062F.",
          NoOriginalForm: "\u062D\u0627\u0644\u062A \u0627\u0648\u0644\u0628\u0647 \u0645\u0648\u062C\u0648\u062F \u0646\u06CC\u0633\u062A",
          Close: "\u0628\u0633\u062A\u0646",
          EqSource: "\u0645\u0646\u0628\u0639 \u0645\u0639\u0627\u062F\u0644\u0647\u0654 MathJax"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/fa/MathMenu.js");
