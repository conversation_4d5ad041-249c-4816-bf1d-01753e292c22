/*************************************************************
 *
 *  MathJax/localization/th/TeX.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("th","TeX",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "\u0E21\u0E35\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E40\u0E1B\u0E34\u0E14\u0E40\u0E01\u0E34\u0E19\u0E21\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E02\u0E32\u0E14\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E1B\u0E34\u0E14",
          ExtraCloseMissingOpen: "\u0E21\u0E35\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E1B\u0E34\u0E14\u0E40\u0E01\u0E34\u0E19\u0E21\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E02\u0E32\u0E14\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E40\u0E1B\u0E34\u0E14",
          MissingLeftExtraRight: "\u0E02\u0E32\u0E14 \\left \u0E2B\u0E23\u0E37\u0E2D\u0E21\u0E35 \\right \u0E40\u0E01\u0E34\u0E19\u0E21\u0E32",
          MissingScript: "\u0E15\u0E31\u0E27\u0E22\u0E01\u0E2B\u0E23\u0E37\u0E2D\u0E15\u0E31\u0E27\u0E2B\u0E49\u0E2D\u0E22\u0E27\u0E48\u0E32\u0E07\u0E40\u0E1B\u0E25\u0E48\u0E32",
          ExtraLeftMissingRight: "\u0E21\u0E35 \\left \u0E40\u0E01\u0E34\u0E19\u0E21\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E02\u0E32\u0E14 \\right",
          Misplaced: "\u0E27\u0E32\u0E07 %1 \u0E1C\u0E34\u0E14\u0E15\u0E33\u0E41\u0E2B\u0E19\u0E48\u0E07",
          MissingOpenForSub: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E40\u0E1B\u0E34\u0E14\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E15\u0E31\u0E27\u0E2B\u0E49\u0E2D\u0E22",
          MissingOpenForSup: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E40\u0E1B\u0E34\u0E14\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E15\u0E31\u0E27\u0E22\u0E01",
          AmbiguousUseOf: "\u0E1E\u0E1A\u0E01\u0E32\u0E23\u0E43\u0E0A\u0E49\u0E07\u0E32\u0E19\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E0B\u0E49\u0E33\u0E0B\u0E49\u0E2D\u0E19",
          EnvBadEnd: "\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19\u0E14\u0E49\u0E27\u0E22\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 \\begin{%1} \u0E41\u0E15\u0E48\u0E25\u0E07\u0E17\u0E49\u0E32\u0E22\u0E14\u0E49\u0E27\u0E22\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 \\end{%2}",
          EnvMissingEnd: "\u0E25\u0E37\u0E21\u0E25\u0E07\u0E17\u0E49\u0E32\u0E22\u0E14\u0E49\u0E27\u0E22\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 \\end{%1}",
          MissingBoxFor: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E01\u0E25\u0E48\u0E2D\u0E07\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A %1",
          MissingCloseBrace: "\u0E02\u0E32\u0E14\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E1B\u0E34\u0E14",
          UndefinedControlSequence: "\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E44\u0E21\u0E48\u0E17\u0E23\u0E32\u0E1A\u0E04\u0E27\u0E32\u0E21\u0E2B\u0E21\u0E32\u0E22\u0E2B\u0E23\u0E37\u0E2D\u0E23\u0E30\u0E1A\u0E38\u0E44\u0E21\u0E48\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19 %1",
          DoubleExponent: "\u0E15\u0E31\u0E27\u0E22\u0E01\u0E0B\u0E49\u0E33\u0E0B\u0E49\u0E2D\u0E19 \u0E42\u0E1B\u0E23\u0E14\u0E43\u0E0A\u0E49\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E08\u0E31\u0E14\u0E01\u0E25\u0E38\u0E48\u0E21\u0E43\u0E2B\u0E49\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",
          DoubleSubscripts: "\u0E15\u0E31\u0E27\u0E2B\u0E49\u0E2D\u0E22\u0E0B\u0E49\u0E33\u0E0B\u0E49\u0E2D\u0E19 \u0E42\u0E1B\u0E23\u0E14\u0E43\u0E0A\u0E49\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E08\u0E31\u0E14\u0E01\u0E25\u0E38\u0E48\u0E21\u0E43\u0E2B\u0E49\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",
          DoubleExponentPrime: "\u0E40\u0E04\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E2B\u0E21\u0E32\u0E22\u0E44\u0E1E\u0E23\u0E21\u0E4C (Prime) \u0E27\u0E32\u0E07\u0E44\u0E27\u0E49\u0E43\u0E19\u0E25\u0E31\u0E01\u0E29\u0E13\u0E30\u0E15\u0E31\u0E27\u0E22\u0E01\u0E0B\u0E49\u0E33\u0E0B\u0E49\u0E2D\u0E19 \u0E42\u0E1B\u0E23\u0E14\u0E43\u0E0A\u0E49\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E08\u0E31\u0E14\u0E01\u0E25\u0E38\u0E48\u0E21\u0E43\u0E2B\u0E49\u0E08\u0E31\u0E14\u0E40\u0E08\u0E19",
          CantUseHash1: "\u0E17\u0E48\u0E32\u0E19\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E43\u0E0A\u0E49 '\u0E2D\u0E31\u0E01\u0E02\u0E23\u0E30\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E21\u0E32\u0E42\u0E04\u0E23 #' \u0E43\u0E19\u0E42\u0E2B\u0E21\u0E14\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C\u0E44\u0E14\u0E49",
          MisplacedMiddle: "\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E2B\u0E23\u0E37\u0E2D\u0E1B\u0E23\u0E30\u0E42\u0E22\u0E04 %1 \u0E15\u0E49\u0E2D\u0E07\u0E2D\u0E22\u0E39\u0E48\u0E23\u0E30\u0E2B\u0E27\u0E48\u0E32\u0E07 \\left \u0E41\u0E25\u0E30 \\right",
          MisplacedLimits: "\u0E2D\u0E19\u0E38\u0E0D\u0E32\u0E15\u0E43\u0E2B\u0E49\u0E43\u0E0A\u0E49 %1 \u0E01\u0E31\u0E1A\u0E15\u0E31\u0E27\u0E14\u0E33\u0E40\u0E19\u0E34\u0E19\u0E01\u0E32\u0E23\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19",
          MisplacedMoveRoot: "\u0E1B\u0E23\u0E30\u0E42\u0E22\u0E04 %1 \u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E2D\u0E22\u0E39\u0E48\u0E44\u0E14\u0E49\u0E40\u0E09\u0E1E\u0E32\u0E30\u0E20\u0E32\u0E22\u0E43\u0E19\u0E23\u0E32\u0E01\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19",
          MultipleCommand: "\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E0B\u0E49\u0E33\u0E0B\u0E49\u0E2D\u0E19",
          IntegerArg: "\u0E2D\u0E32\u0E23\u0E4C\u0E01\u0E34\u0E27\u0E40\u0E21\u0E19\u0E15\u0E4C\u0E02\u0E2D\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E40\u0E1B\u0E47\u0E19\u0E08\u0E33\u0E19\u0E27\u0E19\u0E40\u0E15\u0E47\u0E21",
          NotMathMLToken: "%1 \u0E44\u0E21\u0E48\u0E16\u0E37\u0E2D\u0E40\u0E1B\u0E47\u0E19 token element",
          InvalidMathMLAttr: "\u0E41\u0E2D\u0E15\u0E17\u0E23\u0E34\u0E1A\u0E34\u0E27\u0E15\u0E4C MathML \u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14: %1",
          UnknownAttrForElement: "%1 \u0E44\u0E21\u0E48\u0E16\u0E37\u0E2D\u0E40\u0E1B\u0E47\u0E19\u0E04\u0E38\u0E13\u0E2A\u0E21\u0E1A\u0E31\u0E15\u0E34\u0E2B\u0E23\u0E37\u0E2D\u0E41\u0E2D\u0E15\u0E17\u0E23\u0E34\u0E1A\u0E34\u0E27\u0E15\u0E4C\u0E17\u0E35\u0E48\u0E43\u0E0A\u0E49\u0E01\u0E31\u0E1A %2 \u0E44\u0E14\u0E49",
          MaxMacroSub1: "\u0E08\u0E33\u0E19\u0E27\u0E19\u0E01\u0E32\u0E23\u0E41\u0E17\u0E19\u0E17\u0E35\u0E48\u0E21\u0E32\u0E42\u0E04\u0E23\u0E02\u0E2D\u0E07 MathJax \u0E40\u0E25\u0E22\u0E04\u0E48\u0E32\u0E2A\u0E39\u0E07\u0E2A\u0E38\u0E14 \u0E2D\u0E32\u0E08\u0E21\u0E35\u0E01\u0E32\u0E23\u0E40\u0E23\u0E35\u0E22\u0E01\u0E43\u0E0A\u0E49\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E41\u0E1A\u0E1A\u0E0B\u0E49\u0E33\u0E01\u0E31\u0E19\u0E44\u0E21\u0E48\u0E23\u0E39\u0E49\u0E08\u0E1A?",
          MaxMacroSub2: "\u0E08\u0E33\u0E19\u0E27\u0E19\u0E01\u0E32\u0E23\u0E41\u0E17\u0E19\u0E04\u0E48\u0E32 MathJax \u0E40\u0E25\u0E22\u0E04\u0E48\u0E32\u0E2A\u0E39\u0E07\u0E2A\u0E38\u0E14 \u0E2D\u0E32\u0E08\u0E08\u0E30\u0E21\u0E35\u0E01\u0E32\u0E23\u0E40\u0E23\u0E35\u0E22\u0E01\u0E43\u0E0A\u0E49\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E41\u0E1A\u0E1A\u0E0B\u0E49\u0E33\u0E01\u0E31\u0E19\u0E44\u0E21\u0E48\u0E23\u0E39\u0E49\u0E08\u0E1A?",
          MissingArgFor: "\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E21\u0E35\u0E2D\u0E07\u0E04\u0E4C\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E17\u0E35\u0E48\u0E08\u0E33\u0E40\u0E1B\u0E47\u0E19\u0E44\u0E21\u0E48\u0E04\u0E23\u0E1A\u0E16\u0E49\u0E27\u0E19",
          ExtraAlignTab: "\u0E21\u0E35\u0E41\u0E17\u0E47\u0E1A\u0E08\u0E31\u0E14\u0E27\u0E32\u0E07\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E40\u0E15\u0E34\u0E21\u0E43\u0E19\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21 \\cases",
          BracketMustBeDimension: "\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E43\u0E19\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E17\u0E35\u0E48\u0E43\u0E2B\u0E49\u0E01\u0E31\u0E1A\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E40\u0E1B\u0E47\u0E19\u0E02\u0E19\u0E32\u0E14\u0E2B\u0E23\u0E37\u0E2D\u0E21\u0E34\u0E15\u0E34",
          InvalidEnv: "\u0E0A\u0E37\u0E48\u0E2D\u0E01\u0E25\u0E38\u0E48\u0E21\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 '%1'\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07",
          UnknownEnv: "\u0E01\u0E25\u0E38\u0E48\u0E21\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 (environment) \u0E17\u0E35\u0E48\u0E44\u0E21\u0E48\u0E17\u0E23\u0E32\u0E1A\u0E2B\u0E23\u0E37\u0E2D\u0E41\u0E1B\u0E25\u0E04\u0E27\u0E32\u0E21\u0E2B\u0E21\u0E32\u0E22\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49 '%1'",
          ExtraCloseLooking: "\u0E21\u0E35\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E1B\u0E34\u0E14\u0E40\u0E01\u0E34\u0E19\u0E02\u0E13\u0E30\u0E17\u0E35\u0E48\u0E15\u0E23\u0E27\u0E08\u0E2B\u0E32 %1",
          MissingCloseBracket: "\u0E44\u0E21\u0E48\u0E1E\u0E1A ']' \u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E17\u0E35\u0E48\u0E43\u0E2B\u0E49\u0E01\u0E31\u0E1A\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1",
          MissingOrUnrecognizedDelim: "\u0E15\u0E31\u0E27\u0E04\u0E31\u0E48\u0E19\u0E17\u0E35\u0E48\u0E43\u0E2B\u0E49\u0E01\u0E31\u0E1A %1 \u0E2B\u0E32\u0E22\u0E44\u0E1B \u0E2B\u0E23\u0E37\u0E2D\u0E44\u0E21\u0E48\u0E23\u0E39\u0E49\u0E08\u0E31\u0E01",
          MissingDimOrUnits: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E19\u0E32\u0E14\u0E2B\u0E23\u0E37\u0E2D\u0E2B\u0E19\u0E48\u0E27\u0E22\u0E17\u0E35\u0E48\u0E43\u0E2B\u0E49\u0E01\u0E31\u0E1A\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1",
          TokenNotFoundForCommand: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E04\u0E49\u0E19\u0E2B\u0E32 %1 \u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A %2",
          MathNotTerminated: "\u0E2A\u0E21\u0E01\u0E32\u0E23\u0E43\u0E19\u0E01\u0E25\u0E48\u0E2D\u0E07\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21\u0E1B\u0E34\u0E14\u0E44\u0E21\u0E48\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22",
          IllegalMacroParam: "\u0E01\u0E32\u0E23\u0E2D\u0E49\u0E32\u0E07\u0E16\u0E36\u0E07\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E21\u0E32\u0E42\u0E04\u0E23\u0E1C\u0E34\u0E14\u0E2B\u0E25\u0E31\u0E01\u0E40\u0E01\u0E13\u0E11\u0E4C",
          MaxBufferSize: "\u0E1A\u0E31\u0E1F\u0E40\u0E1F\u0E2D\u0E23\u0E4C\u0E20\u0E32\u0E22\u0E43\u0E19\u0E02\u0E2D\u0E07 MathJax \u0E25\u0E49\u0E19\u0E40\u0E01\u0E34\u0E19\u0E02\u0E19\u0E32\u0E14 \u0E21\u0E35\u0E01\u0E32\u0E23\u0E40\u0E23\u0E35\u0E22\u0E01\u0E21\u0E32\u0E42\u0E04\u0E23\u0E41\u0E1A\u0E1A\u0E0B\u0E49\u0E33\u0E01\u0E31\u0E19\u0E2B\u0E23\u0E37\u0E2D\u0E44\u0E21\u0E48?",
          CommandNotAllowedInEnv: "\u0E44\u0E21\u0E48\u0E2D\u0E19\u0E38\u0E0D\u0E32\u0E15\u0E43\u0E2B\u0E49\u0E43\u0E0A\u0E49\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E20\u0E32\u0E22\u0E43\u0E19\u0E01\u0E25\u0E38\u0E48\u0E21\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %2",
          MultipleLabel: "\u0E1B\u0E49\u0E32\u0E22\u0E23\u0E30\u0E1A\u0E38 '%1' \u0E21\u0E35\u0E0B\u0E49\u0E33\u0E0B\u0E49\u0E2D\u0E19\u0E01\u0E31\u0E19",
          CommandAtTheBeginingOfLine: "%1 \u0E15\u0E49\u0E2D\u0E07\u0E2D\u0E22\u0E39\u0E48\u0E15\u0E49\u0E19\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14",
          IllegalAlign: "\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E01\u0E32\u0E23\u0E08\u0E31\u0E14\u0E27\u0E32\u0E07\u0E43\u0E19 %1 \u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07",
          BadMathStyleFor: "\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A %1 \u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07",
          PositiveIntegerArg: "\u0E2D\u0E32\u0E23\u0E4C\u0E01\u0E34\u0E27\u0E40\u0E21\u0E19\u0E15\u0E4C\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E40\u0E1B\u0E47\u0E19\u0E08\u0E33\u0E19\u0E27\u0E19\u0E40\u0E15\u0E47\u0E21\u0E1A\u0E27\u0E01",
          ErroneousNestingEq: "\u0E01\u0E32\u0E23\u0E08\u0E31\u0E14\u0E40\u0E23\u0E35\u0E22\u0E07\u0E42\u0E04\u0E23\u0E07\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E0B\u0E49\u0E2D\u0E19\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E21\u0E35\u0E04\u0E27\u0E32\u0E21\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14",
          MultlineRowsOneCol: "\u0E08\u0E33\u0E19\u0E27\u0E19\u0E41\u0E16\u0E27\u0E43\u0E19\u0E01\u0E25\u0E38\u0E48\u0E21\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E08\u0E33\u0E19\u0E27\u0E19\u0E04\u0E2D\u0E25\u0E31\u0E21\u0E19\u0E4C\u0E2B\u0E23\u0E37\u0E2D\u0E2B\u0E25\u0E31\u0E01\u0E40\u0E1E\u0E35\u0E22\u0E07\u0E2B\u0E19\u0E36\u0E48\u0E07\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19",
          MultipleBBoxProperty: "\u0E23\u0E30\u0E1A\u0E38 %1 \u0E0B\u0E49\u0E33\u0E01\u0E31\u0E19\u0E43\u0E19 %2",
          InvalidBBoxProperty: "\u0E14\u0E39\u0E40\u0E2B\u0E21\u0E37\u0E2D\u0E19\u0E27\u0E48\u0E32 '%1' \u0E44\u0E21\u0E48\u0E43\u0E0A\u0E48\u0E2A\u0E35 \u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E01\u0E32\u0E23\u0E40\u0E15\u0E34\u0E21 \u0E2B\u0E23\u0E37\u0E2D\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A",
          ExtraEndMissingBegin: "\u0E21\u0E35 %1 \u0E40\u0E01\u0E34\u0E19 \u0E2B\u0E23\u0E37\u0E2D\u0E02\u0E32\u0E14 \\begingroup",
          GlobalNotFollowedBy: "%1 \u0E44\u0E21\u0E48\u0E15\u0E32\u0E21\u0E14\u0E49\u0E27\u0E22 \\let, \\def, \u0E2B\u0E23\u0E37\u0E2D \\newcommand",
          UndefinedColorModel: "\u0E41\u0E1A\u0E1A\u0E08\u0E33\u0E25\u0E2D\u0E07\u0E2A\u0E35 '%1' \u0E22\u0E31\u0E07\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49\u0E19\u0E34\u0E22\u0E32\u0E21",
          ModelArg1: "\u0E04\u0E48\u0E32\u0E2A\u0E35\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E41\u0E1A\u0E1A\u0E08\u0E33\u0E25\u0E2D\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02 3 \u0E15\u0E31\u0E27",
          InvalidDecimalNumber: "\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02\u0E17\u0E28\u0E19\u0E34\u0E22\u0E21\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07",
          ModelArg2: "\u0E04\u0E48\u0E32\u0E2A\u0E35\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E41\u0E1A\u0E1A\u0E08\u0E33\u0E25\u0E2D\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E2D\u0E22\u0E39\u0E48\u0E23\u0E30\u0E2B\u0E27\u0E48\u0E32\u0E07 %2 \u0E41\u0E25\u0E30 %3",
          InvalidNumber: "\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07",
          NewextarrowArg1: "\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E41\u0E23\u0E01\u0E02\u0E2D\u0E07\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E40\u0E1B\u0E47\u0E19\u0E0A\u0E37\u0E48\u0E2D\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E04\u0E27\u0E1A\u0E04\u0E38\u0E21",
          NewextarrowArg2: "\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E17\u0E35\u0E48\u0E2A\u0E2D\u0E07\u0E02\u0E2D\u0E07\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E40\u0E1B\u0E47\u0E19\u0E08\u0E33\u0E19\u0E27\u0E19\u0E40\u0E15\u0E47\u0E21\u0E2A\u0E2D\u0E07\u0E15\u0E31\u0E27\u0E04\u0E31\u0E48\u0E19\u0E14\u0E49\u0E27\u0E22\u0E40\u0E04\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E2B\u0E21\u0E32\u0E22\u0E08\u0E38\u0E25\u0E20\u0E32\u0E04",
          NewextarrowArg3: "\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E17\u0E35\u0E48\u0E2A\u0E32\u0E21\u0E02\u0E2D\u0E07\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E40\u0E1B\u0E47\u0E19\u0E23\u0E2B\u0E31\u0E2A\u0E2D\u0E31\u0E01\u0E29\u0E23\u0E43\u0E19\u0E23\u0E30\u0E1A\u0E1A\u0E22\u0E39\u0E19\u0E34\u0E42\u0E04\u0E49\u0E14",
          NoClosingChar: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E27\u0E07\u0E40\u0E25\u0E47\u0E1A\u0E1B\u0E34\u0E14 %1",
          IllegalControlSequenceName: "\u0E0A\u0E37\u0E48\u0E2D\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E1C\u0E34\u0E14\u0E01\u0E0E\u0E40\u0E01\u0E13\u0E11\u0E4C",
          IllegalParamNumber: "\u0E08\u0E33\u0E19\u0E27\u0E19\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E17\u0E35\u0E48\u0E23\u0E30\u0E1A\u0E38\u0E43\u0E19 %1 \u0E1C\u0E34\u0E14\u0E01\u0E0E\u0E40\u0E01\u0E13\u0E11\u0E4C (\u0E02\u0E32\u0E14\u0E2B\u0E23\u0E37\u0E2D\u0E40\u0E01\u0E34\u0E19)",
          MissingCS: "%1 \u0E15\u0E49\u0E2D\u0E07\u0E15\u0E32\u0E21\u0E14\u0E49\u0E27\u0E22\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07\u0E04\u0E27\u0E1A\u0E04\u0E38\u0E21",
          CantUseHash2: "\u0E1E\u0E1A\u0E01\u0E32\u0E23\u0E43\u0E0A\u0E49 # \u0E0B\u0E36\u0E48\u0E07\u0E1C\u0E34\u0E14\u0E2B\u0E25\u0E31\u0E01\u0E40\u0E01\u0E13\u0E11\u0E4C\u0E43\u0E19\u0E41\u0E21\u0E48\u0E41\u0E1A\u0E1A\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1",
          SequentialParam: "\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E15\u0E49\u0E2D\u0E07\u0E40\u0E23\u0E35\u0E22\u0E07\u0E40\u0E1B\u0E47\u0E19\u0E25\u0E33\u0E14\u0E31\u0E1A",
          MissingReplacementString: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E15\u0E31\u0E27\u0E41\u0E17\u0E19\u0E2A\u0E32\u0E22\u0E2D\u0E31\u0E01\u0E02\u0E23\u0E30\u0E43\u0E19\u0E19\u0E34\u0E22\u0E32\u0E21\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1",
          MismatchUseDef: "\u0E01\u0E32\u0E23\u0E43\u0E0A\u0E49\u0E07\u0E32\u0E19\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1 \u0E44\u0E21\u0E48\u0E15\u0E23\u0E07\u0E01\u0E31\u0E1A\u0E19\u0E34\u0E22\u0E32\u0E21\u0E40\u0E14\u0E34\u0E21\u0E02\u0E2D\u0E07\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07",
          RunawayArgument: "\u0E21\u0E35\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E17\u0E35\u0E48\u0E25\u0E30\u0E40\u0E27\u0E49\u0E19\u0E44\u0E27\u0E49\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 %1?",
          NoClosingDelim: "\u0E44\u0E21\u0E48\u0E1E\u0E1A closing delimiter \u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/th/TeX.js");
