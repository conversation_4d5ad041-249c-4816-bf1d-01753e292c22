/*************************************************************
 *
 *  MathJax/localization/it/MathML.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("it","MathML",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          BadMglyph: "mglyph errato: %1",
          BadMglyphFont: "Font errato: %1",
          MathPlayer: "MathJax non \u00E8 stato in grado di avviare MathPlayer.\n\nSe MathPlayer non \u00E8 installato, devi prima installarlo.\nPu\u00F2 darsi anche che le tue impostazioni di sicurezza stiano impedendo\nl'esecuzione dei controlli ActiveX. Controlla la voce Opzioni Internet\ndel menu Strumenti e seleziona il pannello Protezione, quindi premi\nil pulsante 'Livello personalizzato...'. Verifica che siano abilitati\n'Esegui controlli ActiveX e plug-in' e 'Comportamento file binari e script'\n\nOra come ora vedrai dei messaggi d'errore al posto delle formule.",
          CantCreateXMLParser: "MathJax non \u00E8 in grado di creare un parser XML per MathML. Verifica che\nl'impostazione 'Esegui script controlli ActiveX contrassegnati come sicuri'\nsia abilitata (usa la voce Opzioni Internet nel menu Strumenti,\ne seleziona il pannello Sicurezza, quindi premi il pulsante\n'Livello personalizzato...' per far questo).\n\nLe equazioni in MathML non potranno essere elaborate da MathJax.",
          UnknownNodeType: "Tipo di nodo sconosciuto: %1",
          UnexpectedTextNode: "Nodo di testo non previsto: %1",
          ErrorParsingMathML: "Errore nell'analisi di MathML",
          ParsingError: "Errore nell'analisi di MathML: %1",
          MathMLSingleElement: "MathML deve essere formato da un singolo elemento",
          MathMLRootElement: "MathML deve essere formato da un elemento \u003Cmath\u003E, non %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/it/MathML.js");
