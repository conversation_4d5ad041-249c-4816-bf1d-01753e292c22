/*************************************************************
 *
 *  MathJax/localization/mk/MathML.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("mk","MathML",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          BadMglyph: "\u0413\u0440\u0435\u0448\u0435\u043D mglyph: %1",
          BadMglyphFont: "\u0413\u0440\u0435\u0448\u0435\u043D \u0444\u043E\u043D\u0442: %1",
          MathPlayer: "MathJax \u043D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0433\u043E \u043F\u043E\u0441\u0442\u0430\u0432\u0438 MathPlayer.\n\n\u0414\u043E\u043A\u043E\u043B\u043A\u0443 \u043D\u0435 \u0432\u0438 \u0435 \u0432\u043E\u0441\u043F\u043E\u0441\u0442\u0430\u0432\u0435\u043D MathPlayer, \u045C\u0435 \u0442\u0440\u0435\u0431\u0430 \u043F\u0440\u0432\u043E \u0434\u0430 \u0433\u043E \u0432\u043E\u0441\u043F\u043E\u0441\u0442\u0430\u0432\u0438\u0442\u0435.\n\u0412\u043E \u0441\u043F\u0440\u043E\u0442\u0438\u0432\u043D\u043E, \u043E\u0432\u0430 \u0437\u043D\u0430\u0447\u0438 \u0434\u0435\u043A\u0430 \u0432\u0430\u0448\u0438\u0442\u0435 \u0431\u0435\u0437\u0431\u0435\u0434\u043D\u043E\u0441\u043D\u0438 \u043F\u043E\u0441\u0442\u0430\u0432\u043A\u0438 \u0458\u0430 \u0441\u043F\u0440\u0435\u0447\u0443\u0432\u0430\u0430\u0442 \u0440\u0430\u0431\u043E\u0442\u0430\u0442\u0430 \u043D\u0430 \u043A\u043E\u043D\u0442\u0440\u043E\u043B\u0438\u0442\u0435 \u0432\u043E ActiveX. \u041F\u043E\u0458\u0434\u0435\u0442\u0435 \u0432\u043E \u0431\u0435\u0437\u0431\u0435\u0434\u043D\u043E\u0441\u043D\u0438\u0442\u0435 \u043D\u0430\u0433\u043E\u0434\u0443\u0432\u0430\u045A\u0430 \u043D\u0430 \u043F\u0440\u0435\u043B\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u043E\u0442 \u0438 \u0441\u0442\u0438\u0441\u043D\u0435\u0442\u0435 \u043D\u0430 \u043F\u0440\u0438\u043B\u0430\u0433\u043E\u0434\u043B\u0438\u0432\u043E (Custom Level). \u0422\u0430\u043C\u0443 \u043F\u0440\u043E\u0432\u0435\u0440\u0435\u0442\u0435 \u0434\u0430\u043B\u0438 \u0432\u0438 \u0441\u0435 \u0432\u043A\u043B\u0443\u0447\u0435\u043D\u0438 \u043C\u043E\u0436\u043D\u043E\u0441\u0442\u0438\u0442\u0435 \u0437\u0430 \u0440\u0430\u0431\u043E\u0442\u0430 \u0441\u043E ActiveX-\u043A\u043E\u043D\u0442\u0440\u043E\u043B\u0438 \u0438 \u0411\u0438\u043D\u0430\u0440\u043D\u0438 \u0438 \u0441\u043A\u0440\u0438\u043F\u0442\u043D\u0438 \u043F\u043E\u0432\u0435\u0434\u0435\u043D\u0438\u0458\u0430.\n\n\u0417\u0430\u0441\u0435\u0433\u0430 \u045C\u0435 \u0433\u043B\u0435\u0434\u0430\u0442\u0435 \u0438\u0437\u0432\u0435\u0441\u0442\u0443\u0432\u0430\u045A\u0430 \u0437\u0430 \u0433\u0440\u0435\u0448\u043A\u0438 \u043D\u0430\u043C\u0435\u0441\u0442\u043E \u0438\u0441\u043F\u0438\u0448\u0430\u043D\u0438 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043A\u0438 \u0441\u043E\u0434\u0440\u0436\u0438\u043D\u0438.",
          CantCreateXMLParser: "MathJax \u043D\u0435 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0441\u043E\u0437\u0434\u0430\u0434\u0435 XML-\u043F\u0430\u0440\u0441\u0435\u0440 \u0437\u0430 MathML. \u041F\u0440\u043E\u0432\u0435\u0440\u0435\u0442\u0435 \u0434\u0430\u043B\u0438 \u0435 \u0432\u043A\u043B\u0443\u0447\u0435\u043D\u0430 \u0441\u0438\u0433\u0443\u0440\u043D\u043E\u0441\u043D\u0430\u0442\u0430 \u043F\u043E\u0441\u0442\u0430\u0432\u043A\u0430 ActiveX-\u043A\u043E\u043D\u0442\u0440\u043E\u043B\u0438 \u0431\u0435\u0437\u0431\u0435\u0434\u043D\u0438 \u0437\u0430 \u0441\u043A\u0440\u0438\u043F\u0442\u0438\u0440\u0430\u045A\u0435 (\u0432\u043E \u043F\u0440\u0438\u043B\u0430\u0433\u043E\u0434\u043B\u0438\u0432\u0438\u043E\u0442 \u0434\u0435\u043B (Custom Level) \u043D\u0430 \u0431\u0435\u0437\u0431\u0435\u0434\u043D\u043E\u0441\u0442\u0438\u0442\u0435 \u043F\u043E\u0441\u0442\u0430\u0432\u043A\u0438 \u0432\u043E \u043F\u0440\u0435\u043B\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u043E\u0442).\n\nMathJax \u043D\u0435\u043C\u0430 \u0434\u0430 \u043C\u043E\u0436\u0435 \u0434\u0430 \u0433\u0438 \u043E\u0431\u0440\u0430\u0431\u043E\u0442\u0438 \u0440\u0430\u0432\u0435\u043D\u043A\u0438\u0442\u0435 \u043D\u0430 MathML.",
          UnknownNodeType: "\u041D\u0435\u043F\u043E\u0437\u043D\u0430\u0442 \u0442\u0438\u043F \u043D\u0430 \u0458\u0430\u0437\u043E\u043B: %1",
          UnexpectedTextNode: "\u041D\u0435\u043E\u0447\u0435\u043A\u0443\u0432\u0430\u043D \u0458\u0430\u0437\u043E\u043B \u0432\u043E \u0442\u0435\u043A\u0441\u0442\u043E\u0442: %1",
          ErrorParsingMathML: "\u0413\u0440\u0435\u0448\u043A\u0430 \u043F\u0440\u0438 \u0440\u0430\u0441\u0447\u043B\u0435\u043D\u0443\u0432\u0430\u045A\u0435\u0442\u043E \u043D\u0430 MathML",
          ParsingError: "\u0413\u0440\u0435\u0448\u043A\u0430 \u043F\u0440\u0438 \u0440\u0430\u0441\u0447\u043B\u0435\u043D\u0443\u0432\u0430\u045A\u0435\u0442\u043E \u043D\u0430 MathML: %1",
          MathMLSingleElement: "MathML \u043C\u043E\u0440\u0430 \u0434\u0430 \u0441\u0435 \u043E\u0431\u0440\u0430\u0437\u0443\u0432\u0430 \u043E\u0434 \u0435\u0434\u0435\u043D \u0435\u043B\u0435\u043C\u0435\u043D\u0442",
          MathMLRootElement: "MathML \u043C\u043E\u0440\u0430 \u0434\u0430 \u0441\u0435 \u043E\u0431\u0440\u0430\u0437\u0443\u0432\u0430 \u0441\u043E \u0435\u043B\u0435\u043C\u0435\u043D\u0442 \u003Cmath\u003E, \u0430 \u043D\u0435 %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/mk/MathML.js");
