/*************************************************************
 *
 *  MathJax/localization/de/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("de","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "Zeige mathematischen Ausdruck als",
          MathMLcode: "MathML-Code",
          OriginalMathML: "Original-MathML",
          TeXCommands: "TeX-Befehle",
          AsciiMathInput: "AsciiMathML-Eingabe",
          Original: "Originalformel",
          ErrorMessage: "Fehlermeldung",
          Annotation: "Anmerkung",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Content MathML",
          OpenMath: "OpenMath",
          texHints: "TeX-Hinweise in MathML anzeigen",
          Settings: "Math-Einstellungen",
          ZoomTrigger: "Zoomausl\u00F6ser",
          Hover: "\u00DCberfahren",
          Click: "Klick",
          DoubleClick: "Doppelklick",
          NoZoom: "Kein Zoom",
          TriggerRequires: "Ausl\u00F6ser ben\u00F6tigt:",
          Option: "Option",
          Alt: "Alt",
          Command: "\u2318",
          Control: "Strg",
          Shift: "Umschalt",
          ZoomFactor: "Vergr\u00F6\u00DFerungsfaktor",
          Renderer: "Math-Renderer",
          MPHandles: "MathPlayer darf verwalten:",
          MenuEvents: "Men\u00FCereignisse",
          MouseEvents: "Mausereignisse",
          MenuAndMouse: "Maus- und Men\u00FCereignisse",
          FontPrefs: "Schriftarteinstellungen",
          ForHTMLCSS: "F\u00FCr HTML-CSS:",
          Auto: "Automatisch",
          TeXLocal: "TeX (Lokal)",
          TeXWeb: "TeX (Web)",
          TeXImage: "TeX (Bild)",
          STIXLocal: "STIX (Lokal)",
          STIXWeb: "STIX (Web)",
          AsanaMathWeb: "Asana Math (Web)",
          GyrePagellaWeb: "Gyre Pagella (Web)",
          GyreTermesWeb: "Gyre Termes (Web)",
          LatinModernWeb: "Latin Modern (Web)",
          NeoEulerWeb: "Neo Euler (Web)",
          ContextMenu: "Kontextmen\u00FC",
          Browser: "Browser",
          Scale: "Alle Inhalte skalieren \u2026",
          Discoverable: "Beim \u00DCberfahren hervorheben",
          Locale: "Sprache",
          LoadLocale: "Lade von URL \u2026",
          About: "\u00DCber MathJax",
          Help: "MathJax-Hilfe",
          localTeXfonts: "Lokale TeX-Schriften verwenden",
          webTeXfonts: "Web-TeX-Schriftart verwenden",
          imagefonts: "Bildschriften verwenden",
          localSTIXfonts: "Lokale STIX-Schriften verwenden",
          webSVGfonts: "Web-SVG-Schriften verwenden",
          genericfonts: "Allgemeine Unicode-Schriften verwenden",
          wofforotffonts: "WOFF- oder OTF-Schriften",
          eotffonts: "EOT-Schriften",
          svgfonts: "SVG-Schriften",
          WebkitNativeMMLWarning: "Ihr Browser scheint kein natives MathML zu unterst\u00FCtzen. Ein Wechsel auf MathML-Ausgabe kann dazu f\u00FChren, dass die Inhalte auf der Seite unlesbar werden.",
          MSIENativeMMLWarning: "Um die MathML-Ausgabe verarbeiten zu k\u00F6nnen, ben\u00F6tigt der Internet Explorer das MathPlayer-Plugin.",
          OperaNativeMMLWarning: "Die Unterst\u00FCtzung f\u00FCr MathML bei Opera ist beschr\u00E4nkt. Deshalb kann ein Wechsel auf MathML-Ausgabe verursachen, dass einige Ausdr\u00FCcke nur schlecht gerendert werden k\u00F6nnen.",
          SafariNativeMMLWarning: "Das native MathML Ihres Browsers unterst\u00FCtzt nicht alle Funktionen, die von MathJax verwendet werden. Deshalb k\u00F6nnen einige Ausdr\u00FCcke nicht richtig gerendert werden.",
          FirefoxNativeMMLWarning: "Das native MathML Ihres Browsers unterst\u00FCtzt nicht alle Funktionen, die von MathJax verwendet werden. Deshalb k\u00F6nnen einige Ausdr\u00FCcke nicht richtig gerendert werden.",
          MSIESVGWarning: "SVG wird nicht vom Internet Explorer \u00E4lter als IE9 unterst\u00FCtzt oder wenn IE8 und niedriger emuliert werden. Der Wechsel auf SVG-Ausgabe hat zur Folge, dass die Inhalte nicht richtig dargestellt werden.",
          LoadURL: "\u00DCbersetzungsdaten von dieser URL laden:",
          BadURL: "Die URL sollte f\u00FCr eine JavaScript-Datei sein, die MathJax-\u00DCbersetzungsdaten definiert. JavaScript-Dateinamen sollten enden mit \u201E.js\u201C.",
          BadData: "\u00DCbersetzungsdaten konnten nicht von %1 geladen werden",
          SwitchAnyway: "Den Renderer trotzdem wechseln?\n\n(OK w\u00E4hlen zum Wechseln, ABBRECHEN w\u00E4hlen, um mit dem gegenw\u00E4rtigen Renderer fortzufahren)",
          ScaleMath: "Alle Inhalte skalieren (verglichen mit dem umflie\u00DFenden Text) um",
          NonZeroScale: "Der Wert sollte nicht null sein",
          PercentScale: "Der Wert sollte ein Prozentsatz sein (z.\u0026nbsp;B. 120%%)",
          IE8warning: "Dies deaktiviert das MathJax-Men\u00FC und die Zoomfunktionen, aber du kannst bei einem Ausdruck w\u00E4hrend des Mausklicks die Alt-Taste gedr\u00FCckt halten, um stattdessen das MathJax-Men\u00FC zu erhalten.\n\nMathPlayer-Einstellungen wirklich \u00E4ndern?",
          IE9warning: "Das MathJax-Kontextmen\u00FC wird deaktiviert, aber du kannst bei einem Ausdruck w\u00E4hrend des Mausklicks die Alt-Taste gedr\u00FCckt halten, um stattdessen das MathJax-Men\u00FC zu erhalten.",
          NoOriginalForm: "Keine Originalformel verf\u00FCgbar",
          Close: "Schlie\u00DFen",
          EqSource: "MathJax-Gleichungsquelle",
          CloseAboutDialog: "Dialog \u201E\u00DCber MathJax\u201C schlie\u00DFen",
          FastPreview: "Schnelle Vorschau",
          AssistiveMML: "Assistives MathML",
          InTabOrder: "In Reiterreihenfolge einschlie\u00DFen"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/de/MathMenu.js");
