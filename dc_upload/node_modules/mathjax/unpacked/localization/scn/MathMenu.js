/*************************************************************
 *
 *  MathJax/localization/scn/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("scn","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "Ammustra la f\u00F2rmula sutta forma di",
          MathMLcode: "C\u00F2dici MathML",
          OriginalMathML: "MathML origginali",
          TeXCommands: "Cumanni <PERSON>",
          AsciiMathInput: "Input AsciiMathML",
          Original: "Forma origginali",
          ErrorMessage: "Missaggiu d'erruri",
          Annotation: "Annutazzioni",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "MathML d\u00FB cuntinutu",
          OpenMath: "OpenMath",
          texHints: "Ammustra suggirimenti TeX nt\u00F4 MathML",
          Settings: "Mpustazzioni d\u00EE f\u00F2rmuli matim\u00E0tichi",
          ZoomTrigger: "Attivazzioni d\u00FB zoom",
          Hover: "Passaggiu d\u00FB mouse",
          Click: "Clic",
          DoubleClick: "Duppiu clic",
          NoZoom: "Zoom nenti",
          TriggerRequires: "Pi l'attivazzioni ci voli:",
          Option: "Opzioni",
          Alt: "Alt",
          Command: "Cumannu",
          Control: "Ctrl",
          Shift: "Mai\u00F9sc.",
          ZoomFactor: "Fatturi di zoom",
          Renderer: "Giniraturi d\u00E2 matim\u00E0tica",
          MPHandles: "Lassa gistiri \u00F4 MathPlayer:",
          MenuEvents: "L'eventi d\u00EE men\u00F9",
          MouseEvents: "L'eventi d\u00FB mouse",
          MenuAndMouse: "L'eventi d\u00FB mouse e d\u00EE men\u00F9",
          FontPrefs: "Prifirenzi p\u00EE tipi di car\u00E0ttiri",
          ForHTMLCSS: "Pi l'HTML-CSS:",
          Auto: "Autum\u00E0ticu",
          TeXLocal: "TeX (lucali)",
          TeXWeb: "TeX (web)",
          TeXImage: "TeX (mm\u00E0ggini)",
          STIXLocal: "STIX (lucali)",
          STIXWeb: "STIX (web)",
          AsanaMathWeb: "Asana Math (web)",
          GyrePagellaWeb: "Gyre Pagella (web)",
          GyreTermesWeb: "Gyre Termes (web)",
          LatinModernWeb: "Latin Modern (web)",
          NeoEulerWeb: "Neo Euler (web)",
          ContextMenu: "Men\u00F9 cuntistuali",
          Browser: "Browser",
          Scale: "Arridiminziona tutta la matim\u00E0tica...",
          Discoverable: "Arrisaltari \u00F4 passaggiu d\u00FB mouse",
          Locale: "Lingua",
          LoadLocale: "Sc\u00E0rrica di l\u2019URL...",
          About: "A prup\u00F2situ di MathJax",
          Help: "Guida di MathJax",
          localTeXfonts: "sta facennu usu d\u00EE tipi di car\u00E0ttiri lucali TeX",
          webTeXfonts: "sta facennu usu d\u00EE tipi di car\u00E0ttiri web TeX",
          imagefonts: "sta facennu usu d\u00EE mm\u00E0ggini fatti a tipi di car\u00E0ttiri",
          localSTIXfonts: "sta facennu usu d\u00EE tipi di car\u00E0ttiri lucali STIX",
          webSVGfonts: "sta facennu usu d\u00EE tipi di car\u00E0ttiri web SVG",
          genericfonts: "sta facennu usu d\u00EE tipi di car\u00E0ttiri Unicode gin\u00E8rici",
          wofforotffonts: "Tipi di car\u00E0ttiri WOFF o OTF",
          eotffonts: "Tipi di car\u00E0ttiri EOT",
          svgfonts: "Tipi di car\u00E0ttiri SVG",
          WebkitNativeMMLWarning: "Lu t\u00F2 browser nun pari ca supporta nativamenti lu MathML, dunca passannu a l'output MathML la matim\u00E0tica chi c'\u00E8 nt\u00E2 p\u00E0ggina purr\u00ECa addivintari nun ligg\u00ECbbili",
          MSIENativeMMLWarning: "Internet Explorer havi bisognu d\u00FB plugin MathPlayer pi putiri labburari l'output MathML.",
          OperaNativeMMLWarning: "Opera supporta lu MathML di manera limitata, dunca passannu a l'output MathML certi sprissioni matim\u00E0tichi ponnu cump\u00E0riri mali.",
          SafariNativeMMLWarning: "Lu supportu d\u00FB t\u00F2 browser p\u00FB MathML nun mplimenta tutti li carattir\u00ECstichi d\u00EE quali MathJax fa usu, dunca certi sprissioni matim\u00E0tichi ponnu cump\u00E0riri mali.",
          FirefoxNativeMMLWarning: "Lu supportu d\u00FB t\u00F2 browser p\u00FB MathML nun mplimenta tutti li carattir\u00ECstichi d\u00EE quali MathJax fa usu, dunca certi sprissioni matim\u00E0tichi ponnu cump\u00E0riri mali.",
          MSIESVGWarning: "L'SVG nun \u00E8 mplimintatu nta Internet Explorer prima d\u00E2 virsioni 9, o quannu \u00E8mula la virsioni 8 e chiddi nfiriuri. Passannu a l'output SVG la matim\u00E0tica nun \u00E8 visualizzata bona.",
          LoadURL: "Carricari li dati di traduzzioni di st'URL:",
          BadURL: "L'URL avissi a cunt\u00E8niri nu file JavaScript ca difinisci li dati d\u00E2 traduzzioni di MathJax. \u003ELi noma d\u00EE file JavaScript av\u00ECssiru a finiri cu \u00AB.js\u00BB",
          BadData: "Nun arrinisc\u00ECu lu scarricamentu d\u00EE dati d\u00E2 traduzzioni di %1",
          SwitchAnyway: "Voi canciari lu giniraturi lu stissu?\n\n(Carca OK pi canciari, ANNULLA p'arristari c\u00FB giniraturi attuali)",
          ScaleMath: "Arridiminziunari tutta la matim\u00E0tica (rispettu \u00F4 testu circustanti) di",
          NonZeroScale: "Lu fatturi di scala nun havi a \u00E8ssiri zeru",
          PercentScale: "Lu fatturi di scala havi a \u00E8ssiri na pircintuali (p'esempiu 120%%)",
          IE8warning: "Sta cosa disattiva lu men\u00F9 di MathJax e li s\u00F2 funziunalit\u00E0 di zoom, pir\u00F2 si p\u00F2 sempri gr\u00E0piri lu men\u00F9 di MathJax cliccannu supra \u00EA sprissioni tinennu carcatu Alt.\n\nS\u00EE sicuru di vuliri canciari li mpustazzioni d\u00FB MathPlayer?",
          IE9warning: "Veni disattivatu lu men\u00F9 cuntistuali di MathJax, chi pir\u00F2 si p\u00F2 sempri gr\u00E0piri cliccannu supra \u00EA sprissioni tinennu carcatu Alt.",
          NoOriginalForm: "Nudda forma origginali \u00E8 dispun\u00ECbbili",
          Close: "Chiudi",
          EqSource: "Surgenti d\u00E2 f\u00F2rmula MathJax"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/scn/MathMenu.js");
