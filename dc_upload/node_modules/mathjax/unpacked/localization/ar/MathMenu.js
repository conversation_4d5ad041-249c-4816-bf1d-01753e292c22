/*************************************************************
 *
 *  MathJax/localization/ar/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ar","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "\u062A\u0638\u0647\u0631 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A \u0643",
          MathMLcode: "\u0643\u0648\u062F MathML",
          OriginalMathML: "MathML \u0627\u0644\u0623\u0635\u0644\u064A\u0629",
          TeXCommands: "\u0623\u0648\u0627\u0645\u0631 TeX",
          AsciiMathInput: "\u0625\u062F\u062E\u0627\u0644 AsciiMathML",
          Original: "\u0627\u0644\u0646\u0645\u0648\u0630\u062C \u0627\u0644\u0623\u0635\u0644\u064A",
          ErrorMessage: "\u0631\u0633\u0627\u0644\u0629 \u062E\u0637\u0623",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "\u0645\u062D\u062A\u0648\u0649 MathML",
          OpenMath: "OpenMath",
          texHints: "\u0623\u0638\u0647\u0631 \u062A\u0644\u0645\u064A\u062D\u0627\u062A TeX \u0641\u064A MathML",
          Settings: "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A",
          ZoomTrigger: "\u0645\u0634\u063A\u0644 \u0627\u0644\u062A\u0643\u0628\u064A\u0631",
          Click: "\u0627\u0636\u063A\u0637",
          DoubleClick: "\u0627\u0636\u063A\u0637 \u0645\u0631\u062A\u064A\u0646",
          NoZoom: "\u0644\u0627 \u062A\u0643\u0628\u064A\u0631",
          Option: "\u062E\u064A\u0627\u0631",
          Alt: "Alt",
          Control: "\u062A\u062D\u0643\u0645",
          Shift: "Shift",
          ZoomFactor: "\u0639\u0627\u0645\u0644 \u0627\u0644\u062A\u0643\u0628\u064A\u0631",
          Renderer: "\u0639\u0627\u0631\u0636 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A",
          MPHandles: "\u0627\u0644\u0633\u0645\u0627\u062D \u0628\u0645\u0639\u0627\u0644\u062C\u0629 MathPlayer:",
          MenuEvents: "\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0623\u062D\u062F\u0627\u062B",
          MouseEvents: "\u0623\u062D\u062F\u0627\u062B \u0627\u0644\u0641\u0623\u0631\u0629",
          MenuAndMouse: "\u0623\u062D\u062F\u0627\u062B \u0627\u0644\u0642\u0627\u0626\u0645\u0629 \u0648\u0627\u0644\u0641\u0623\u0631\u0629",
          FontPrefs: "\u062A\u0641\u0636\u064A\u0644\u0627\u062A \u0627\u0644\u062E\u0637",
          ForHTMLCSS: "\u0644HTML-CSS:",
          ContextMenu: "\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0645\u062D\u062A\u0648\u064A\u0627\u062A",
          Browser: "\u0645\u062A\u0635\u0641\u062D",
          Scale: "\u062D\u062C\u0645 \u0643\u0644 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A...",
          Locale: "\u0644\u063A\u0629",
          LoadLocale: "\u0627\u0644\u062A\u062D\u0645\u064A\u0644 \u0645\u0646 \u0631\u0627\u0628\u0637",
          About: "\u062D\u0648\u0644 \u0645\u0627\u062B \u062C\u0627\u0643\u0633",
          Help: "\u0645\u0633\u0627\u0639\u062F\u0629 \u0645\u0627\u062B \u062C\u0627\u0643\u0633",
          wofforotffonts: "\u062E\u0637\u0648\u0637 WOFF \u0623\u0648 OTF",
          eotffonts: "\u062E\u0637\u0648\u0637 EOT",
          svgfonts: "\u062E\u0637\u0648\u0637 SVG",
          WebkitNativeMMLWarning: "\u064A\u0628\u062F\u0648 \u0623\u0646 \u0645\u062A\u0635\u0641\u062D\u0643 \u0644\u0627 \u064A\u062F\u0639\u0645 MathML \u0623\u0635\u0644\u0627; \u0625\u0630\u0627 \u0642\u0645\u062A \u0628\u0627\u0644\u062A\u063A\u064A\u064A\u0631 \u0625\u0644\u0649 \u0639\u0631\u0636 MathML \u064A\u0645\u0643\u0646 \u0623\u0646 \u062A\u062A\u0639\u0630\u0631 \u0642\u0631\u0627\u0621\u0629 \u0635\u064A\u063A \u0627\u0644\u0635\u0641\u062D\u0629 \u0628\u0634\u0643\u0644 \u0635\u062D\u064A\u062D",
          MSIENativeMMLWarning: "\u064A\u062A\u0637\u0644\u0628 \u0625\u0646\u062A\u0631\u0646\u062A \u0625\u0643\u0633\u0644\u0648\u0631\u0631 \u0627\u0644\u0628\u0631\u0646\u0627\u0645\u062C \u0627\u0644\u0645\u0633\u0627\u0639\u062F MathPlayer \u0644\u0639\u0645\u0644\u064A\u0629 \u0646\u062A\u064A\u062C\u0629 MathML",
          OperaNativeMMLWarning: "\u062F\u0639\u0645 \u0623\u0648\u0628\u0631\u0627 MathML \u0645\u062D\u062F\u0648\u062F\u061B \u0625\u0630\u0627 \u0642\u0645\u062A \u0628\u0627\u0644\u062A\u063A\u064A\u064A\u0631 \u0625\u0644\u0649 \u0639\u0631\u0636 MathML \u064A\u0645\u0643\u0646 \u0623\u0644\u0627 \u062A\u064F\u0639\u0631\u0636 \u0628\u0639\u0636 \u0627\u0644\u062A\u0639\u0628\u064A\u0631\u0627\u062A \u0628\u0634\u0643\u0644 \u0635\u062D\u064A\u062D.",
          SafariNativeMMLWarning: "MathML \u0627\u0644\u0623\u0635\u0644\u064A \u0641\u064A \u0645\u062A\u0635\u0641\u062D\u0643 \u0644\u0627 \u064A\u0646\u0641\u0630 \u0643\u0627\u0641\u0629 \u0627\u0644\u0645\u064A\u0632\u0627\u062A \u0627\u0644\u062A\u064A \u064A\u0633\u062A\u062E\u062F\u0645\u0647\u0627 \u0645\u0627\u062B \u062C\u0627\u0643\u0633; \u0644\u0630\u0644\u0643 \u0628\u0639\u0636 \u0627\u0644\u062A\u0639\u0627\u0628\u064A\u0631 \u0642\u062F \u0644\u0627 \u062A\u0643\u0648\u0646 \u0635\u062D\u064A\u062D\u0629.",
          FirefoxNativeMMLWarning: "MathML \u0627\u0644\u0623\u0635\u0644\u064A \u0641\u064A \u0645\u062A\u0635\u0641\u062D\u0643 \u0644\u0627 \u064A\u0646\u0641\u0630 \u0643\u0627\u0641\u0629 \u0627\u0644\u0645\u064A\u0632\u0627\u062A \u0627\u0644\u062A\u064A \u064A\u0633\u062A\u062E\u062F\u0645\u0647\u0627 \u0645\u0627\u062B \u062C\u0627\u0643\u0633; \u0644\u0630\u0644\u0643 \u0628\u0639\u0636 \u0627\u0644\u062A\u0639\u0627\u0628\u064A\u0631 \u0642\u062F \u0644\u0627 \u062A\u0643\u0648\u0646 \u0635\u062D\u064A\u062D\u0629.",
          MSIESVGWarning: "\u0644\u0645 \u064A\u062A\u0645 \u062A\u0646\u0641\u064A\u0630 SVG \u0641\u064A \u0625\u0646\u062A\u0631\u0646\u062A \u0625\u0643\u0633\u0644\u0648\u0631\u0631 \u0642\u0628\u0644 IE9 \u0623\u0648 \u0639\u0646\u062F\u0645\u0627 \u062A\u0643\u0648\u0646 \u0645\u062D\u0627\u0643\u0627\u0629 IE8 \u0623\u0648 \u0623\u0642\u0644. \u0627\u0644\u062A\u062D\u0648\u0644 \u0625\u0644\u0649 \u0645\u062E\u0631\u062C\u0627\u062A SVG \u064A\u062A\u0633\u0628\u0628 \u0641\u064A \u0639\u062F\u0645 \u0639\u0631\u0636 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A \u0628\u0634\u0643\u0644 \u0635\u062D\u064A\u062D.",
          LoadURL: "\u062A\u062D\u0645\u064A\u0644 \u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u062A\u0631\u062C\u0645\u0629 \u0645\u0646 \u0647\u0630\u0627 \u0627\u0644\u0631\u0627\u0628\u0637:",
          BadURL: "\u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 \u0639\u0646\u0648\u0627\u0646 URL \u0644\u0645\u0644\u0641 \u062C\u0627\u0641\u0627 \u0633\u0643\u0631\u064A\u0628\u062A \u0627\u0644\u0630\u064A \u064A\u062D\u062F\u062F \u0628\u064A\u0627\u0646\u0627\u062A \u062A\u0631\u062C\u0645\u0629 \u0645\u0627\u062B \u062C\u0627\u0643\u0633. \u064A\u062C\u0628 \u0623\u0646 \u062A\u0646\u062A\u0647\u064A \u0623\u0633\u0645\u0627\u0621 \u0645\u0644\u0641\u0627\u062A \u062C\u0627\u0641\u0627 \u0633\u0643\u0631\u064A\u0628\u062A \u0628'.js",
          BadData: "\u0641\u0634\u0644 \u062A\u062D\u0645\u064A\u0644 \u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u062A\u0631\u062C\u0645\u0629 \u0645\u0646 %1",
          SwitchAnyway: "\u062A\u0628\u062F\u064A\u0644 \u0627\u0644\u0639\u0627\u0631\u0636 \u0639\u0644\u0649 \u0623\u064A \u062D\u0627\u0644\u061F (\u0627\u0636\u063A\u0637 \u0645\u0648\u0627\u0641\u0642\u0629 \u0644\u0644\u062A\u0628\u062F\u064A\u0644\u060C \u0648\u0625\u0644\u063A\u0627\u0621 \u0644\u0644\u0645\u0648\u0627\u0635\u0644\u0629 \u0628\u0627\u0644\u0639\u0627\u0631\u0636 \u0627\u0644\u062D\u0627\u0644\u064A)",
          ScaleMath: "\u062D\u062C\u0645 \u0643\u0644 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A (\u0628\u0627\u0644\u0645\u0642\u0627\u0631\u0646\u0629 \u0645\u0639 \u0627\u0644\u0646\u0635 \u0627\u0644\u0645\u062D\u064A\u0637) \u0628\u0648\u0627\u0633\u0637\u0629",
          NonZeroScale: "\u0627\u0644\u0642\u064A\u0627\u0633 \u0644\u0627 \u064A\u0645\u0643\u0646 \u0623\u0646 \u064A\u0643\u0648\u0646 \u0635\u0641\u0631",
          PercentScale: "\u0627\u0644\u0642\u064A\u0627\u0633 \u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 \u0646\u0633\u0628\u0629 \u0645\u0626\u0648\u064A\u0629 (\u0639\u0644\u0649 \u0633\u0628\u064A\u0644 \u0627\u0644\u0645\u062B\u0627\u0644 120%%)",
          IE8warning: "\u0633\u064A\u0624\u062F\u064A \u0630\u0644\u0643 \u0625\u0644\u0649 \u062A\u0639\u0637\u064A\u0644 \u0642\u0627\u0626\u0645\u0629 \u0648\u0645\u0632\u0627\u064A\u0627 \u062A\u0642\u0631\u064A\u0628 \u0645\u0627\u062B \u062C\u0627\u0643\u0633\u060C \u0648\u0644\u0643\u0646 \u064A\u0645\u0643\u0646\u0643 \u0627\u0644\u0636\u063A\u0637 \u0639\u0644\u0649 Alt \u0644\u0644\u062D\u0635\u0648\u0644 \u0639\u0644\u0649 \u0642\u0627\u0626\u0645\u0629 \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u0628\u062F\u0644\u0627 \u0645\u0646 \u0630\u0644\u0643. \u0647\u0644 \u062A\u0631\u064A\u062F \u062D\u0642\u0627 \u062A\u063A\u064A\u064A\u0631 \u0625\u0639\u062F\u0627\u062F\u0627\u062A MathPlayer\u061F",
          IE9warning: "\u0642\u0627\u0626\u0645\u0629 \u0645\u062D\u062A\u0648\u064A\u0627\u062A \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u0633\u0648\u0641 \u064A\u062A\u0645 \u062A\u0639\u0637\u064A\u0644\u0647\u0627\u060C \u0648\u0644\u0643\u0646 \u064A\u0645\u0643\u0646\u0643 \u0627\u0627\u0644\u0636\u063A\u0637 \u0639\u0644\u0649 Alt \u0644\u0644\u062D\u0635\u0648\u0644 \u0639\u0644\u0649 \u0642\u0627\u0626\u0645\u0629 \u0645\u0627\u062B \u062C\u0627\u0643\u0633 \u0628\u062F\u0644\u0627 \u0645\u0646 \u0630\u0644\u0643.",
          NoOriginalForm: "\u0644\u0627 \u064A\u0648\u062C\u062F \u0646\u0645\u0648\u0630\u062C \u0623\u0635\u0644\u064A \u0645\u062A\u0627\u062D",
          EqSource: "\u0645\u0639\u0627\u062F\u0644\u0629 \u0645\u0635\u062F\u0631 \u0645\u0627\u062B \u062C\u0627\u0643\u0633",
          FastPreview: "\u0645\u0639\u0627\u064A\u0646\u0629 \u0633\u0631\u064A\u0639\u0629"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ar/MathMenu.js");
