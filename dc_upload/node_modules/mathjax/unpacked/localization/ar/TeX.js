/*************************************************************
 *
 *  MathJax/localization/ar/TeX.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ar","TeX",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "\u0642\u0648\u0633 \u0645\u0641\u062A\u0648\u062D \u0625\u0636\u0627\u0641\u064A \u0623\u0648 \u063A\u0644\u0642 \u0642\u0648\u0633 \u0645\u0641\u0642\u0648\u062F",
          ExtraCloseMissingOpen: "\u0642\u0648\u0633 \u0645\u063A\u0644\u0642 \u0625\u0636\u0627\u0641\u064A \u0623\u0648 \u0641\u062A\u062D \u0642\u0648\u0633 \u0645\u0641\u0642\u0648\u062F",
          MissingLeftExtraRight: "\u064A\u0633\u0627\u0631 /\u0645\u0641\u0642\u0648\u062F \u0623\u0648 \u064A\u0645\u064A\u0646 /\u0625\u0636\u0627\u0641\u064A",
          ExtraLeftMissingRight: "\u064A\u0633\u0627\u0631 /\u0625\u0636\u0627\u0641\u064A \u0623\u0648 \u064A\u0645\u064A\u0646 /\u0645\u0641\u0642\u0648\u062F",
          Misplaced: "%1 \u0641\u064A \u063A\u064A\u0631 \u0645\u062D\u0644\u0647",
          AmbiguousUseOf: "\u0627\u0633\u062A\u062E\u062F\u0627\u0645 \u063A\u0627\u0645\u0636 \u0644%1",
          EnvBadEnd: "\\\u0628\u062F\u0627\u064A\u0629{%1} \u064A\u0646\u062A\u0647\u064A \u0628 \\\u0646\u0647\u0627\u064A\u0629{%2}",
          EnvMissingEnd: "\u0646\u0647\u0627\u064A\u0629\\\u0645\u0641\u0642\u0648\u062F\u0629{%1}",
          MissingBoxFor: "\u0635\u0646\u062F\u0648\u0642 \u0645\u0641\u0642\u0648\u062F \u0644 %1",
          UndefinedControlSequence: "%1 \u062A\u0633\u0644\u0633\u0644 \u0645\u0631\u0627\u0642\u0628\u0629 \u063A\u064A\u0631 \u0645\u0639\u0631\u0648\u0641",
          DoubleExponent: "\u0623\u0633 \u0645\u0632\u062F\u0648\u062C: \u0627\u0633\u062A\u062E\u062F\u0645 \u0627\u0644\u0623\u0642\u0648\u0627\u0633 \u0644\u0644\u062A\u0648\u0636\u064A\u062D",
          DoubleSubscripts: "\u0627\u0644\u062D\u0631\u0648\u0641 \u0627\u0644\u0633\u0641\u0644\u064A\u0629 \u0645\u0632\u062F\u0648\u062C\u0629: \u0627\u0633\u062A\u062E\u062F\u0645 \u0627\u0644\u0623\u0642\u0648\u0627\u0633 \u0644\u0644\u062A\u0648\u0636\u064A\u062D",
          DoubleExponentPrime: "\u0639\u062F\u062F \u0623\u0648\u0644\u064A \u064A\u0633\u0628\u0628 \u0623\u0633 \u0645\u0632\u062F\u0648\u062C: \u0627\u0633\u062A\u062E\u062F\u0645 \u0627\u0644\u0623\u0642\u0648\u0627\u0633 \u0644\u0644\u062A\u0648\u0636\u064A\u062D",
          CantUseHash1: "\u0644\u0627 \u064A\u0645\u0643\u0646\u0643 \u0627\u0633\u062A\u062E\u062F\u0627\u0645 '\u0627\u0644\u062D\u0631\u0641 \u0627\u0644\u0645\u0639\u0644\u0645 \u0627\u0644\u0643\u0644\u064A#' \u0641\u064A \u0648\u0636\u0639 \u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A",
          MisplacedMiddle: "%1 \u064A\u062C\u0628 \u0623\u0646 \u064A\u0643\u0648\u0646 \u0636\u0645\u0646 \\\u064A\u0633\u0627\u0631 \u0648\\\u064A\u0645\u064A\u0646",
          MisplacedLimits: "%1 \u0645\u0633\u0645\u0648\u062D \u0641\u0642\u0637 \u0639\u0644\u0649 \u0627\u0644\u0645\u0634\u063A\u0644\u064A\u0646",
          MisplacedMoveRoot: "%1 \u064A\u0645\u0643\u0646 \u0623\u0646 \u062A\u0638\u0647\u0631 \u0641\u0642\u0637 \u0641\u064A \u0627\u0644\u062C\u0630\u0631",
          MultipleCommand: "%1 \u0645\u062A\u0639\u062F\u062F",
          NotMathMLToken: "%1 \u0644\u064A\u0633 \u0639\u0646\u0635\u0631\u064B\u0627 \u0631\u0645\u0632\u064A\u064B\u0627",
          UnknownAttrForElement: "%1 \u0644\u064A\u0633\u062A \u0633\u0645\u0629 \u0645\u0639\u062A\u0631\u0641 \u0628\u0647\u0627 \u0644%2",
          ExtraAlignTab: "\u0645\u062D\u0627\u0630\u0627\u0629 \u0639\u0644\u0627\u0645\u0629 \u0627\u0644\u062A\u0628\u0648\u064A\u0628 \u0625\u0636\u0627\u0641\u064A\u0629 \u0641\u064A \\\u062D\u0627\u0644\u0627\u062A \u0627\u0644\u0646\u0635\u0648\u0635",
          InvalidEnv: "\u0627\u0633\u0645 \u0628\u064A\u0626\u0629 \u063A\u064A\u0631 \u0635\u0627\u0644\u062D '%1'",
          UnknownEnv: "\u0628\u064A\u0626\u0629 \u063A\u064A\u0631 \u0645\u0639\u0631\u0648\u0641\u0629 '%1'",
          ExtraCloseLooking: "\u063A\u0644\u0642 \u0642\u0648\u0633 \u0625\u0636\u0627\u0641\u064A \u0623\u062B\u0646\u0627\u0621 \u0627\u0644\u0628\u062D\u062B \u0639\u0646 %1",
          MissingOrUnrecognizedDelim: "\u0645\u062D\u062F\u062F \u0645\u0641\u0642\u0648\u062F \u0623\u0648 \u063A\u064A\u0631 \u0645\u0639\u062A\u0631\u0641 \u0628\u0647 \u0644 %1",
          MissingDimOrUnits: "\u0628\u0639\u062F (\u0623\u0648 \u0648\u062D\u062F\u0627\u062A\u0647) \u0645\u0641\u0642\u0648\u062F \u0644 %1",
          TokenNotFoundForCommand: "\u0644\u0645 \u064A\u062A\u0645 \u0627\u064A\u062C\u0627\u062F %1 \u0625\u0644\u064A %2",
          MathNotTerminated: "\u0627\u0644\u0631\u064A\u0627\u0636\u064A\u0627\u062A \u0644\u0627 \u062A\u0646\u062A\u0647\u064A \u0641\u064A \u0645\u0631\u0628\u0639 \u0627\u0644\u0646\u0635",
          IllegalMacroParam: "\u0645\u0631\u062C\u0639 \u0645\u0639\u0644\u0645 \u0643\u0644\u064A \u063A\u064A\u0631 \u0642\u0627\u0646\u0648\u0646\u064A",
          MaxBufferSize: "\u062A\u0645 \u062A\u062C\u0627\u0648\u0632 \u062D\u062C\u0645 \u0627\u0644\u0645\u062E\u0632\u0646 \u0627\u0644\u0645\u0624\u0642\u062A \u0627\u0644\u062F\u0627\u062E\u0644\u064A \u0644\u0645\u0627\u062B \u062C\u0627\u0643\u0633. \u0647\u0644 \u0647\u0646\u0627\u0643 \u0627\u0633\u062A\u062F\u0639\u0627\u0621 \u0645\u0627\u0643\u0631\u0648 \u0645\u0643\u0631\u0631\u061F",
          CommandNotAllowedInEnv: "%1 \u063A\u064A\u0631 \u0645\u0633\u0645\u0648\u062D \u0641\u064A \u0628\u064A\u0626\u0629 %2",
          MultipleLabel: "\u062A\u062D\u062F\u064A\u062F \u0645\u0636\u0627\u0639\u0641 \u0644\u062A\u0633\u0645\u064A\u0629 '%1'",
          CommandAtTheBeginingOfLine: "\u064A\u062C\u0628 \u0623\u0646 \u064A\u0623\u062A\u064A \u0641\u064A \u0628\u062F\u0627\u064A\u0629 \u0627\u0644\u0633\u0637\u0631 %1",
          IllegalAlign: "\u0645\u062D\u0627\u0630\u0627\u0629 \u063A\u064A\u0631 \u0642\u0627\u0646\u0648\u0646\u064A\u0629 \u0645\u062D\u062F\u062F\u0629 \u0641\u064A %1",
          BadMathStyleFor: "\u0646\u0645\u0637 \u0631\u064A\u0627\u0636\u064A \u0633\u064A\u0621 \u0644 %1",
          MultlineRowsOneCol: "\u0627\u0644\u0635\u0641\u0648\u0641 \u0636\u0645\u0646 \u0628\u064A\u0626\u0629 %1 \u064A\u062C\u0628 \u0623\u0646 \u062A\u062D\u062A\u0648\u064A \u0639\u0644\u0649 \u0639\u0645\u0648\u062F \u0648\u0627\u062D\u062F \u0628\u0627\u0644\u0636\u0628\u0637",
          MultipleBBoxProperty: "%1 \u0645\u062D\u062F\u062F \u0645\u0631\u062A\u064A\u0646 \u0641\u064A %2",
          ExtraEndMissingBegin: "%1 \u0625\u0636\u0627\u0641\u064A \u0623\u0648 \u0645\u0641\u0642\u0648\u062F \\begingroup",
          GlobalNotFollowedBy: "%1 \u0644\u0645 \u064A\u062A\u0645 \u062A\u062A\u0628\u0639\u0647 \\let, \\def, \u0623\u0648 \\newcommand",
          UndefinedColorModel: "\u0646\u0645\u0648\u0630\u062C \u0627\u0644\u0644\u0648\u0646 '%1' \u063A\u064A\u0631 \u0645\u0639\u0631\u0641",
          ModelArg1: "\u0642\u064A\u0645 \u0627\u0644\u0644\u0648\u0646 \u0644\u0644\u0646\u0645\u0648\u0630\u062C %1 \u062A\u062A\u0637\u0644\u0628 3 \u0623\u0631\u0642\u0627\u0645",
          InvalidDecimalNumber: "\u0639\u062F\u062F \u0639\u0634\u0631\u064A \u063A\u064A\u0631 \u0635\u0627\u0644\u062D",
          ModelArg2: "\u064A\u062C\u0628 \u0623\u0646 \u062A\u0643\u0648\u0646 \u0642\u064A\u0645 \u0627\u0644\u0644\u0648\u0646 \u0644\u0644\u0646\u0645\u0648\u0630\u062C %1 \u0628\u064A\u0646 %2 \u0648%3",
          InvalidNumber: "\u0639\u062F\u062F \u063A\u064A\u0631 \u0635\u0627\u0644\u062D",
          NoClosingChar: "\u0644\u0627 \u064A\u0645\u0643\u0646 \u0627\u0644\u0639\u062B\u0648\u0631 \u0639\u0644\u0649 \u0625\u063A\u0644\u0627\u0642 %1",
          IllegalParamNumber: "\u0639\u062F\u062F \u063A\u064A\u0631 \u0645\u0634\u0631\u0648\u0639 \u0644\u0644\u0645\u0639\u0627\u064A\u064A\u0631 \u0627\u0644\u0645\u062D\u062F\u062F\u0629 \u0641\u064A %1",
          CantUseHash2: "\u0627\u0633\u062A\u062E\u062F\u0627\u0645 \u063A\u064A\u0631 \u0627\u0644\u0645\u0634\u0631\u0648\u0639 \u0644# \u0641\u064A \u0642\u0627\u0644\u0628 \u0644%1",
          SequentialParam: "\u0645\u0639\u0644\u0645\u0627\u062A %1 \u064A\u062C\u0628 \u0623\u0646 \u064A\u062A\u0645 \u062A\u0631\u0642\u064A\u0645\u0647\u0627 \u0628\u0627\u0644\u062A\u0633\u0644\u0633\u0644",
          MissingReplacementString: "\u0633\u0644\u0633\u0644\u0629 \u0628\u062F\u064A\u0644\u0629 \u0644\u062A\u0639\u0631\u064A\u0641 %1 \u0645\u0641\u0642\u0648\u062F\u0629",
          MismatchUseDef: "\u0627\u0633\u062A\u062E\u062F\u0627\u0645 %1 \u0644\u0627 \u064A\u062A\u0637\u0627\u0628\u0642 \u0645\u0639 \u062A\u0639\u0631\u064A\u0641\u0647",
          NoClosingDelim: "\u0644\u0627 \u064A\u0645\u0643\u0646 \u0627\u0644\u0639\u062B\u0648\u0631 \u0639\u0644\u0649 \u0625\u063A\u0644\u0627\u0642 \u0645\u062D\u062F\u062F \u0644%1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ar/TeX.js");
