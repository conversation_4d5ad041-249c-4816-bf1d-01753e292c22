/*************************************************************
 *
 *  MathJax/localization/bg/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("bg","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "\u041F\u043E\u043A\u0430\u0436\u0438 \u043A\u0430\u0442\u043E",
          MathMLcode: "MathML \u043A\u043E\u0434",
          OriginalMathML: "\u041E\u0440\u0438\u0433\u0438\u043D\u0430\u043B\u043D\u0438\u044F\u0442 MathML \u043A\u043E\u0434",
          TeXCommands: "TeX \u043A\u043E\u043C\u0430\u043D\u0434\u0438",
          AsciiMathInput: "AsciiMathML \u043A\u043E\u0434",
          ErrorMessage: "\u0421\u044A\u043E\u0431\u0449\u0435\u043D\u0438\u0435 \u0437\u0430 \u0433\u0440\u0435\u0448\u043A\u0430",
          Annotation: "\u0410\u043D\u043E\u0442\u0430\u0446\u0438\u044F",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          OpenMath: "OpenMath",
          Settings: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",
          ZoomTrigger: "\u0423\u0432\u0435\u043B\u0438\u0447\u0430\u0432\u0430\u0439 \u0447\u0440\u0435\u0437",
          Hover: "\u041F\u043E\u0441\u043E\u0447\u0432\u0430\u043D\u0435",
          Click: "\u041A\u043B\u0438\u043A",
          DoubleClick: "\u0414\u0432\u043E\u0435\u043D \u043A\u043B\u0438\u043A",
          NoZoom: "\u0418\u0437\u043A\u043B\u044E\u0447\u0435\u043D\u043E",
          TriggerRequires: "\u0418\u0437\u0438\u0441\u043A\u0432\u0430\u0439 \u043D\u0430\u0442\u0438\u0441\u043D\u0430\u0442:",
          Option: "Option",
          Alt: "Alt",
          Command: "Command",
          Control: "Ctrl",
          Shift: "Shift",
          ZoomFactor: "\u0423\u0432\u0435\u043B\u0438\u0447\u0438 \u043D\u0430",
          Auto: "\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u043D\u043E",
          Browser: "\u0411\u0440\u0430\u0443\u0437\u044A\u0440",
          Locale: "\u0415\u0437\u0438\u043A",
          About: "\u0417\u0430 MathJax",
          Close: "\u0417\u0430\u0442\u0432\u043E\u0440\u0438"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/bg/MathMenu.js");
