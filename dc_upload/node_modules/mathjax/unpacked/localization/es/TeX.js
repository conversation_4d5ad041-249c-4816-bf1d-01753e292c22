/*************************************************************
 *
 *  MathJax/localization/es/TeX.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("es","TeX",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "Llave de apertura adicional o falta la llave de cierre",
          ExtraCloseMissingOpen: "Llave de cierre adicional o falta la llave de apertura",
          MissingLeftExtraRight: "Falta \\left o \\right adicional",
          MissingScript: "Falta argumento super\u00EDndice o sub\u00EDndice",
          ExtraLeftMissingRight: "\\left adicional o falta \\right",
          Misplaced: "%1 ubicado incorrectamente",
          MissingOpenForSub: "Falta la llave de apertura para el sub\u00EDndice",
          MissingOpenForSup: "Falta la llave de apertura para super\u00EDndice",
          AmbiguousUseOf: "Uso ambiguo de %1",
          EnvBadEnd: "\\begin{%1} termina con \\end{%2}",
          EnvMissingEnd: "Falta \\end{%1}",
          MissingBoxFor: "Falta la caja para %1",
          MissingCloseBrace: "Falta la llave de cierre",
          UndefinedControlSequence: "Secuencia de control indefinida %1",
          DoubleExponent: "Doble exponente: utiliza llaves para aclarar",
          DoubleSubscripts: "Doble sub\u00EDndices: utiliza llaves para aclarar",
          DoubleExponentPrime: "El s\u00EDmbolo \u00ABprima\u00BB causa un exponente doble, utiliza llaves para aclarar",
          CantUseHash1: "No puedes utilizar \"n\u00FAmero de car\u00E1cter de par\u00E1metro de macro\" en modo matem\u00E1tico",
          MisplacedMiddle: "%1 debe estar dentro de \\left y \\right",
          MisplacedLimits: "%1 est\u00E1 permitido solo en operadores",
          MisplacedMoveRoot: "%1 solo puede aparecer dentro de una ra\u00EDz",
          MultipleCommand: "M\u00FAltiples %1",
          IntegerArg: "El argumento para %1 debe ser un entero",
          NotMathMLToken: "%1 no es un elemento",
          InvalidMathMLAttr: "El atributo MathML no es v\u00E1lido: %1",
          UnknownAttrForElement: "%1 no es un atributo reconocido para %2",
          MaxMacroSub1: "Conteo m\u00E1ximo de sustituci\u00F3n de macro MathJax superado, \u00BFhay una llamada recursiva de macro?",
          MaxMacroSub2: "Se excedi\u00F3 el conteo m\u00E1ximo de sustituci\u00F3n MathJax, \u00BFhay un ambiente recursivo de LaTeX?",
          MissingArgFor: "Falta el argumento para %1",
          ExtraAlignTab: "Ficha de alineaci\u00F3n adicional en texto \\cases",
          BracketMustBeDimension: "El argumento de par\u00E9ntesis %1 debe ser una dimensi\u00F3n",
          InvalidEnv: "Nombre de entorno \u00AB%1\u00BB no v\u00E1lido",
          UnknownEnv: "Entorno desconocido \u00AB%1\u00BB",
          ExtraCloseLooking: "Llave de cierre adicional al buscar %1",
          MissingCloseBracket: "No se pudo encontrar \"]\" de cierre para el argumento %1",
          MissingOrUnrecognizedDelim: "Delimitador no reconocido o faltante para %1",
          MissingDimOrUnits: "Falta la dimensi\u00F3n o sus unidades para %1",
          TokenNotFoundForCommand: "No se pudo encontrar %1 para %2",
          MathNotTerminated: "La notaci\u00F3n matem\u00E1tica no termina en el cuadro de texto",
          IllegalMacroParam: "Referencia de par\u00E1metro de macro ilegal",
          MaxBufferSize: "Tama\u00F1o del b\u00FAfer interno de MathJax excedido. \u00BFHay una llamada macro recursiva?",
          CommandNotAllowedInEnv: "%1 no permitido en el entorno %2",
          MultipleLabel: "Se defini\u00F3 la etiqueta \u00AB%1\u00BB varias veces",
          CommandAtTheBeginingOfLine: "%1 debe aparecer al principio de la l\u00EDnea",
          IllegalAlign: "Se especific\u00F3 un alineamiento incorrecto en %1",
          BadMathStyleFor: "Estilo matem\u00E1tico err\u00F3neo para %1",
          PositiveIntegerArg: "El argumento de %1 debe ser un entero positivo",
          ErroneousNestingEq: "Anidaci\u00F3n err\u00F3nea de estructuras de ecuaciones",
          MultlineRowsOneCol: "Las filas dentro del entorno %1 deben tener exactamente una columna",
          MultipleBBoxProperty: "Se ha especificado %1 dos veces en %2",
          InvalidBBoxProperty: "\"%1\" no parece ser un color, una dimensi\u00F3n de relleno o un estilo",
          ExtraEndMissingBegin: "%1 adicional o falta \\begingroup",
          GlobalNotFollowedBy: "%1 no va seguido de \\let, \\def o \\newcommand",
          UndefinedColorModel: "El modelo de color \u00AB%1\u00BB no est\u00E1 definido",
          ModelArg1: "Los valores de color para el modelo %1 requieren 3 n\u00FAmeros",
          InvalidDecimalNumber: "El n\u00FAmero decimal no es v\u00E1lido",
          ModelArg2: "Los valores de color para el modelo %1 deben estar entre %2 y %3",
          InvalidNumber: "El n\u00FAmero no es v\u00E1lido",
          NewextarrowArg1: "El primer argumento de %1 debe ser un nombre de secuencia de control",
          NewextarrowArg2: "El segundo argumento de %1 debe ser dos n\u00FAmeros enteros separados por una coma",
          NewextarrowArg3: "El tercer argumento de %1 debe ser un n\u00FAmero de caracteres Unicode",
          NoClosingChar: "No se puede encontrar el %1 de cierre",
          IllegalControlSequenceName: "El nombre de la secuencia de control para %1 es incorrecto",
          IllegalParamNumber: "Se especific\u00F3 una cantidad incorrecta de par\u00E1metros en %1",
          MissingCS: "A %1 ha de a\u00F1ad\u00EDrsele una secuencia de control",
          CantUseHash2: "Uso incorrecto de \u00AB#\u00BB en la plantilla para %1",
          SequentialParam: "Los par\u00E1metros para %1 deben ser numerados de forma secuencial",
          MissingReplacementString: "Falta la cadena de sustituci\u00F3n para la definici\u00F3n de %1",
          MismatchUseDef: "El uso de %1 no coincide con su definici\u00F3n",
          RunawayArgument: "\u00BFArgumento de escape para %1?",
          NoClosingDelim: "No se puede encontrar el delimitador de cierre para %1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/es/TeX.js");
