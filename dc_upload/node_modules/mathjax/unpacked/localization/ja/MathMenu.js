/*************************************************************
 *
 *  MathJax/localization/ja/MathMenu.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("ja","MathMenu",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          Show: "\u6570\u5F0F\u3092\u6B21\u306E\u5F62\u5F0F\u3067\u8868\u793A",
          MathMLcode: "MathML \u30B3\u30FC\u30C9",
          OriginalMathML: "\u5143\u306E MathML",
          TeXCommands: "TeX \u30B3\u30DE\u30F3\u30C9",
          AsciiMathInput: "AsciiMathML \u5165\u529B",
          Original: "\u5143\u306E\u5F62\u5F0F",
          ErrorMessage: "\u30A8\u30E9\u30FC \u30E1\u30C3\u30BB\u30FC\u30B8",
          Annotation: "\u6CE8\u91C8",
          TeX: "TeX",
          StarMath: "StarMath",
          Maple: "Maple",
          ContentMathML: "Content MathML",
          OpenMath: "OpenMath",
          texHints: "MathML \u3067 TeX \u306E\u30D2\u30F3\u30C8\u3092\u8868\u793A",
          Settings: "\u6570\u5F0F\u306E\u8A2D\u5B9A",
          ZoomTrigger: "\u30BA\u30FC\u30E0\u306E\u958B\u59CB\u64CD\u4F5C",
          Hover: "\u30DB\u30D0\u30FC",
          Click: "\u30AF\u30EA\u30C3\u30AF",
          DoubleClick: "\u30C0\u30D6\u30EB\u30AF\u30EA\u30C3\u30AF",
          NoZoom: "\u30BA\u30FC\u30E0\u306A\u3057",
          TriggerRequires: "\u5FC5\u8981\u306A\u30AD\u30FC:",
          Option: "Option",
          Alt: "Alt",
          Command: "Command",
          Control: "Ctrl",
          Shift: "Shift",
          ZoomFactor: "\u30BA\u30FC\u30E0\u306E\u500D\u7387",
          Renderer: "\u6570\u5F0F\u30EC\u30F3\u30C0\u30E9\u30FC",
          MPHandles: "\u6570\u5F0F\u30D7\u30EC\u30FC\u30E4\u30FC\u306B\u51E6\u7406\u3055\u305B\u308B\u30A4\u30D9\u30F3\u30C8:",
          MenuEvents: "\u30E1\u30CB\u30E5\u30FC \u30A4\u30D9\u30F3\u30C8",
          MouseEvents: "\u30DE\u30A6\u30B9 \u30A4\u30D9\u30F3\u30C8",
          MenuAndMouse: "\u30DE\u30A6\u30B9\u3068\u30E1\u30CB\u30E5\u30FC\u306E\u30A4\u30D9\u30F3\u30C8",
          FontPrefs: "\u30D5\u30A9\u30F3\u30C8\u306E\u8A2D\u5B9A",
          ForHTMLCSS: "HTML-CSS:",
          Auto: "\u81EA\u52D5",
          TeXLocal: "TeX (\u30ED\u30FC\u30AB\u30EB)",
          TeXWeb: "TeX (Web)",
          TeXImage: "TeX (\u753B\u50CF)",
          STIXLocal: "STIX (\u30ED\u30FC\u30AB\u30EB)",
          STIXWeb: "STIX (Web)",
          AsanaMathWeb: "Asana Math (Web)",
          GyrePagellaWeb: "Gyre Pagella (Web)",
          GyreTermesWeb: "Gyre Termes (Web)",
          LatinModernWeb: "Latin Modern (Web)",
          NeoEulerWeb: "Neo Euler (Web)",
          ContextMenu: "\u30B3\u30F3\u30C6\u30AD\u30B9\u30C8 \u30E1\u30CB\u30E5\u30FC",
          Browser: "\u30D6\u30E9\u30A6\u30B6\u30FC",
          Scale: "\u3059\u3079\u3066\u306E\u6570\u5F0F\u306E\u500D\u7387\u3092\u5909\u66F4...",
          Discoverable: "\u30DB\u30D0\u30FC\u6642\u306B\u5F37\u8ABF",
          Locale: "\u8A00\u8A9E",
          LoadLocale: "URL \u304B\u3089\u8AAD\u307F\u8FBC\u3080...",
          About: "MathJax \u306B\u3064\u3044\u3066",
          Help: "MathJax \u30D8\u30EB\u30D7",
          localTeXfonts: "\u30ED\u30FC\u30AB\u30EB TeX \u30D5\u30A9\u30F3\u30C8\u3092\u4F7F\u7528",
          webTeXfonts: "Web TeX \u30D5\u30A9\u30F3\u30C8\u3092\u4F7F\u7528",
          imagefonts: "\u753B\u50CF\u30D5\u30A9\u30F3\u30C8\u3092\u4F7F\u7528",
          localSTIXfonts: "\u30ED\u30FC\u30AB\u30EB STIX \u30D5\u30A9\u30F3\u30C8\u3092\u4F7F\u7528",
          webSVGfonts: "Web SVG \u30D5\u30A9\u30F3\u30C8\u3092\u4F7F\u7528",
          genericfonts: "\u6C4E\u7528 Unicode \u30D5\u30A9\u30F3\u30C8\u3092\u4F7F\u7528",
          wofforotffonts: "WOFF \u30D5\u30A9\u30F3\u30C8\u307E\u305F\u306F OTF \u30D5\u30A9\u30F3\u30C8",
          eotffonts: "EOT \u30D5\u30A9\u30F3\u30C8",
          svgfonts: "SVG \u30D5\u30A9\u30F3\u30C8",
          WebkitNativeMMLWarning: "\u3054\u4F7F\u7528\u4E2D\u306E\u30D6\u30E9\u30A6\u30B6\u30FC\u306F MathML \u306B\u30CD\u30A4\u30C6\u30A3\u30D6\u5BFE\u5FDC\u3057\u3066\u3044\u306A\u3044\u3068\u601D\u308F\u308C\u308B\u305F\u3081\u3001MathML \u51FA\u529B\u306B\u5207\u308A\u66FF\u3048\u308B\u3068\u30DA\u30FC\u30B8\u5185\u306E\u6570\u5F0F\u304C\u5224\u8AAD\u4E0D\u80FD\u306B\u306A\u308B\u304A\u305D\u308C\u304C\u3042\u308A\u307E\u3059\u3002",
          MSIENativeMMLWarning: "Internet Explorer \u3067\u306F\u3001MathML \u51FA\u529B\u3092\u51E6\u7406\u3059\u308B\u305F\u3081\u306B MathPlayer \u30D7\u30E9\u30B0\u30A4\u30F3\u304C\u5FC5\u8981\u3067\u3059\u3002",
          OperaNativeMMLWarning: "Opera \u306F MathML \u306B\u5B8C\u5168\u306B\u306F\u5BFE\u5FDC\u3057\u3066\u3044\u306A\u3044\u305F\u3081\u3001MathML \u51FA\u529B\u306B\u5207\u308A\u66FF\u3048\u308B\u3068\u6570\u5F0F\u306E\u63CF\u753B\u304C\u4E0D\u5B8C\u5168\u306B\u306A\u308B\u304A\u305D\u308C\u304C\u3042\u308A\u307E\u3059\u3002",
          SafariNativeMMLWarning: "\u3042\u306A\u305F\u306E\u30D6\u30E9\u30A6\u30B6\u30FC\u306E\u30CD\u30A4\u30C6\u30A3\u30D6 MathML \u306F\u3001MathJax \u304C\u4F7F\u7528\u3059\u308B\u6A5F\u80FD\u3092\u3059\u3079\u3066\u306F\u5B9F\u88C5\u3057\u3066\u3044\u306A\u3044\u305F\u3081\u3001\u6570\u5F0F\u306E\u4E00\u90E8\u304C\u9069\u5207\u306B\u63CF\u753B\u3055\u308C\u306A\u3044\u304A\u305D\u308C\u304C\u3042\u308A\u307E\u3059\u3002",
          FirefoxNativeMMLWarning: "\u3042\u306A\u305F\u306E\u30D6\u30E9\u30A6\u30B6\u30FC\u306E\u30CD\u30A4\u30C6\u30A3\u30D6 MathML \u306F\u3001MathJax \u304C\u4F7F\u7528\u3059\u308B\u6A5F\u80FD\u3092\u3059\u3079\u3066\u306F\u5B9F\u88C5\u3057\u3066\u3044\u306A\u3044\u305F\u3081\u3001\u6570\u5F0F\u306E\u4E00\u90E8\u304C\u9069\u5207\u306B\u63CF\u753B\u3055\u308C\u306A\u3044\u304A\u305D\u308C\u304C\u3042\u308A\u307E\u3059\u3002",
          MSIESVGWarning: "IE9 \u3088\u308A\u524D\u306E\u30D0\u30FC\u30B8\u30E7\u30F3\u306E\u5834\u5408\u3001\u307E\u305F\u306F IE8 \u4EE5\u524D\u3092\u30A8\u30DF\u30E5\u30EC\u30FC\u30C8\u3057\u3066\u3044\u308B\u5834\u5408\u3001Internet Explorer \u306B\u306F SVG \u304C\u5B9F\u88C5\u3055\u308C\u3066\u3044\u307E\u305B\u3093\u3002SVG \u306B\u5207\u308A\u66FF\u3048\u308B\u3068\u3001\u6570\u5F0F\u304C\u9069\u5207\u306B\u8868\u793A\u3055\u308C\u306A\u304F\u306A\u308A\u307E\u3059\u3002",
          LoadURL: "\u7FFB\u8A33\u30C7\u30FC\u30BF\u3092\u8AAD\u307F\u8FBC\u3080 URL:",
          BadURL: "MathJax \u7FFB\u8A33\u30C7\u30FC\u30BF\u3092\u5B9A\u7FA9\u3059\u308B JavaScript \u30D5\u30A1\u30A4\u30EB\u306E URL \u3067\u3042\u308B\u5FC5\u8981\u304C\u3042\u308A\u3001JavaScript \u306E\u30D5\u30A1\u30A4\u30EB\u540D\u306E\u672B\u5C3E\u306F\u300C.js\u300D\u3067\u3042\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059\u3002",
          BadData: "%1 \u304B\u3089\u7FFB\u8A33\u30C7\u30FC\u30BF\u3092\u8AAD\u307F\u8FBC\u3081\u307E\u305B\u3093\u3067\u3057\u305F",
          SwitchAnyway: "\u30EC\u30F3\u30C0\u30E9\u30FC\u3092\u672C\u5F53\u306B\u5207\u308A\u66FF\u3048\u307E\u3059\u304B?\n\n(\u5207\u308A\u66FF\u3048\u308B\u306B\u306F OK \u3092\u3001\u73FE\u5728\u306E\u30EC\u30F3\u30C0\u30E9\u30FC\u306E\u307E\u307E\u306B\u3059\u308B\u306B\u306F\u30AD\u30E3\u30F3\u30BB\u30EB\u3092\u62BC\u3057\u3066\u304F\u3060\u3055\u3044)",
          ScaleMath: "\u3059\u3079\u3066\u306E\u6570\u5F0F\u306E\u500D\u7387 (\u5468\u56F2\u306E\u30C6\u30AD\u30B9\u30C8\u3068\u306E\u6BD4)",
          NonZeroScale: "\u500D\u7387\u306B 0 \u306F\u6307\u5B9A\u3067\u304D\u307E\u305B\u3093",
          PercentScale: "\u500D\u7387\u306F\u767E\u5206\u7387\u3067\u306A\u3051\u308C\u3070\u306A\u308A\u307E\u305B\u3093 (\u4F8B: 120%%)",
          IE8warning: "MathJax \u306E\u30E1\u30CB\u30E5\u30FC\u3068\u30BA\u30FC\u30E0\u6A5F\u80FD\u304C\u7121\u52B9\u306B\u306A\u308A\u307E\u3059\u304C\u3001\u4EE3\u308F\u308A\u306B\u6570\u5F0F\u3092 Alt+\u30AF\u30EA\u30C3\u30AF\u3059\u308B\u3068 MathJax \u306E\u30E1\u30CB\u30E5\u30FC\u3092\u4F7F\u7528\u3067\u304D\u307E\u3059\u3002\n\nMathPlayer \u306E\u8A2D\u5B9A\u3092\u672C\u5F53\u306B\u5909\u66F4\u3057\u307E\u3059\u304B?",
          IE9warning: "MathJax \u306E\u30B3\u30F3\u30C6\u30AD\u30B9\u30C8 \u30E1\u30CB\u30E5\u30FC\u304C\u7121\u52B9\u306B\u306A\u308A\u307E\u3059\u304C\u3001\u4EE3\u308F\u308A\u306B\u6570\u5F0F\u3092 Alt+\u30AF\u30EA\u30C3\u30AF\u3059\u308B\u3068 MathJax \u306E\u30E1\u30CB\u30E5\u30FC\u3092\u4F7F\u7528\u3067\u304D\u307E\u3059\u3002",
          NoOriginalForm: "\u5143\u306E\u5F62\u5F0F\u304C\u898B\u3064\u304B\u308A\u307E\u305B\u3093",
          Close: "\u9589\u3058\u308B",
          EqSource: "MathJax \u6570\u5F0F\u306E\u30BD\u30FC\u30B9"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/ja/MathMenu.js");
