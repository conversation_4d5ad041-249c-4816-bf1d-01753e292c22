/*************************************************************
 *
 *  MathJax/localization/uk/TeX.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("uk","TeX",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          ExtraOpenMissingClose: "\u0417\u0430\u0439\u0432\u0430 \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u0430 \u0444\u0456\u0433\u0443\u0440\u043D\u0430 \u0434\u0443\u0436\u043A\u0430 \u0430\u0431\u043E \u0432\u0456\u0434\u0441\u0443\u0442\u043D\u044F \u0437\u0430\u043A\u0440\u0438\u0442\u0430 \u0444\u0456\u0433\u0443\u0440\u043D\u0430 \u0434\u0443\u0436\u043A\u0430",
          ExtraCloseMissingOpen: "\u0417\u0430\u0439\u0432\u0430 \u0437\u0430\u043A\u0440\u0438\u0442\u0430 \u0444\u0456\u0433\u0443\u0440\u043D\u0430 \u0434\u0443\u0436\u043A\u0430 \u0430\u0431\u043E \u0432\u0456\u0434\u0441\u0443\u0442\u043D\u044F \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u0430 \u0444\u0456\u0433\u0443\u0440\u043D\u0430 \u0434\u0443\u0436\u043A\u0430",
          MissingLeftExtraRight: "\u0412\u0456\u0434\u0441\u0443\u0442\u043D\u0456\u0439 \\left \u0430\u0431\u043E \u0437\u0430\u0439\u0432\u0438\u0439 \\right",
          MissingScript: "\u0412\u0456\u0434\u0441\u0443\u0442\u043D\u0456 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442\u0438 \u0432\u0435\u0440\u0445\u043D\u044C\u043E\u0433\u043E \u0456 \u043D\u0438\u0436\u043D\u044C\u043E\u0433\u043E \u0456\u043D\u0434\u0435\u043A\u0441\u0456\u0432",
          ExtraLeftMissingRight: "\u0417\u0430\u0439\u0432\u0438\u0439 \\left \u0430\u0431\u043E \u0432\u0456\u0434\u0441\u0443\u0442\u043D\u0456\u0439 \\right",
          Misplaced: "\u041D\u0435\u0432\u0456\u0440\u043D\u0438\u0439 %1",
          MissingOpenForSub: "\u0411\u0440\u0430\u043A\u0443\u0454 \u0432\u0456\u0434\u043A\u0440\u0438\u0432\u0430\u044E\u0447\u043E\u0457 \u0434\u0443\u0436\u043A\u0438 \u0434\u043B\u044F \u043D\u0438\u0436\u043D\u044C\u043E\u0433\u043E \u0456\u043D\u0434\u0435\u043A\u0441\u0443",
          MissingOpenForSup: "\u0411\u0440\u0430\u043A\u0443\u0454 \u0432\u0456\u0434\u043A\u0440\u0438\u0432\u0430\u044E\u0447\u043E\u0457 \u0434\u0443\u0436\u043A\u0438 \u0434\u043B\u044F \u0432\u0435\u0440\u0445\u043D\u044C\u043E\u0433\u043E \u0456\u043D\u0434\u0435\u043A\u0441\u0443",
          AmbiguousUseOf: "\u041D\u0435\u043E\u0434\u043D\u043E\u0437\u043D\u0430\u0447\u043D\u0435 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F %1",
          EnvBadEnd: "\\begin{%1} \u0437\u0430\u043A\u0456\u043D\u0447\u0438\u0432\u0441\u044F \\end{%2}",
          EnvMissingEnd: "\u0411\u0440\u0430\u043A\u0443\u0454 \\end{%1}",
          MissingBoxFor: "\u0412\u0456\u0434\u0441\u0443\u0442\u043D\u0454 \u043F\u043E\u043B\u0435 \u0434\u043B\u044F %1",
          MissingCloseBrace: "\u0412\u0456\u0434\u0441\u0443\u0442\u043D\u044F \u0437\u0430\u043A\u0440\u0438\u0432\u0430\u044E\u0447\u0430 \u0434\u0443\u0436\u043A\u0430",
          UndefinedControlSequence: "\u041D\u0435\u0432\u0438\u0437\u043D\u0430\u0447\u0435\u043D\u0430 \u043A\u043E\u043C\u0430\u043D\u0434\u0430 %1",
          DoubleExponent: "\u041F\u043E\u0434\u0432\u0456\u0439\u043D\u0438\u0439 \u0441\u0442\u0435\u043F\u0456\u043D\u044C: \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u0434\u0443\u0436\u043A\u0438 \u0434\u043B\u044F \u0443\u0442\u043E\u0447\u043D\u0435\u043D\u043D\u044F",
          DoubleSubscripts: "\u041F\u043E\u0434\u0432\u0456\u0439\u043D\u0438\u0439 \u043D\u0438\u0436\u043D\u0456\u0439 \u0456\u043D\u0434\u0435\u043A\u0441: \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u0434\u0443\u0436\u043A\u0438 \u0443\u0442\u043E\u0447\u0435\u043D\u043D\u044F",
          DoubleExponentPrime: "\u0417\u043D\u0430\u043A \u0448\u0442\u0440\u0438\u0445\u0430 \u0441\u043F\u0440\u0438\u0447\u0438\u043D\u0438\u0432 \u043F\u043E\u0434\u0432\u0456\u0439\u043D\u0438\u0439 \u0441\u0442\u0435\u043F\u0456\u043D\u044C: \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u0439\u0442\u0435 \u0434\u0443\u0436\u043A\u0438 \u0434\u043B\u044F \u0443\u0442\u043E\u0447\u043D\u0435\u043D\u043D\u044F",
          CantUseHash1: "\u041D\u0435 \u043C\u043E\u0436\u043D\u0430 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0441\u0438\u043C\u0432\u043E\u043B \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0430 \u043C\u0430\u043A\u0440\u043E\u0441\u0443 # \u0443 \u043C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u0447\u043D\u043E\u043C\u0443 \u0440\u0435\u0436\u0438\u043C\u0456",
          MisplacedMiddle: "%1 \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u0432 \u043C\u0435\u0436\u0430\u0445 \\left \u0456 \\right",
          MisplacedLimits: "%1 \u0434\u043E\u0437\u0432\u043E\u043B\u0435\u043D\u043E \u0442\u0456\u043B\u044C\u043A\u0438 \u043D\u0430 \u043E\u043F\u0435\u0440\u0430\u0442\u043E\u0440\u0430\u0445",
          MisplacedMoveRoot: "%1 \u043C\u043E\u0436\u0435 \u0437'\u044F\u0432\u0438\u0442\u0438\u0441\u044F \u043B\u0438\u0448\u0435 \u043F\u0456\u0434 \u043A\u043E\u0440\u0435\u043D\u0435\u043C",
          MultipleCommand: "\u041A\u0456\u043B\u044C\u043A\u0430 %1",
          IntegerArg: "\u0410\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0434\u043B\u044F  %1  \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u0446\u0456\u043B\u0438\u043C \u0447\u0438\u0441\u043B\u043E\u043C",
          NotMathMLToken: "%1 \u043D\u0435 \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u043C \u043C\u0430\u0440\u043A\u0435\u0440\u0430",
          InvalidMathMLAttr: "\u041D\u0435\u043F\u0440\u0438\u043F\u0443\u0441\u0442\u0438\u043C\u0438\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442 MathML: %1",
          UnknownAttrForElement: "%1 \u043D\u0435\u0440\u043E\u0437\u043F\u0456\u0437\u043D\u0430\u043D\u0438\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442 \u0434\u043B\u044F %2",
          MaxMacroSub1: "\u041F\u0435\u0440\u0435\u0432\u0438\u0449\u0435\u043D\u043E \u043C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u044C\u043D\u0443 \u043A\u0456\u043B\u044C\u043A\u0456\u0441\u0442\u044C \u0432\u0438\u043A\u043B\u0438\u043A\u0456\u0432 \u043C\u0430\u043A\u0440\u043E\u0441\u0456\u0432; \u0430\u0431\u043E \u0432\u0456\u0434\u0431\u0443\u0432\u0430\u0454\u0442\u044C\u0441\u044F \u0440\u0435\u043A\u0443\u0440\u0441\u0438\u0432\u043D\u0438\u0439 \u0432\u0438\u043A\u043B\u0438\u043A \u043C\u0430\u043A\u0440\u043E\u0441\u0443?",
          MaxMacroSub2: "\u041F\u0435\u0440\u0435\u0432\u0438\u0449\u0435\u043D\u043E \u043C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u044C\u043D\u0443 \u043A\u0456\u043B\u044C\u043A\u0456\u0441\u0442\u044C \u043F\u0456\u0434\u0441\u0442\u0430\u043D\u043E\u0432\u043E\u043A MathJax; \u041C\u043E\u0436\u043B\u0438\u0432\u043E, \u0440\u0435\u043A\u0443\u0440\u0441\u0456\u044F \u0432 \u0441\u0435\u0440\u0435\u0434\u043E\u0432\u0438\u0449\u0456 LaTeX?",
          MissingArgFor: "\u041F\u0440\u043E\u043F\u0443\u0449\u0435\u043D\u0438\u0439 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0434\u043B\u044F %1",
          ExtraAlignTab: "\u0417\u0430\u0439\u0432\u0438\u0439 \u0442\u0430\u0431\u0443\u043B\u044F\u0442\u043E\u0440 \u0442\u0435\u043A\u0441\u0442\u0443 \u0443 \\cases",
          BracketMustBeDimension: "\u0410\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0443 \u0434\u0443\u0436\u043A\u0430\u0445 \u0434\u043B\u044F %1 \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u0440\u043E\u0437\u043C\u0456\u0440\u043E\u043C",
          InvalidEnv: "\u041D\u0435\u0432\u0456\u0440\u043D\u0435 \u0456\u043C'\u044F \u0441\u0435\u0440\u0435\u0434\u043E\u0432\u0438\u0449\u0430 '%1'",
          UnknownEnv: "\u041D\u0435\u0432\u0456\u0434\u043E\u043C\u0435 \u0441\u0435\u0440\u0435\u0434\u043E\u0432\u0438\u0449\u0435 '%1'",
          ExtraCloseLooking: "\u0417\u0430\u0439\u0432\u0430 \u0437\u0430\u043A\u0440\u0438\u0432\u0430\u044E\u0447\u0430 \u0434\u0443\u0436\u043A\u0430 \u0432\u0438\u043D\u0438\u043A\u043B\u0430 \u043F\u0456\u0434 \u0447\u0430\u0441 \u043F\u043E\u0448\u0443\u043A\u0443 %1",
          MissingCloseBracket: "\u041D\u0435 \u043C\u043E\u0436\u043B\u0438\u0432\u043E \u0437\u043D\u0430\u0439\u0442\u0438 \u0437\u0430\u043A\u0440\u0438\u0432\u0430\u044E\u0447\u0443 ']' \u0434\u043B\u044F \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442\u0443 \u0434\u043E %1",
          MissingOrUnrecognizedDelim: "\u0412\u0456\u0434\u0441\u0443\u0442\u043D\u0456\u0439 \u0430\u0431\u043E \u043D\u0435\u0440\u043E\u0437\u043F\u0456\u0437\u043D\u0430\u043D\u0438\u0439 \u0440\u043E\u0437\u0434\u0456\u043B\u044C\u043D\u0438\u043A \u0434\u043B\u044F %1",
          MissingDimOrUnits: "\u0412\u0456\u0434\u0441\u0443\u0442\u043D\u0456\u0439 \u0440\u043E\u0437\u043C\u0456\u0440 \u0430\u0431\u043E \u0439\u043E\u0433\u043E \u043E\u0434\u0438\u043D\u0438\u0446\u0456 \u0434\u043B\u044F %1",
          TokenNotFoundForCommand: "\u041D\u0435 \u043C\u043E\u0436\u043B\u0438\u0432\u043E \u0437\u043D\u0430\u0439\u0442\u0438 %1 \u0434\u043B\u044F %2",
          MathNotTerminated: "\u041C\u0430\u0442\u0435\u043C\u0430\u0442\u0438\u043A\u0430 \u043D\u0435 \u0437\u0430\u0432\u0435\u0440\u0448\u0443\u0454\u0442\u044C\u0441\u044F \u0432 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u043E\u043C\u0443 \u043F\u043E\u043B\u0456",
          IllegalMacroParam: "\u041D\u0435\u0437\u0430\u043A\u043E\u043D\u043D\u0435 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u043D\u0430 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440 \u043C\u0430\u043A\u0440\u043E\u0441\u0443",
          MaxBufferSize: "\u041F\u0435\u0440\u0435\u0432\u0438\u0449\u0435\u043D\u043E \u0440\u043E\u0437\u043C\u0456\u0440 \u0432\u043D\u0443\u0442\u0440\u0456\u0448\u043D\u044C\u043E\u0433\u043E \u0431\u0443\u0444\u0435\u0440\u0430 MathJax; \u0442\u0430\u043C \u0454 \u0440\u0435\u043A\u0443\u0440\u0441\u0438\u0432\u043D\u0438\u0439 \u0432\u0438\u043A\u043B\u0438\u043A \u043C\u0430\u043A\u0440\u043E\u0441\u0443?",
          CommandNotAllowedInEnv: "%1 \u043D\u0435 \u0434\u043E\u043F\u0443\u0441\u043A\u0430\u0454\u0442\u044C\u0441\u044F \u0432 \u0441\u0435\u0440\u0435\u0434\u043E\u0432\u0438\u0449\u0456 %2",
          MultipleLabel: "\u041C\u0456\u0442\u043A\u0430 '%1' \u0432\u0438\u0437\u043D\u0430\u0447\u0435\u043D\u0430 \u043A\u0456\u043B\u044C\u043A\u0430 \u0440\u0430\u0437\u0456\u0432",
          CommandAtTheBeginingOfLine: "%1 \u043C\u0430\u0454 \u0437\u043D\u0430\u0445\u043E\u0434\u0438\u0442\u0438\u0441\u044F \u043D\u0430 \u043F\u043E\u0447\u0430\u0442\u043A\u0443 \u0440\u044F\u0434\u043A\u0430",
          IllegalAlign: "\u041D\u0435\u0437\u0430\u043A\u043E\u043D\u043D\u0435 \u0432\u0438\u0440\u0456\u0432\u043D\u044E\u0432\u0430\u043D\u043D\u044F \u0432\u043A\u0430\u0437\u0430\u043D\u0435 \u0443 %1",
          BadMathStyleFor: "\u041D\u0435\u0432\u0456\u0440\u043D\u0438\u0439 \u0441\u0438\u043D\u0442\u0430\u043A\u0441\u0438\u0441 \u0434\u043B\u044F %1",
          PositiveIntegerArg: "\u0410\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0434\u043B\u044F %1 \u043F\u043E\u0432\u0438\u043D\u0435\u043D \u0431\u0443\u0442\u0438 \u0434\u043E\u0434\u0430\u0442\u043D\u0438\u043C \u0446\u0456\u043B\u0438\u043C \u0447\u0438\u0441\u043B\u043E\u043C",
          ErroneousNestingEq: "\u041F\u043E\u043C\u0438\u043B\u043A\u043E\u0432\u0456 \u0432\u043A\u043B\u0430\u0434\u0435\u043D\u043E\u0441\u0442\u0456 \u0441\u0442\u0440\u0443\u043A\u0442\u0443\u0440 \u0440\u0456\u0432\u043D\u044F\u043D\u043D\u044F",
          MultlineRowsOneCol: "\u0420\u044F\u0434\u043A\u0438 \u0443 \u0441\u0435\u0440\u0435\u0434\u043E\u0432\u0438\u0449\u0456 %1 \u043C\u0443\u0441\u044F\u0442\u044C \u043C\u0430\u0442\u0438 \u043B\u0438\u0448\u0435 \u043E\u0434\u0438\u043D \u0441\u0442\u043E\u0432\u043F\u0435\u0446\u044C",
          MultipleBBoxProperty: "%1 \u0432\u043A\u0430\u0437\u0430\u043D\u043E \u0434\u0432\u0456\u0447\u0456 \u0432 %2",
          InvalidBBoxProperty: "'%1' \u0437\u043E\u0432\u0441\u0456\u043C \u043D\u0435 \u0441\u0445\u043E\u0436\u0438\u0439 \u043D\u0430 \u043A\u043E\u043B\u0456\u0440, \u0434\u043E\u0434\u0430\u0442\u043A\u043E\u0432\u0438\u0439 \u0440\u043E\u0437\u043C\u0456\u0440 \u0430\u0431\u043E \u0441\u0442\u0438\u043B\u044C",
          ExtraEndMissingBegin: "\u0417\u0430\u0439\u0432\u0430 %1 \u0430\u0431\u043E \u0432\u0456\u0434\u0441\u0443\u0442\u043D\u044F \\begingroup",
          GlobalNotFollowedBy: "%1 \u043D\u0435 \u0441\u043B\u0456\u0434\u0443\u0454 \u043F\u0456\u0441\u043B\u044F \\let, \\def \u0430\u0431\u043E \\newcommand",
          UndefinedColorModel: "\u041A\u043E\u043B\u0456\u0440\u043D\u0430 \u043C\u043E\u0434\u0435\u043B\u044C '%1' \u043D\u0435 \u0432\u0438\u0437\u043D\u0430\u0447\u0435\u043D\u0430",
          ModelArg1: "\u0417\u043D\u0430\u0447\u0435\u043D\u043D\u044F \u043A\u043E\u043B\u044C\u043E\u0440\u0443 \u0434\u043B\u044F \u043C\u043E\u0434\u0435\u043B\u0456 %1 \u0432\u0438\u043C\u0430\u0433\u0430\u044E\u0442\u044C 3 \u0446\u0438\u0444\u0440\u0438",
          InvalidDecimalNumber: "\u041D\u0435\u0432\u0456\u0440\u043D\u0435 \u0434\u0435\u0441\u044F\u0442\u043A\u043E\u0432\u0435 \u0447\u0438\u0441\u043B\u043E",
          ModelArg2: "\u0417\u043D\u0430\u0447\u0435\u043D\u043D\u044F \u043A\u043E\u043B\u044C\u043E\u0440\u0443 \u0434\u043B\u044F \u043C\u043E\u0434\u0435\u043B\u0456 %1 \u043F\u043E\u0432\u0438\u043D\u043D\u043E \u0431\u0443\u0442\u0438 \u043C\u0456\u0436 \u0442\u0435\u0433\u0430\u043C\u0438 %2 \u0456 %3",
          InvalidNumber: "\u041D\u0435\u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u0435 \u0447\u0438\u0441\u043B\u043E",
          NewextarrowArg1: "\u041F\u0435\u0440\u0448\u0438\u0439 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 %1 \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u043D\u0430\u0437\u0432\u043E\u044E \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0430 \u043A\u0435\u0440\u0456\u0432\u043D\u043E\u0457 \u043F\u043E\u0441\u043B\u0456\u0434\u043E\u0432\u043D\u043E\u0441\u0442\u0456",
          NewextarrowArg2: "\u0414\u0440\u0443\u0433\u0438\u0439 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0434\u043B\u044F %1 \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u0434\u0432\u043E\u043C\u0430 \u0446\u0456\u043B\u0438\u043C\u0438 \u0447\u0438\u0441\u043B\u0430\u043C\u0438, \u0440\u043E\u0437\u0434\u0456\u043B\u0435\u043D\u0438\u043C\u0438 \u043A\u043E\u043C\u0430\u043C\u0438",
          NewextarrowArg3: "\u0422\u0440\u0435\u0442\u0456\u0439 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0434\u043B\u044F %1 \u043C\u0430\u0454 \u0431\u0443\u0442\u0438 \u0447\u0438\u0441\u043B\u043E\u0432\u0438\u043C \u044E\u043D\u0456\u043A\u043E\u0434\u043D\u0438\u043C \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u043C",
          NoClosingChar: "\u041D\u0435 \u043C\u043E\u0436\u043B\u0438\u0432\u043E \u0437\u043D\u0430\u0439\u0442\u0438 \u0432\u0456\u0434\u043A\u0440\u0438\u0432\u0430\u044E\u0447\u043E\u0457 %1",
          IllegalControlSequenceName: "\u041D\u0435\u0432\u0456\u0440\u043D\u0435 \u0456\u043C'\u044F \u043A\u0435\u0440\u0456\u0432\u043D\u043E\u0457 \u043F\u043E\u0441\u043B\u0456\u0434\u043E\u0432\u043D\u043E\u0441\u0442\u0456 \u0434\u043B\u044F %1",
          IllegalParamNumber: "\u041D\u0435\u0432\u0456\u0440\u043D\u0435 \u0447\u0438\u0441\u043B\u043E \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0456\u0432, \u0437\u0430\u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0445 \u0443 %1",
          MissingCS: "\u041F\u0456\u0441\u043B\u044F %1 \u043C\u0443\u0441\u0438\u0442\u044C \u0441\u043B\u0456\u0434\u0443\u0432\u0430\u0442\u0438 \u043A\u0435\u0440\u0456\u0432\u043D\u0430 \u043F\u043E\u0441\u043B\u0456\u0434\u043E\u0432\u043D\u0456\u0441\u0442\u044C",
          CantUseHash2: "\u041D\u0435\u0437\u0430\u043A\u043E\u043D\u043D\u0435 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F # \u0443 \u0448\u0430\u0431\u043B\u043E\u043D\u0456 \u0434\u043B\u044F %1",
          SequentialParam: "\u041F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0438 \u0434\u043B\u044F %1 \u043F\u043E\u0432\u0438\u043D\u043D\u0456 \u0431\u0443\u0442\u0438 \u043F\u0440\u043E\u043D\u0443\u043C\u0435\u0440\u043E\u0432\u0430\u043D\u0456 \u043F\u043E\u0441\u043B\u0456\u0434\u043E\u0432\u043D\u043E",
          MissingReplacementString: "\u0412\u0456\u0434\u0441\u0443\u0442\u043D\u0456\u0439 \u0440\u044F\u0434\u043E\u043A \u0437\u0430\u043C\u0456\u043D\u0438 \u0434\u043B\u044F \u0432\u0438\u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F %1",
          MismatchUseDef: "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F %1 \u043D\u0435 \u0432\u0456\u0434\u043F\u043E\u0432\u0456\u0434\u0430\u0454 \u0439\u043E\u0433\u043E \u0432\u0438\u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044E",
          RunawayArgument: "\u0412\u0442\u0440\u0430\u0447\u0435\u043D\u0438\u0439 \u0430\u0440\u0433\u0443\u043C\u0435\u043D\u0442 \u0434\u043B\u044F %1?",
          NoClosingDelim: "\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0437\u043D\u0430\u0439\u0442\u0438 \u0437\u0430\u043A\u0440\u0438\u0432\u0430\u044E\u0447\u043E\u0433\u043E \u0440\u043E\u0437\u0434\u0456\u043B\u044C\u043D\u0438\u043A\u0430 \u0434\u043B\u044F%1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/uk/TeX.js");
