/*************************************************************
 *
 *  MathJax/localization/zh-hant/MathML.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
MathJax.Localization.addTranslation("zh-hant","MathML",{
        version: "2.7.9",
        isLoaded: true,
        strings: {
          BadMglyph: "\u4E0D\u6B63\u78BA\u7684mglyph\uFF1A%1",
          BadMglyphFont: "\u932F\u8AA4\u5B57\u578B\uFF1A%1",
          MathPlayer: "MathJax\u6C92\u6709\u8FA6\u6CD5\u8A2D\u7F6EMathPlayer\u3002\n\n\u5982\u679CMathPlayer\u672A\u88AB\u5B89\u88DD\uFF0C\u60A8\u9700\u8981\u5148\u5B89\u88DD\u6B64\u5957\u4EF6\u3002\n\u5426\u5247\u60A8\u7684\u5B89\u5168\u8A2D\u7F6E\u5167\u5BB9\u53EF\u80FD\u6703\u963B\u7919ActiveX\u63A7\u5236\u65B9\u9762\u7684\u904B\u4F5C\u3002\u82E5\u662FIE\u700F\u89BD\u5668\u9EDE\u9078\u9078\u55AE\u88E1\u7684\u300C\u5DE5\u5177\u300D\u4E2D\u7684\u300C\u7DB2\u969B\u7DB2\u8DEF\u9078\u9805\u300D\u5F8C\u518D\u9078\u64C7\u300C\u5B89\u5168\u6027\u300D\uFF0C\u7136\u5F8C\u6309\u4E0B\u300C\u81EA\u8A02\u5C64\u7D1A\u300D\u6309\u9375\u3002\u6AA2\u67E5\u95DC\u65BC\u300C\u57F7\u884CActiveX\u63A7\u5236\u9805\u300D\u548C\u300C\u4E8C\u9032\u4F4D\u548C\u6307\u4EE4\u78BC\u884C\u70BA\u300D\u7684\u8A2D\u5B9A\u662F\u5426\u958B\u555F\u3002\n\n\u76EE\u524D\u60A8\u6240\u770B\u5230\u7684\u662F\u932F\u8AA4\u8A0A\u606F\u800C\u975E\u6578\u5B78\u516C\u5F0F\u6392\u7248\u554F\u984C",
          CantCreateXMLParser: "MathJax\u7121\u6CD5\u5EFA\u7ACB\u4E00\u500B\u7528\u65BCMathML\u7684XML\u89E3\u6790\u3002\n\n\u6AA2\u67E5\u300C\u4EE5Script\u7A0B\u5F0F\u64CD\u63A7\u5B89\u5168\u6A19\u793A\u7684ActiveX \u63A7\u5236\u9805\u300D\u8A2D\u5B9A\u662F\u5426\u958B\u555F\uFF08\u82E5\u662FIE\u700F\u89BD\u5668\u9EDE\u9078\u9078\u55AE\u88E1\u7684\u300C\u5DE5\u5177\u300D\u4E2D\u7684\u300C\u7DB2\u969B\u7DB2\u8DEF\u9078\u9805\u300D\u5F8C\u518D\u9078\u64C7\u300C\u5B89\u5168\u6027\u300D\uFF0C\u7136\u5F8C\u6309\u4E0B\u300C\u81EA\u8A02\u5C64\u7D1A\u300D\u6309\u9375\u4F86\u627E\u5C0B\uFF09\u3002",
          UnknownNodeType: "\u4E0D\u660E\u7684\u7BC0\u9EDE\u985E\u578B\uFF1A%1",
          UnexpectedTextNode: "\u975E\u9810\u671F\u7684\u6587\u5B57\u7BC0\u9EDE\uFF1A%1",
          ErrorParsingMathML: "\u89E3\u6790MathML\u51FA\u932F",
          ParsingError: "\u89E3\u6790MathML\u51FA\u932F\uFF1A %1",
          MathMLSingleElement: "MathML\u5FC5\u9808\u4EE5\u55AE\u4E00\u5143\u7D20\u69CB\u6210",
          MathMLRootElement: "MathML\u5FC5\u9808\u7531\u003Cmath\u003E\u5143\u7D20\u69CB\u6210\uFF0C\u800C\u975E%1"
        }
});

MathJax.Ajax.loadComplete("[MathJax]/localization/zh-hant/MathML.js");
