/*************************************************************
 *
 *  MathJax/jax/output/CommonHTML/fonts/TeX/SansSerif-Bold.js
 *
 *  Copyright (c) 2015-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

(function (CHTML) {

var font = 'MathJax_SansSerif-Bold';

CHTML.FONTDATA.FONTS[font] = {
  className: CHTML.FONTDATA.familyName(font),
  centerline: 250, ascent: 750, descent: 250,
  weight: 'bold',
  0x20: [0,0,250,0,0],               // SPACE
  0x21: [694,0,367,110,256],         // EXCLAMATION MARK
  0x22: [694,-442,558,37,420],       // QUOTATION MARK
  0x23: [694,193,917,61,855],        // NUMBER SIGN
  0x24: [750,56,550,49,488],         // DOLLAR SIGN
  0x25: [750,56,1029,61,966],        // PERCENT SIGN
  0x26: [716,22,831,47,769],         // AMPERSAND
  0x27: [694,-442,306,80,226],       // APOSTROPHE
  0x28: [750,249,428,79,366],        // LEFT PARENTHESIS
  0x29: [750,250,428,61,348],        // RIGHT PARENTHESIS
  0x2A: [750,-293,550,67,482],       // ASTERISK
  0x2B: [617,116,856,61,794],        // PLUS SIGN
  0x2C: [146,106,306,80,226],        // COMMA
  0x2D: [273,-186,367,12,305],       // HYPHEN-MINUS
  0x2E: [146,0,306,80,226],          // FULL STOP
  0x2F: [750,249,550,61,488],        // SOLIDUS
  0x30: [715,22,550,43,506],         // DIGIT ZERO
  0x31: [716,-1,550,76,473],         // DIGIT ONE
  0x32: [716,0,550,46,495],          // DIGIT TWO
  0x33: [716,22,550,46,503],         // DIGIT THREE
  0x34: [694,0,550,31,518],          // DIGIT FOUR
  0x35: [694,22,550,37,494],         // DIGIT FIVE
  0x36: [716,22,550,46,503],         // DIGIT SIX
  0x37: [695,11,550,46,503],         // DIGIT SEVEN
  0x38: [715,22,550,46,503],         // DIGIT EIGHT
  0x39: [716,22,550,46,503],         // DIGIT NINE
  0x3A: [458,0,306,80,226],          // COLON
  0x3B: [458,106,306,80,226],        // SEMICOLON
  0x3D: [407,-94,856,61,794],        // EQUALS SIGN
  0x3F: [705,0,519,61,457],          // QUESTION MARK
  0x40: [704,11,733,61,671],         // COMMERCIAL AT
  0x41: [694,0,733,42,690],          // LATIN CAPITAL LETTER A
  0x42: [694,-1,733,92,671],         // LATIN CAPITAL LETTER B
  0x43: [704,11,703,61,647],         // LATIN CAPITAL LETTER C
  0x44: [694,-1,794,92,732],         // LATIN CAPITAL LETTER D
  0x45: [691,0,642,92,595],          // LATIN CAPITAL LETTER E
  0x46: [691,0,611,92,564],          // LATIN CAPITAL LETTER F
  0x47: [705,11,733,61,659],         // LATIN CAPITAL LETTER G
  0x48: [694,0,794,92,702],          // LATIN CAPITAL LETTER H
  0x49: [694,0,331,85,246],          // LATIN CAPITAL LETTER I
  0x4A: [694,22,519,46,427],         // LATIN CAPITAL LETTER J
  0x4B: [694,0,764,92,701],          // LATIN CAPITAL LETTER K
  0x4C: [694,0,581,92,534],          // LATIN CAPITAL LETTER L
  0x4D: [694,0,978,92,886],          // LATIN CAPITAL LETTER M
  0x4E: [694,0,794,92,702],          // LATIN CAPITAL LETTER N
  0x4F: [716,22,794,62,731],         // LATIN CAPITAL LETTER O
  0x50: [694,0,703,92,641],          // LATIN CAPITAL LETTER P
  0x51: [716,106,794,62,732],        // LATIN CAPITAL LETTER Q
  0x52: [694,0,703,92,654],          // LATIN CAPITAL LETTER R
  0x53: [716,22,611,49,549],         // LATIN CAPITAL LETTER S
  0x54: [688,0,733,40,692],          // LATIN CAPITAL LETTER T
  0x55: [694,22,764,92,672],         // LATIN CAPITAL LETTER U
  0x56: [694,-1,733,27,705],         // LATIN CAPITAL LETTER V
  0x57: [694,0,1039,24,1014],        // LATIN CAPITAL LETTER W
  0x58: [694,0,733,37,694],          // LATIN CAPITAL LETTER X
  0x59: [694,0,733,24,708],          // LATIN CAPITAL LETTER Y
  0x5A: [694,0,672,61,616],          // LATIN CAPITAL LETTER Z
  0x5B: [750,250,343,79,318],        // LEFT SQUARE BRACKET
  0x5D: [750,250,343,24,263],        // RIGHT SQUARE BRACKET
  0x5E: [694,-537,550,108,441],      // CIRCUMFLEX ACCENT
  0x5F: [-23,110,550,0,549],         // LOW LINE
  0x61: [475,11,525,31,472],         // LATIN SMALL LETTER A
  0x62: [694,10,561,54,523],         // LATIN SMALL LETTER B
  0x63: [475,11,489,37,457],         // LATIN SMALL LETTER C
  0x64: [694,11,561,37,507],         // LATIN SMALL LETTER D
  0x65: [474,10,511,30,480],         // LATIN SMALL LETTER E
  0x66: [705,0,336,29,381],          // LATIN SMALL LETTER F
  0x67: [469,206,550,17,534],        // LATIN SMALL LETTER G
  0x68: [694,0,561,53,508],          // LATIN SMALL LETTER H
  0x69: [695,0,256,46,208],          // LATIN SMALL LETTER I
  0x6A: [695,205,286,-71,232],       // LATIN SMALL LETTER J
  0x6B: [694,0,531,63,496],          // LATIN SMALL LETTER K
  0x6C: [694,0,256,54,201],          // LATIN SMALL LETTER L
  0x6D: [469,0,867,53,815],          // LATIN SMALL LETTER M
  0x6E: [468,0,561,53,508],          // LATIN SMALL LETTER N
  0x6F: [474,11,550,32,518],         // LATIN SMALL LETTER O
  0x70: [469,194,561,54,523],        // LATIN SMALL LETTER P
  0x71: [469,194,561,37,507],        // LATIN SMALL LETTER Q
  0x72: [469,0,372,54,356],          // LATIN SMALL LETTER R
  0x73: [474,10,422,30,396],         // LATIN SMALL LETTER S
  0x74: [589,10,404,20,373],         // LATIN SMALL LETTER T
  0x75: [458,11,561,52,508],         // LATIN SMALL LETTER U
  0x76: [458,0,500,26,473],          // LATIN SMALL LETTER V
  0x77: [458,0,744,24,719],          // LATIN SMALL LETTER W
  0x78: [458,0,500,24,475],          // LATIN SMALL LETTER X
  0x79: [458,205,500,29,473],        // LATIN SMALL LETTER Y
  0x7A: [458,0,476,31,442],          // LATIN SMALL LETTER Z
  0x7E: [344,-198,550,92,457],       // TILDE
  0xA0: [0,0,250,0,0],               // NO-BREAK SPACE
  0x131: [458,0,256,54,201],         // LATIN SMALL LETTER DOTLESS I
  0x237: [458,205,286,-71,232],      // LATIN SMALL LETTER DOTLESS J
  0x300: [694,-537,0,-458,-218],     // COMBINING GRAVE ACCENT
  0x301: [694,-537,0,-334,-93],      // COMBINING ACUTE ACCENT
  0x302: [694,-537,0,-442,-109],     // COMBINING CIRCUMFLEX ACCENT
  0x303: [694,-548,0,-458,-93],      // COMBINING TILDE
  0x304: [660,-560,0,-474,-77],      // COMBINING MACRON
  0x306: [694,-552,0,-470,-80],      // COMBINING BREVE
  0x307: [695,-596,0,-356,-194],     // COMBINING DOT ABOVE
  0x308: [695,-595,0,-459,-91],      // COMBINING DIAERESIS
  0x30A: [694,-538,0,-365,-119],     // COMBINING RING ABOVE
  0x30B: [694,-537,0,-440,-94],      // COMBINING DOUBLE ACUTE ACCENT
  0x30C: [657,-500,0,-442,-109],     // COMBINING CARON
  0x393: [691,0,581,92,534],         // GREEK CAPITAL LETTER GAMMA
  0x394: [694,0,917,60,856],         // GREEK CAPITAL LETTER DELTA
  0x398: [716,22,856,62,793],        // GREEK CAPITAL LETTER THETA
  0x39B: [694,0,672,41,630],         // GREEK CAPITAL LETTER LAMDA
  0x39E: [688,0,733,46,686],         // GREEK CAPITAL LETTER XI
  0x3A0: [691,0,794,92,702],         // GREEK CAPITAL LETTER PI
  0x3A3: [694,0,794,61,732],         // GREEK CAPITAL LETTER SIGMA
  0x3A5: [715,0,856,62,793],         // GREEK CAPITAL LETTER UPSILON
  0x3A6: [694,0,794,62,732],         // GREEK CAPITAL LETTER PHI
  0x3A8: [694,0,856,61,794],         // GREEK CAPITAL LETTER PSI
  0x3A9: [716,0,794,49,744],         // GREEK CAPITAL LETTER OMEGA
  0x2013: [327,-240,550,0,549],      // EN DASH
  0x2014: [327,-240,1100,0,1099],    // EM DASH
  0x2018: [694,-443,306,81,226],     // LEFT SINGLE QUOTATION MARK
  0x2019: [694,-442,306,80,226],     // RIGHT SINGLE QUOTATION MARK
  0x201C: [694,-443,558,138,520],    // LEFT DOUBLE QUOTATION MARK
  0x201D: [694,-442,558,37,420]      // RIGHT DOUBLE QUOTATION MARK
};

CHTML.fontLoaded("TeX/"+font.substr(8));

})(MathJax.OutputJax.CommonHTML);
