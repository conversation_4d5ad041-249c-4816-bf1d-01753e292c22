/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/AMS/Regular/MathOperators.js
 *
 *  Copyright (c) 2011-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_AMS'],
  {
    // COMPLEMENT
    0x2201: [846,22,500,56,444,'404 269Q412 269 418 267T428 261T435 253T441 245L444 240V172Q444 103 443 96Q440 81 431 65T403 27T344 -7T250 -21T156 -8T97 27T69 65T58 96Q56 103 56 413Q56 722 58 729Q74 822 215 845Q221 846 229 846H243Q282 846 290 845Q422 826 443 729Q444 722 444 653V586L442 583Q441 580 440 578T436 573T430 567T423 562T415 558T404 556Q377 556 367 583Q364 590 364 654V719Q363 721 360 726T355 733Q326 766 250 766H249Q235 766 219 765T174 752T137 719V107Q145 83 178 71T251 58H254Q340 58 364 107V172Q364 176 364 187T363 204Q363 269 404 269'],

    // THERE DOES NOT EXIST
    0x2204: [860,166,556,55,497,'55 676Q55 688 66 694H199L333 696L351 772Q364 827 370 843T386 860Q393 860 399 854T406 841Q406 836 391 765L375 696Q375 694 431 694H484Q491 688 497 681V12L493 5L486 1L353 -1H219L202 -79Q184 -153 180 -159Q175 -166 165 -166Q146 -166 146 -148Q146 -141 161 -76T177 -4Q177 -1 122 -1H68Q55 12 55 20T66 39H126L186 41L219 181Q226 215 234 251T246 305T251 325Q251 328 166 328H79Q68 345 68 347Q68 352 75 359L82 368H262L291 505Q298 539 306 575T319 630T324 650V654H68Q55 669 55 676ZM457 368V654H411Q366 654 366 652Q365 651 361 634T349 580T333 514Q303 373 302 372V368H457ZM457 39V328H375Q293 328 293 325Q292 322 260 183T228 41T344 39H457'],

    // EMPTY SET
    0x2205: [587,3,778,55,720,'624 470Q624 468 639 446T668 382T683 291Q683 181 612 99T437 -1Q425 -2 387 -2T337 -1Q245 18 193 70L179 81L131 39Q96 8 89 3T75 -3Q55 -3 55 17Q55 24 61 30T111 73Q154 113 151 113Q151 114 140 130T115 177T95 241Q94 253 94 291T95 341Q112 431 173 495Q265 587 385 587Q410 587 437 581Q522 571 582 513L595 501L642 541Q689 586 695 586Q696 586 697 586T699 587Q706 587 713 583T720 568Q720 560 711 551T664 510Q651 499 642 490T628 475T624 470ZM564 477Q517 522 448 539Q428 546 375 546Q290 546 229 492T144 370Q133 332 133 279Q136 228 151 195Q157 179 168 160T184 141Q186 141 375 307T564 477ZM642 290Q642 318 637 343T625 386T611 416T598 436T593 444Q590 444 402 277T213 108Q213 104 231 89T293 55T392 37Q495 37 568 111T642 290'],

    // SMALL CONTAINS AS MEMBER
    0x220D: [440,1,429,102,456,'154 -1Q122 -1 112 3T102 26Q102 63 158 63H178Q192 64 206 65T228 66T240 68Q301 85 324 146L329 157H244Q158 157 153 161Q149 162 145 169T140 183Q140 201 158 215L167 221H256L344 223L349 237Q352 262 352 287Q352 308 351 315Q341 352 315 368T256 385Q231 385 206 376T166 356T149 346Q143 346 138 364T132 388Q132 396 147 406Q198 440 252 440Q291 440 318 435Q421 404 451 301Q456 288 456 248V234Q456 151 391 86Q330 25 240 3Q212 -1 154 -1'],

    // MINUS SIGN
    0x2212: [270,-230,500,84,417,'84 237T84 250T98 270H402Q417 262 417 250T402 230H98Q84 237 84 250'],

    // DOT PLUS
    0x2214: [766,93,778,57,722,'339 717Q339 739 354 752T388 766Q410 766 424 751T439 716T424 681T390 666Q369 666 354 681T339 717ZM57 237T57 250T71 270H369V425L370 581Q380 594 389 594Q402 594 409 579V270H707Q722 262 722 250T707 230H409V-79Q401 -93 391 -93H389H387Q375 -93 369 -79V230H71Q57 237 57 250'],

    // SET MINUS
    0x2216: [430,23,778,91,685,'91 404T91 410T97 423T111 430Q117 430 395 224Q676 13 678 10Q685 3 685 -3T678 -16T664 -23Q658 -23 380 184T98 397Q91 404 91 410'],

    // PROPORTIONAL TO
    0x221D: [472,-28,778,56,722,'56 250Q56 346 122 409T276 472Q349 472 407 430T486 326L489 316Q490 317 493 326T501 345T514 367T531 393Q557 425 602 448T698 472Q722 472 722 452Q722 437 702 435T642 421T571 377Q520 323 520 250Q520 179 568 126T693 68Q722 66 722 48Q722 28 698 28Q636 28 576 67T493 174L490 184Q489 181 483 167T475 150T468 136T458 120T447 107T432 90T412 73Q350 28 277 28Q188 28 122 91T56 250ZM199 68T278 68T408 122T459 250Q459 322 414 370T308 430Q302 431 273 431Q204 431 150 380T96 250Q96 176 147 122'],

    // ANGLE
    0x2220: [694,0,722,55,666,'71 0L68 2Q65 3 63 5T58 11T55 20Q55 22 57 28Q67 43 346 361Q397 420 474 508Q595 648 616 671T647 694T661 688T666 674Q666 668 663 663Q662 662 627 622T524 503T390 350L120 41L386 40H653Q666 30 666 20Q666 8 651 0H71'],

    // MEASURED ANGLE
    0x2221: [714,20,722,55,666,'71 0L68 2Q65 3 63 5T58 11T55 20Q55 22 57 28Q64 38 348 373T638 712Q644 714 646 714Q653 714 659 709T666 694V693Q666 687 633 647Q619 631 576 580Q528 524 495 485Q336 296 329 289Q328 288 348 264T395 182T433 54L434 40H651Q666 32 666 20T651 0H436Q431 -20 416 -20Q400 -20 396 -4V0H71ZM394 40Q394 51 389 76T366 149T319 234L302 256L119 41L256 40H394'],

    // SPHERICAL ANGLE
    0x2222: [551,51,722,55,666,'666 -32Q666 -51 646 -51Q639 -51 365 85L75 228Q55 238 55 250Q55 257 59 262T68 268L72 270L611 536Q642 551 647 551T659 547T666 532Q666 521 657 515L525 449Q525 448 535 424T556 352T566 250T556 148T536 77T525 51L657 -15Q666 -21 666 -32ZM526 250Q526 297 517 342T499 409T488 431Q487 431 304 341T121 250T304 159T488 69Q526 143 526 250'],

    // DIVIDES
    0x2223: [430,23,222,91,131,'91 417Q104 430 111 430T131 417V-10Q116 -23 111 -23T91 -10V417'],

    // DOES NOT DIVIDE
    0x2224: [750,252,278,-20,296,'118 737Q131 750 138 750L151 746L158 739V579L160 421L213 470Q269 519 276 519Q284 519 290 513T296 499V498Q296 493 291 488T244 445Q225 428 213 417L158 368V-239Q143 -252 136 -252L124 -248L120 -241L118 44V328L62 279Q4 231 0 230Q-8 230 -14 236T-20 250Q-20 257 -11 265T62 332L118 384V737'],

    // PARALLEL TO
    0x2225: [431,23,389,55,331,'55 417Q69 431 76 431T95 419V-12Q84 -23 76 -23Q72 -23 69 -22T62 -16T55 -10V417ZM293 419Q300 431 310 431L324 424L331 417V-10Q316 -23 309 -23L297 -19L293 -12V419'],

    // NOT PARALLEL TO
    0x2226: [751,250,500,-20,518,'131 737Q134 739 138 743T144 748T151 750T171 737V199L327 357V737Q340 750 347 750Q351 750 353 749T360 743T367 737V397L429 457Q493 518 498 519Q506 519 512 512T518 500Q518 489 442 417L367 339V-237Q352 -250 346 -250L333 -243L327 -237V301L171 143V-237Q156 -250 151 -250T131 -237V101L69 41Q24 -3 15 -12T0 -21Q-8 -21 -14 -14T-20 -2Q-20 5 -7 19T56 81L131 159V737'],

    // THEREFORE
    0x2234: [471,82,667,24,643,'273 411Q273 437 291 454T334 471Q358 471 375 454T393 411T376 368T333 351Q307 351 290 368T273 411ZM84 38Q110 38 126 21T143 -22Q143 -46 127 -64T83 -82Q57 -82 41 -65T24 -22Q24 4 41 21T84 38ZM524 -22Q524 4 541 21T584 38Q608 38 625 21T643 -22Q643 -45 627 -63T583 -82Q557 -82 541 -65T524 -22'],

    // BECAUSE
    0x2235: [471,82,667,23,644,'23 411Q23 437 41 454T84 471Q108 471 125 454T143 411T126 368T83 351Q57 351 40 368T23 411ZM523 411Q523 437 541 454T584 471Q608 471 625 454T643 411T626 368T583 351Q557 351 540 368T523 411ZM274 -22Q274 4 291 21T334 38Q356 38 374 22T392 -22T375 -65T333 -82Q307 -82 291 -65T274 -22'],

    // TILDE OPERATOR
    0x223C: [365,-132,778,55,719,'73 132Q55 132 55 172Q55 220 79 272Q95 301 111 319Q148 353 195 363Q199 364 212 364Q262 364 294 350T408 272Q472 222 522 212Q537 208 555 208Q606 208 646 243Q671 268 680 296T691 342T702 365Q713 365 716 354T719 314Q714 236 664 179L660 176Q657 173 654 170T644 163T631 154T615 146T596 139T574 134T549 132Q510 132 465 156T386 211T307 265T223 290Q162 290 124 249T86 165Q86 155 82 144T73 132'],

    // REVERSED TILDE
    0x223D: [367,-133,778,56,722,'222 133Q147 133 102 197T56 335Q56 362 66 365Q71 369 77 364Q83 356 84 335T90 298Q102 254 137 222T223 189Q258 189 292 206T355 250T413 301T477 346T550 367Q628 367 673 309T722 171Q722 133 708 133Q703 133 699 141T694 162Q694 220 655 265T555 311Q519 311 485 293T421 248T363 196T298 152T222 133'],

    // stix-not, vert, similar
    0x2241: [467,-32,778,55,719,'220 366Q258 366 297 347T361 308T391 288Q394 288 464 370Q494 407 510 425T535 454T546 465T552 467H553Q560 467 566 461T573 448Q573 439 499 350Q424 266 424 261Q424 259 442 247T492 222T554 209Q607 209 646 243Q671 268 680 295T690 341T702 366Q719 366 719 314Q716 265 695 226Q682 199 664 179Q614 132 555 132Q517 132 477 151T412 190T383 210T347 172T278 89T233 37Q228 32 220 32Q210 32 206 38T201 48Q201 57 266 137Q272 144 275 148Q351 231 351 237Q351 239 333 251T283 276T221 289Q159 289 123 248T86 166Q86 156 82 145T73 132Q55 132 55 172Q55 220 79 272Q95 301 111 319Q161 366 220 366'],

    // MINUS TILDE
    0x2242: [463,-34,778,55,720,'55 439T55 443T56 449T62 456T68 463H706Q720 449 720 443T706 423H68Q55 439 55 443ZM56 72Q56 112 73 152T130 225T224 257Q259 257 294 240T360 199T419 149T484 107T553 90Q603 90 643 125T691 223Q693 257 704 257Q717 257 717 221Q717 147 671 91T554 34Q517 34 481 51T414 93T355 142T291 184T222 201Q172 201 131 167T84 67Q81 34 71 34Q56 37 56 72'],

    // APPROXIMATELY BUT NOT ACTUALLY EQUAL TO
    0x2246: [652,155,778,54,720,'55 417Q55 479 101 528T222 578Q259 578 294 564T393 507Q413 493 434 480T469 460T484 454L537 549Q587 639 595 647Q600 652 607 652Q615 652 621 647T628 634Q628 625 575 536Q524 446 524 443Q527 440 555 440Q603 440 644 469T691 547Q694 578 706 578T718 556Q718 555 718 551T717 545Q717 488 684 445T595 387Q582 384 558 384Q530 384 508 389L493 394L404 238L557 236H708Q720 224 720 217T706 196H379L291 43L499 41H708Q720 29 720 21T706 1H268L226 -71Q186 -143 179 -148Q173 -155 165 -155T152 -150T146 -137Q146 -133 184 -64L222 1H144L66 3L59 7Q54 14 54 20Q54 29 66 41H246L333 194Q333 196 202 196H68Q55 211 55 218T66 236H213L357 238L457 409L437 421Q432 423 393 450T307 500T222 523Q171 523 129 491T84 414Q82 383 70 383Q55 383 55 417'],

    // ALMOST EQUAL TO
    0x2248: [481,-49,778,55,719,'55 326Q55 394 101 437T226 481Q268 479 313 460T392 419T469 379T555 361Q622 361 662 401Q686 423 688 450Q693 479 702 479H705Q719 479 719 442Q719 367 670 327T554 286Q512 286 466 304T386 345T307 385T220 404Q184 404 157 394T120 374L111 363Q86 339 86 317Q86 288 71 288Q55 288 55 326ZM55 90Q55 164 105 205T226 246Q269 243 314 224T392 183T470 144T558 126Q622 126 662 166Q686 187 688 214Q693 244 704 244Q716 244 719 210Q719 165 702 132T658 82T605 58T552 50T498 58T447 77T384 110Q322 146 302 152Q263 168 220 168Q179 168 144 152Q128 147 107 125T86 81Q86 52 71 52Q55 52 55 90'],

    // ALMOST EQUAL OR EQUAL TO
    0x224A: [579,39,778,51,725,'220 523Q163 523 124 486T84 412Q81 383 69 383Q56 383 56 413Q56 441 67 470Q78 508 111 537T187 575Q203 579 219 579Q248 579 271 572Q304 565 393 508Q498 439 551 439Q620 439 662 486Q688 512 693 557Q693 565 697 572T707 579Q719 579 719 548Q719 483 673 434T550 384Q512 384 467 405T386 453T305 501T220 523ZM222 288Q164 288 124 251T84 177Q81 148 69 148Q56 148 56 178Q56 206 67 235Q78 274 111 302T187 339Q198 343 220 343Q244 343 259 341T308 322T393 272Q496 203 553 203Q612 203 651 241T691 312Q693 343 705 343Q719 343 719 313Q719 245 673 199Q626 148 552 148Q513 148 467 170T385 218T304 266T222 288ZM51 -19Q51 -6 62 -1H387Q713 -1 715 -3Q725 -10 725 -20Q725 -27 718 -34Q714 -38 672 -38T387 -39H62Q51 -25 51 -19'],

    // GEOMETRICALLY EQUIVALENT TO
    0x224E: [492,-8,778,56,723,'245 367Q251 415 288 453T392 492Q445 492 485 456T532 367H707Q722 359 722 347Q722 334 711 331T665 327H608H509Q500 332 498 336Q496 338 493 363T472 411Q443 451 389 451H387Q335 451 305 411Q290 392 287 374T282 344T268 327H72Q56 332 56 347Q56 360 70 367H245ZM56 153Q56 168 72 173H268Q277 168 279 164Q281 162 284 137T305 89Q334 49 389 49H391Q442 49 472 89Q487 108 490 126T495 156T509 173H608H666Q701 173 711 170T722 153T707 133H532Q526 81 486 45T389 8Q331 8 291 45T245 133H70Q56 140 56 153'],

    // DIFFERENCE BETWEEN
    0x224F: [492,-133,778,56,722,'245 367Q251 415 288 453T392 492Q445 492 485 456T532 367H707Q722 359 722 347Q722 334 711 331T665 327H608H509Q500 332 498 336Q496 338 493 363T472 411Q443 451 389 451H387Q335 451 305 411Q290 392 287 374T282 344T268 327H72Q56 332 56 347Q56 360 70 367H245ZM56 153Q56 168 72 173H708Q722 163 722 153Q722 140 707 133H70Q56 140 56 153'],

    // GEOMETRICALLY EQUAL TO
    0x2251: [609,108,778,56,722,'421 474T389 474T339 493T321 541Q321 566 337 587T391 609Q456 602 456 541Q456 512 439 493ZM56 347Q56 360 70 367H707Q722 359 722 347Q722 336 708 328L390 327H72Q56 332 56 347ZM56 153Q56 168 72 173H708Q722 163 722 153Q722 140 707 133H70Q56 140 56 153ZM421 -108T389 -108T339 -89T321 -41Q321 -16 337 5T391 27Q456 20 456 -41Q456 -70 439 -89'],

    // APPROXIMATELY EQUAL TO OR THE IMAGE OF
    0x2252: [601,101,778,15,762,'15 541Q15 569 33 585T75 601T117 585T135 541Q135 514 118 498T75 481T32 498T15 541ZM56 347Q56 360 70 367H707Q722 359 722 347Q722 336 708 328L390 327H72Q56 332 56 347ZM56 153Q56 168 72 173H708Q722 163 722 153Q722 140 707 133H70Q56 140 56 153ZM642 -41Q642 -17 658 0T702 18Q726 18 744 3T762 -41Q762 -67 745 -84T702 -101Q676 -101 659 -85T642 -41'],

    // IMAGE OF OR APPROXIMATELY EQUAL TO
    0x2253: [601,102,778,14,762,'642 541Q642 569 660 585T702 601T744 585T762 541Q762 515 745 498T702 481Q676 481 659 497T642 541ZM56 347Q56 360 70 367H707Q722 359 722 347Q722 336 708 328L390 327H72Q56 332 56 347ZM56 153Q56 168 72 173H708Q722 163 722 153Q722 140 707 133H70Q56 140 56 153ZM14 -41Q14 -15 31 1T74 18Q101 18 118 0T135 -41Q135 -64 118 -83T75 -102Q51 -102 33 -85T14 -41'],

    // RING IN EQUAL TO
    0x2256: [367,-133,778,56,722,'56 347Q56 360 70 367H707Q722 359 722 347Q722 334 711 331T658 327H586H465L472 318Q496 288 496 250T472 182L465 173H586H663Q700 173 711 170T722 153T707 133H70Q56 140 56 153Q56 168 72 173H312L305 182Q281 212 281 250T305 318L312 327H72Q56 332 56 347ZM473 250Q473 265 472 273T460 297T428 327H349Q328 313 318 298T306 273T304 250Q304 235 305 227T317 203T349 173H428Q449 187 459 202T471 227T473 250'],

    // RING EQUAL TO
    0x2257: [721,-133,778,56,722,'279 612Q279 656 310 688T388 721Q433 721 465 689T498 612Q498 573 470 538T389 503Q336 503 308 538T279 612ZM458 614Q458 637 452 651T433 672T411 679T383 680T352 675T333 664T324 647T321 629T320 611Q320 593 321 584T332 562T359 545Q366 543 389 543H391Q406 543 414 544T435 552T452 573T458 614ZM56 347Q56 360 70 367H707Q722 359 722 347Q722 336 708 328L390 327H72Q56 332 56 347ZM56 153Q56 168 72 173H708Q722 163 722 153Q722 140 707 133H70Q56 140 56 153'],

    // DELTA EQUAL TO
    0x225C: [859,-133,778,56,723,'192 482H190Q187 483 185 484T181 488T177 493T175 501Q175 506 178 512Q184 523 278 687T375 853Q379 857 383 857Q385 857 387 858T390 859Q397 859 403 853Q405 851 499 687T600 512Q603 506 603 501Q603 488 587 482H192ZM548 523L389 798Q388 798 309 661T230 523T389 522T548 523ZM56 347Q56 360 70 367H708Q723 359 723 347Q723 336 709 328L390 327H72Q56 332 56 347ZM56 153Q56 168 72 173H709Q723 163 723 153Q723 140 708 133H70Q56 140 56 153'],

    // LESS-THAN OVER EQUAL TO
    0x2266: [753,175,778,83,694,'674 753Q682 753 688 747T694 732T687 718Q686 717 417 589L151 463L399 345Q687 209 691 204Q694 198 694 193Q694 175 676 173H670L382 309Q92 446 90 448Q83 453 83 465Q84 476 96 482Q104 486 382 617T665 751Q669 753 674 753ZM84 39Q84 49 99 59H678Q694 53 694 39Q694 26 679 19H98Q84 26 84 39ZM83 -157Q83 -153 84 -150T86 -145T89 -141T92 -139T96 -137T99 -135H678Q694 -146 694 -155Q694 -168 679 -175H98Q84 -168 83 -157'],

    // GREATER-THAN OVER EQUAL TO
    0x2267: [753,175,778,82,694,'83 733Q83 741 89 747T99 753Q107 753 253 685T543 548T687 478Q694 473 694 463T687 448Q685 446 395 309L107 173H101Q83 175 83 193Q83 194 83 196Q82 203 98 212Q117 222 248 284Q326 321 378 345L626 463L360 589Q291 622 200 665Q112 706 98 714T83 733ZM84 39Q84 49 99 59H678Q694 53 694 39Q694 26 679 19H98Q84 26 84 39ZM83 -157Q83 -153 84 -150T86 -145T89 -141T92 -139T96 -137T99 -135H678Q694 -146 694 -155Q694 -168 679 -175H98Q84 -168 83 -157'],

    // stix-less, vert, not double equals
    0x2268: [752,286,778,82,694,'86 472Q93 477 381 614T673 752Q680 752 686 746T693 732T689 721Q686 715 418 590L151 461L418 332Q684 207 689 201Q693 195 693 190Q693 183 687 177T675 170Q668 170 380 307T86 450Q82 454 82 461Q82 467 86 472ZM82 33Q82 37 83 40T89 47T95 54H473L520 105Q569 156 571 156Q573 157 578 157Q586 157 592 151T598 136Q598 130 562 92L526 56L604 54H682Q693 43 693 35Q693 31 692 28T686 21T680 14H489L342 -139L513 -142H682Q693 -148 693 -160Q693 -167 680 -182H304L258 -230Q248 -240 237 -251T221 -268T211 -278T203 -284T197 -286Q189 -286 184 -280T178 -264Q178 -257 213 -219L249 -182H171L93 -179L86 -175Q82 -170 82 -163Q82 -155 95 -142H289L360 -64L433 14H262L93 16Q82 23 82 33'],

    // stix-gt, vert, not double equals
    0x2269: [752,286,778,82,693,'89 745Q95 752 100 752Q106 752 394 615T689 472Q693 468 693 461T689 450Q684 445 396 308T100 170Q95 170 89 176T82 190Q82 195 86 201Q91 208 358 332L624 461L358 590Q90 715 86 721Q82 725 82 731Q82 739 89 745ZM82 33Q82 37 83 40T89 47T95 54H473L520 105Q569 156 571 156Q573 157 578 157Q586 157 592 151T598 136Q598 130 562 92L526 56L604 54H682Q693 43 693 35Q693 31 692 28T686 21T680 14H489L342 -139L513 -142H682Q693 -148 693 -160Q693 -167 680 -182H304L258 -230Q248 -240 237 -251T221 -268T211 -278T203 -284T197 -286Q189 -286 184 -280T178 -264Q178 -257 213 -219L249 -182H171L93 -179L86 -175Q82 -170 82 -163Q82 -155 95 -142H289L360 -64L433 14H262L93 16Q82 23 82 33'],

    // BETWEEN
    0x226C: [751,251,500,74,425,'104 730Q104 749 123 749Q130 749 138 745Q186 717 237 671L250 659L261 670Q297 703 332 726T375 750T389 744T395 730Q395 721 390 717T364 699T322 668Q290 641 283 632Q280 628 281 627T293 612Q425 454 425 250Q425 144 388 51T293 -112Q282 -125 281 -126T283 -132Q306 -162 379 -209Q395 -219 395 -230Q395 -238 389 -244T375 -250T335 -228T262 -171L250 -159L238 -170Q202 -203 167 -226T124 -250T110 -244T104 -230Q104 -219 121 -209Q199 -156 216 -132Q219 -128 218 -127T206 -112Q74 46 74 250T206 612Q217 625 218 626T216 632Q199 656 121 709Q104 719 104 730ZM249 -94Q364 61 364 250Q364 430 265 574Q253 590 249 594L242 583Q134 439 134 250Q134 114 192 -1Q212 -44 242 -83L249 -94'],

    // stix-not, vert, less-than
    0x226E: [709,209,778,82,693,'693 -14T693 -20T687 -33T675 -41Q667 -41 506 37L344 112Q342 112 262 -46Q184 -196 176 -205Q172 -209 168 -209T162 -208Q155 -208 151 -203T146 -190Q146 -178 171 -137Q193 -91 251 21L306 132L198 183Q142 208 118 220T88 238T82 249Q82 253 86 261Q92 267 278 357L464 443L529 572Q561 637 577 667T597 703T607 708Q615 708 622 702T629 688Q629 680 575 579L524 474Q524 473 545 482T598 508Q666 541 673 541T686 535T693 521Q693 512 679 504T589 459L493 414L360 150Q366 148 378 142T431 116T529 70Q686 -8 689 -10Q693 -14 693 -20ZM380 277L433 383Q432 385 292 319T151 250T237 209T324 170L380 277'],

    // stix-not, vert, greater-than
    0x226F: [708,209,778,82,693,'82 514T82 520T89 533T100 541Q106 541 271 463Q434 386 435 386L515 543Q593 699 600 706Q604 708 607 708Q615 708 622 702T629 688T549 526Q509 445 491 407T473 368Q522 343 580 317Q636 291 660 278T688 261T693 250V249Q693 241 681 234T580 184Q533 161 502 146Q317 59 315 59Q312 56 246 -74Q197 -170 186 -189T168 -209Q164 -209 162 -208Q155 -208 151 -203T146 -190Q146 -187 200 -79L253 28L218 11Q182 -6 144 -23T100 -41Q95 -41 89 -35T82 -21Q82 -12 96 -4T186 41L284 88L349 217Q377 273 395 311T413 350Q413 351 253 428Q101 498 86 510Q82 514 82 520ZM624 250Q461 330 455 330Q454 331 453 329T448 321T441 308T430 287T416 259T398 223L342 114L624 250'],

    // stix-not, vert, less-than-or-equal
    0x2270: [801,303,778,81,694,'82 -124Q82 -120 83 -117T89 -110T95 -103H220L284 50Q346 204 344 206L218 268Q153 297 123 313T87 333T82 344T86 355Q104 369 291 455Q491 552 491 553L542 673Q581 767 590 784T609 801Q616 801 622 795T629 781Q629 773 586 677Q546 581 546 577L609 606Q669 635 673 635Q680 635 686 629T693 615Q693 610 692 608T670 593T604 561L524 521L400 226L542 157Q617 123 649 107T687 85T694 72Q694 66 690 60T679 54Q664 54 526 121Q513 127 495 136T464 150T438 162T416 173T399 180T388 185L384 186Q383 186 322 41L262 -103H680Q682 -105 684 -108T688 -113T691 -118T693 -124Q693 -134 682 -141L464 -143H246L213 -219Q182 -292 178 -299Q172 -303 166 -303T153 -297T146 -283Q146 -282 174 -213T202 -143H146L93 -141Q82 -134 82 -124ZM418 370L466 495Q464 495 308 420T151 344T204 317T311 267T364 244Q364 247 418 370'],

    // stix-not, vert, greater-than-or-equal
    0x2271: [801,303,778,82,694,'97 54Q82 54 82 72Q82 79 86 84Q95 91 222 153L351 215L398 324L442 433L258 519Q95 597 87 604Q82 608 82 615T88 628T102 635Q107 635 424 484L458 468L524 630Q593 789 597 795Q601 801 609 801Q616 801 622 795T629 781L562 615L493 450L589 406Q665 371 679 362T694 344Q694 339 693 337T677 326T631 302T538 257Q504 241 465 223T406 195T386 186Q384 185 322 39L262 -103H680Q682 -105 684 -108T688 -113T691 -118T693 -124Q693 -134 682 -141L464 -143H246L213 -219Q182 -292 178 -299Q172 -303 166 -303T153 -297T146 -283Q146 -282 174 -213T202 -143H146L93 -141Q82 -134 82 -124Q82 -120 83 -117T89 -110T95 -103H220L273 26Q326 156 326 157L218 106Q109 54 97 54ZM553 379Q480 412 480 415Q479 415 460 372T423 285T406 241Q408 240 516 291T624 344L553 379'],

    // stix-less-than or (contour) similar
    0x2272: [732,228,778,56,722,'674 732Q682 732 688 726T694 711T687 697Q686 696 417 568L151 442L399 324Q687 188 691 183Q694 177 694 172Q694 154 676 152H670L382 288Q92 425 90 427Q83 432 83 444Q84 455 96 461Q104 465 382 596T665 730Q669 732 674 732ZM56 -194Q56 -107 106 -51T222 6Q260 6 296 -12T362 -56T420 -108T483 -153T554 -171Q616 -171 654 -128T694 -29Q696 6 708 6Q722 6 722 -26Q722 -102 676 -164T557 -227Q518 -227 481 -209T415 -165T358 -113T294 -69T223 -51Q163 -51 125 -93T83 -196Q81 -228 69 -228Q56 -228 56 -202V-194'],

    // stix-greater-than or (contour) similar
    0x2273: [732,228,778,56,722,'90 697Q83 704 83 712T88 726T99 732Q107 732 253 664T543 527T687 457Q694 452 694 442T687 427Q685 425 395 288L107 152H101Q83 154 83 172Q83 173 83 175Q82 182 98 191Q117 201 248 263Q326 300 378 324L626 442L360 568Q91 696 90 697ZM56 -194Q56 -107 106 -51T222 6Q260 6 296 -12T362 -56T420 -108T483 -153T554 -171Q616 -171 654 -128T694 -29Q696 6 708 6Q722 6 722 -26Q722 -102 676 -164T557 -227Q518 -227 481 -209T415 -165T358 -113T294 -69T223 -51Q163 -51 125 -93T83 -196Q81 -228 69 -228Q56 -228 56 -202V-194'],

    // LESS-THAN OR GREATER-THAN
    0x2276: [681,253,778,44,734,'734 181Q734 173 728 167T714 161Q711 161 386 280T54 404Q44 408 44 421Q44 432 52 437Q66 443 388 562T714 681Q721 681 727 675T734 661Q734 651 722 645Q711 639 462 546Q441 539 420 531L122 421L420 311L723 198Q734 192 734 181ZM44 247Q44 255 50 261T63 267Q66 267 391 148T723 24Q734 18 734 7T723 -10Q716 -14 391 -133T63 -253Q56 -253 50 -247T44 -233Q44 -223 55 -217Q67 -210 317 -118Q337 -110 357 -103L655 7L357 117L54 230Q44 236 44 247'],

    // GREATER-THAN OR LESS-THAN
    0x2277: [681,253,778,83,694,'83 661Q83 668 88 674T104 681Q111 679 396 560Q686 437 687 436Q694 431 694 421T687 406Q686 405 543 344T253 222T101 161Q83 163 83 180Q83 194 95 199Q96 199 130 213T232 257T361 311L621 421L357 532Q307 553 233 584Q121 631 102 640T83 661ZM673 267Q694 267 694 248Q694 237 687 232Q684 229 420 118L156 7L416 -103L683 -215Q694 -222 694 -233Q694 -251 676 -253Q670 -253 524 -192T235 -70T90 -8Q83 -1 83 7Q83 19 94 24Q97 25 378 144T667 266Q669 267 673 267'],

    // PRECEDES OR EQUAL TO
    0x227C: [580,153,778,83,694,'112 270Q83 270 83 290Q83 301 94 307Q98 310 118 310Q516 310 620 464Q635 486 642 510T651 548T657 571T675 580Q693 577 693 559V552Q684 472 628 410T465 314Q436 303 372 290Q373 290 388 287T425 278T465 266Q674 199 693 28L694 17Q688 5 683 3Q677 0 673 0Q656 0 653 24Q623 270 118 270H112ZM110 116Q83 116 83 136T110 156H113Q134 156 160 155T231 146T318 128T407 95T489 44T550 -30T583 -131Q583 -153 563 -153Q556 -153 553 -152T547 -145T542 -127Q531 -54 478 0Q425 53 333 83T123 116H110'],

    // SUCCEEDS OR EQUAL TO
    0x227D: [580,154,778,83,694,'668 310Q694 310 694 290Q694 285 691 279Q684 271 664 270Q550 268 464 257T301 220T179 146T124 27Q119 0 103 0T83 16Q83 21 83 31T92 68T113 121T157 177T229 231Q295 268 405 290Q404 290 389 293T352 302T312 314Q138 371 96 500Q83 541 83 562Q83 568 89 574T103 580Q115 580 120 570T126 542T138 497T173 442Q289 310 659 310H668ZM194 -131Q201 -60 241 -6T343 82T477 133T628 155Q632 155 644 155T661 156Q685 155 690 147Q694 143 694 136Q694 132 693 129T689 124T685 120T681 117L656 116Q596 114 543 106T436 79T342 35T272 -33T235 -127Q231 -154 212 -154Q203 -153 199 -147T194 -136V-131'],

    // PRECEDES OR EQUIVALENT TO
    0x227E: [732,228,778,56,722,'84 442Q84 455 91 459T117 463Q120 463 126 463T137 462Q388 466 512 526T653 705Q657 732 676 732Q685 731 689 725T694 714V708Q689 662 672 624T626 559T569 513T500 479T435 458T373 442Q379 441 404 435T440 426T477 414T533 392Q592 362 630 319T681 241T694 174Q694 153 674 153Q662 153 657 163T652 188T640 231T606 287Q500 416 137 422H114Q104 422 98 423T88 428T84 442ZM56 -194Q56 -107 106 -51T222 6Q260 6 296 -12T362 -56T420 -108T483 -153T554 -171Q616 -171 654 -128T694 -29Q696 6 708 6Q722 6 722 -26Q722 -102 676 -164T557 -227Q518 -227 481 -209T415 -165T358 -113T294 -69T223 -51Q163 -51 125 -93T83 -196Q81 -228 69 -228Q56 -228 56 -202V-194'],

    // SUCCEEDS OR EQUIVALENT TO
    0x227F: [732,228,778,56,722,'84 710Q84 732 102 732Q115 732 119 722T125 696T137 652T171 597Q277 468 640 462H661Q694 462 694 442T661 422H640Q578 421 526 417T415 403T309 376T222 333T156 268T124 179Q122 162 118 158T103 153Q100 153 98 153T95 154T93 155T90 158T85 163Q83 167 83 176Q88 222 105 260T151 325T208 371T277 405T342 426T404 442Q401 443 380 447T345 456T302 469T245 492Q125 551 92 661Q84 695 84 710ZM56 -194Q56 -107 106 -51T222 6Q260 6 296 -12T362 -56T420 -108T483 -153T554 -171Q616 -171 654 -128T694 -29Q696 6 708 6Q722 6 722 -26Q722 -102 676 -164T557 -227Q518 -227 481 -209T415 -165T358 -113T294 -69T223 -51Q163 -51 125 -93T83 -196Q81 -228 69 -228Q56 -228 56 -202V-194'],

    // DOES NOT PRECEDE
    0x2280: [705,208,778,82,693,'386 292Q388 292 439 393T543 598T598 703Q599 703 603 704T609 705Q616 705 622 699T629 685T533 494Q440 308 440 305Q451 310 462 312Q547 342 592 388T651 505Q654 525 658 532T673 539Q680 539 686 533T693 519Q693 495 678 450Q638 341 500 283Q433 259 418 259Q416 259 411 251T406 241T415 239Q482 224 544 190Q674 121 691 -10Q693 -28 691 -32Q684 -43 672 -43Q664 -43 658 -37Q656 -33 650 -6T634 47T589 109T500 168Q473 179 436 190T388 201H386L284 -1Q261 -45 232 -101T191 -181T178 -206Q176 -206 172 -207T166 -208Q160 -208 153 -202T146 -188Q146 -185 246 12Q344 206 344 210Q344 213 305 217T213 225T124 228H95Q82 241 82 248Q82 253 95 268H124Q172 268 236 273T343 283T386 292'],

    // stix-not (vert) succeeds
    0x2281: [705,208,778,82,693,'103 -43Q96 -43 89 -39T82 -26L84 -10Q105 141 275 212Q342 236 355 236Q360 236 364 245L369 256H360Q284 280 275 283Q115 351 86 490Q82 507 82 517Q82 526 88 532T103 538Q110 538 115 534Q119 531 122 517T128 486T143 444T174 397T231 351T320 310Q371 292 389 292L491 496Q595 701 598 703Q599 703 603 704T609 705Q616 705 622 699T629 685Q629 684 531 485Q431 296 431 288Q431 278 520 273T651 268H680Q693 253 693 248Q693 241 680 228H651Q591 228 491 218T386 201L284 -1Q261 -45 232 -101T191 -181T178 -206Q176 -206 172 -207T166 -208Q160 -208 153 -202T146 -188Q146 -182 302 125L335 190L324 185Q313 185 289 172Q241 153 208 128T159 78T135 31T124 -11T118 -37Q112 -43 103 -43'],

    // stix-/nsubseteq N: not (vert) subset, equals
    0x2288: [801,303,778,83,693,'146 -283Q146 -282 174 -213T202 -143H115Q102 -127 102 -123T115 -103H220L291 68L278 73Q203 101 153 157T86 288Q83 309 83 344Q83 380 86 399Q107 480 160 539Q222 601 298 621Q328 630 345 631T435 635L526 637L560 715Q587 778 593 789T609 801Q616 801 622 795T629 781Q629 780 625 771T614 742T600 706L571 637Q571 635 626 635H680Q693 620 693 613T689 601L682 597L618 595H553L449 346Q425 288 399 223T359 127T346 95H356Q365 95 381 95T417 94T463 93T515 93H682Q693 82 693 74T680 53H511Q420 55 335 55L329 57L262 -103H680Q682 -105 684 -108T688 -113T691 -118T693 -124Q693 -134 682 -141L464 -143H246L213 -219Q182 -292 178 -299Q172 -303 166 -303T153 -297T146 -283ZM509 590Q509 595 438 595Q354 595 318 586Q246 567 195 516T126 395Q123 378 123 344T126 293Q141 229 184 181T291 110L306 104L406 346L509 590'],

    // stix-/nsupseteq N: not (vert) superset, equals
    0x2289: [801,303,778,82,691,'82 606T82 613T95 635H251H348Q408 635 435 632T502 615L515 608L520 617Q520 619 558 708Q584 774 591 787T609 801Q616 801 622 795T629 781Q629 775 562 615L551 590L569 577Q646 527 678 437Q691 398 691 344T678 250Q653 182 597 132T469 64Q427 53 366 53H326L295 -25L262 -103H660Q673 -118 673 -124Q673 -129 669 -136L662 -141L453 -143H246L213 -219Q182 -292 178 -299Q172 -303 166 -303T153 -297T146 -283Q146 -282 174 -213T202 -143H95Q82 -128 82 -123T95 -103H220L251 -25L284 53H189L93 55L86 59Q82 64 82 71T95 93H302L400 333Q498 569 498 573L444 590Q431 593 260 595L93 597L86 601Q82 606 82 613ZM652 344V354Q652 451 575 521Q571 526 557 538T537 551Q534 551 533 548Q533 543 438 319L344 95L371 93H386Q487 93 557 150T649 293Q652 309 652 344'],

    // stix-subset, not equals, variant
    0x228A: [635,241,778,84,693,'693 72Q693 68 692 66T686 59T680 52H524Q398 52 367 53T309 63Q236 82 180 132T98 250Q84 288 84 343Q84 397 98 437Q126 515 193 568T346 632Q347 632 373 633T440 634T520 635H680Q693 620 693 615Q693 608 680 595H526Q364 595 353 592Q279 582 221 539T138 430Q124 392 124 343Q124 296 138 257Q163 192 221 149T353 95Q364 92 526 92H680Q693 79 693 72ZM102 -132T102 -125T115 -103H382L420 -68Q429 -60 438 -52T452 -39T463 -28T472 -20T478 -14T483 -10T487 -7T490 -6T493 -5T496 -5Q502 -5 508 -12T515 -28Q515 -34 513 -37Q512 -38 507 -42T492 -55T475 -70L440 -101L562 -103H682Q693 -114 693 -122T680 -143H395L355 -179Q289 -241 280 -241Q273 -241 267 -235T260 -221T265 -208T300 -174L335 -143H224L113 -141L106 -137Q102 -132 102 -125'],

    // stix-superset, not equals, variant
    0x228B: [635,241,778,82,691,'82 615Q82 620 95 635H251Q378 635 409 634T469 623Q540 605 596 555T678 437Q691 397 691 343T678 250Q649 172 581 119T426 55Q415 52 251 52H95Q93 55 89 59T84 65T82 72Q82 79 95 92H249Q411 92 422 95Q496 105 554 148T638 257Q651 296 651 343Q651 391 638 430Q613 495 555 538T422 592Q411 595 249 595H95Q82 608 82 615ZM82 -132T82 -125T95 -103H380L420 -57Q452 -21 460 -14T474 -6Q482 -6 488 -12T495 -25T451 -81L433 -101L549 -103H662Q673 -114 673 -122T660 -143H395L355 -190Q311 -239 309 -239Q305 -241 302 -241Q294 -241 287 -235T280 -221T324 -163L342 -143H218L93 -141L86 -137Q82 -132 82 -125'],

    // SQUARE IMAGE OF
    0x228F: [539,41,778,83,694,'83 523Q87 535 99 539H679Q694 531 694 519Q694 506 679 499H123V-1H678Q694 -7 694 -21Q694 -34 679 -41H98Q93 -38 84 -28L83 247V523'],

    // SQUARE ORIGINAL OF
    0x2290: [539,41,778,64,714,'64 506T64 519T78 539H699Q706 536 714 526V-28Q706 -38 699 -41H78Q64 -34 64 -21Q64 -6 80 -1H674V499H78Q64 506 64 519'],

    // CIRCLED RING OPERATOR
    0x229A: [583,82,778,57,721,'57 250Q57 327 87 392T166 497T270 560T382 582H394Q512 582 610 500Q721 401 721 250Q721 112 626 15T389 -82Q251 -82 154 13T57 250ZM682 129T682 250T596 457T390 543Q269 543 183 457T96 250Q96 132 180 45T389 -43Q511 -43 596 43ZM250 250Q250 316 295 352T384 388Q451 388 489 347T528 250Q528 192 487 152T389 112Q331 112 291 152T250 250ZM488 250Q488 290 460 319T389 349Q348 349 319 320T290 250Q290 208 320 180T389 151Q431 151 459 181T488 250'],

    // CIRCLED ASTERISK OPERATOR
    0x229B: [583,82,778,57,721,'57 250Q57 327 87 392T166 497T270 560T382 582H394Q512 582 610 500Q721 401 721 250Q721 112 626 15T389 -82Q251 -82 154 13T57 250ZM682 129T682 250T596 457T390 543Q269 543 183 457T96 250Q96 132 180 45T389 -43Q511 -43 596 43ZM204 339Q204 357 215 366T238 375Q247 375 283 348Q300 336 311 328L368 286Q369 286 366 323T359 398T355 437Q357 456 379 465Q380 465 384 465T391 466Q403 465 412 457T423 437Q423 436 420 398T413 323T410 286L467 328Q476 334 486 341T501 353T513 361T523 368T529 372T535 374T541 375Q554 375 564 365T575 339Q575 325 566 318T519 292Q504 285 496 281L430 250L496 219Q552 192 559 188T572 175Q575 168 575 161Q575 148 566 137T541 126H538Q530 126 499 149Q480 163 467 172L410 214Q409 214 412 177T419 102T423 63Q423 59 421 54T411 43T389 36T368 42T357 54T355 63Q355 64 358 102T365 177T368 214L311 172Q302 165 293 159T279 148T268 140T260 134T254 131T250 128T246 127T242 126T238 126Q223 126 214 135T204 161T213 183T282 219L348 250L282 281Q226 308 219 312T206 325Q204 330 204 339'],

    // CIRCLED DASH
    0x229D: [583,82,778,57,721,'57 250Q57 327 87 392T166 497T270 560T382 582H394Q512 582 610 500Q721 401 721 250Q721 112 626 15T389 -82Q251 -82 154 13T57 250ZM682 129T682 250T596 457T390 543Q269 543 183 457T96 250Q96 132 180 45T389 -43Q511 -43 596 43ZM223 250Q223 263 233 267T280 271Q289 271 325 271T389 270H490Q535 270 545 267T555 250Q555 241 549 235Q544 231 527 231T389 230Q239 230 235 232Q223 236 223 250'],

    // SQUARED PLUS
    0x229E: [689,0,778,55,722,'71 0Q59 4 55 16V346L56 676Q64 686 70 689H709Q719 681 722 674V15Q719 10 709 1L390 0H71ZM369 365V649H95V365H369ZM682 365V649H409V365H682ZM369 40V325H95V40H369ZM682 40V325H409V40H682'],

    // SQUARED MINUS
    0x229F: [689,0,778,55,722,'71 0Q59 4 55 16V346L56 676Q64 686 70 689H709Q719 681 722 674V15Q719 10 709 1L390 0H71ZM682 365V649H95V365H682ZM682 40V325H95V40H682'],

    // SQUARED TIMES
    0x22A0: [689,0,778,55,722,'71 0Q59 4 55 16V346L56 676Q64 686 70 689H707Q714 686 722 676V13Q714 3 707 0H71ZM123 649Q147 625 214 555T335 430T389 374L654 649H123ZM95 70Q99 74 229 209T360 345L95 619V70ZM682 70V619L418 346Q417 344 549 207L682 70ZM654 41L400 304L388 315L123 41L256 40H522L654 41'],

    // SQUARED DOT OPERATOR
    0x22A1: [689,0,778,55,722,'71 0Q59 4 55 16V346L56 676Q64 686 70 689H709Q719 681 722 674V15Q719 10 709 1L390 0H71ZM682 40V649H95V40H682ZM330 345Q330 371 347 388T390 405Q412 405 430 389T448 345Q448 317 430 301T389 285T348 301T330 345'],

    // TRUE
    0x22A8: [695,0,611,55,556,'55 678Q55 679 56 681T58 684T61 688T65 691T70 693T77 694Q88 692 95 679V464H540Q554 456 555 446Q555 442 554 439T552 434T549 430T546 428T542 426T539 424H95V270H539Q540 270 542 269T545 267T549 264T552 260T554 255T555 248Q554 238 540 230H95V15Q88 2 77 0Q73 0 70 1T65 3T61 6T59 9T57 13T55 16V678'],

    // FORCES
    0x22A9: [695,0,722,55,666,'55 678Q55 679 56 681T58 684T61 688T65 691T70 693T77 694Q88 692 95 679V15Q88 2 77 0Q73 0 70 1T65 3T61 6T59 9T57 13T55 16V678ZM249 678Q249 679 250 681T252 684T255 688T259 691T264 693T271 694Q282 692 289 679V367H651Q666 359 666 347Q666 334 651 327H289V15Q282 2 271 0Q267 0 264 1T259 3T255 6T253 9T251 13T249 16V678'],

    // TRIPLE VERTICAL BAR RIGHT TURNSTILE
    0x22AA: [695,0,889,55,833,'55 678Q55 679 56 681T58 684T61 688T65 691T70 693T77 694Q88 692 95 679V15Q88 2 77 0Q73 0 70 1T65 3T61 6T59 9T57 13T55 16V678ZM237 678Q237 679 238 681T240 684T243 688T247 691T252 693T259 694Q270 692 277 679V15Q270 2 259 0Q255 0 252 1T247 3T243 6T241 9T239 13T237 16V678ZM419 678Q419 679 420 681T422 684T425 688T429 691T434 693T441 694Q452 692 459 679V367H818Q833 359 833 347Q833 334 818 327H459V15Q452 2 441 0Q437 0 434 1T429 3T425 6T423 9T421 13T419 16V678'],

    // DOES NOT PROVE
    0x22AC: [696,1,611,-55,554,'56 681Q70 695 76 695T96 681V368H243L381 530Q521 692 525 692Q537 700 547 688Q554 682 554 674Q554 671 553 669T548 661T539 649T522 631T499 604T465 565T421 512Q296 373 296 368H416H476Q525 368 539 365T554 348Q554 334 543 328H261L96 141V12Q81 -1 75 -1Q65 -1 58 10L56 50V92L18 48Q7 37 -1 28T-13 14T-19 6T-23 1T-27 0T-33 -1Q-42 -1 -48 4T-55 19Q-55 24 -47 34T12 103L56 155V681ZM205 326Q205 328 152 328H96V263Q96 203 98 203Q99 203 123 231T174 290T205 326'],

    // NOT TRUE
    0x22AD: [695,1,611,-55,554,'56 681Q70 695 76 695T96 681V466H327L425 579Q522 692 527 692Q529 693 534 693Q542 693 547 688T553 674Q553 668 549 663Q549 662 538 650T504 611T463 563L381 468L461 466H543Q554 453 554 446T541 426H345L209 272L376 270H543Q554 257 554 251T541 230H174L96 141V12Q81 -1 75 -1Q65 -1 58 10L56 50V92L18 48Q7 37 -1 28T-13 14T-19 6T-23 1T-27 0T-33 -1Q-42 -1 -48 4T-55 19Q-55 24 -47 34T12 103L56 155V681ZM267 399L292 426H96V270H158L201 321Q256 382 267 399ZM118 228L119 229Q119 230 109 230H96V201L107 212Q118 227 118 228'],

    // DOES NOT FORCE
    0x22AE: [695,1,722,-55,665,'56 681Q70 695 77 695T96 683V428L98 175L252 323V681Q264 695 272 695Q278 695 292 681V526Q292 368 296 368Q298 368 447 510Q638 695 642 695H645Q651 695 658 688T665 673Q665 666 661 661Q659 660 639 641T578 582T505 512L356 370L505 368H654Q665 357 665 349Q665 343 652 328H314L303 317L292 308V12Q289 10 285 6T279 1T272 -1Q265 -1 252 12V139Q252 266 249 266L96 119V12Q80 -1 76 -1T70 0T63 6T56 12V79L29 55Q-26 -1 -35 -1Q-42 -1 -48 5T-55 19Q-55 25 -51 30T-15 66Q5 86 18 99L56 135V681'],

    // NEGATED DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
    0x22AF: [695,1,722,-55,665,'56 681Q70 695 77 695T96 683V428L98 175L252 323V681Q264 695 272 695Q278 695 292 681V466H401L503 563L621 679Q637 695 645 695Q652 695 658 688T665 673Q665 670 663 666Q663 665 651 652T611 612T561 563L458 468L556 466H654Q665 455 665 447T652 426H416L294 308L292 288V270H652Q665 255 665 250T652 230H292V12Q289 10 285 6T279 1T272 -1Q265 -1 252 12V139Q252 266 249 266L96 119V12Q80 -1 76 -1T70 0T63 6T56 12V79L29 55Q-26 -1 -35 -1Q-42 -1 -48 5T-55 19Q-55 25 -51 30T-15 66Q5 86 18 99L56 135V681ZM358 426H292V361L325 392L358 426'],

    // NORMAL SUBGROUP OF
    0x22B2: [539,41,778,83,694,'694 -26Q686 -40 676 -41H670L382 95Q92 232 90 234Q83 239 83 249Q83 262 96 267Q101 270 379 401T665 537Q671 539 674 539Q686 539 694 524V-26ZM654 11T654 249T653 487T402 369T151 249L275 190Q399 131 524 72T652 11Q654 11 654 249'],

    // CONTAINS AS NORMAL SUBGROUP
    0x22B3: [540,41,778,83,694,'83 523Q83 524 85 527T92 535T103 539Q107 539 389 406T680 268Q694 260 694 249Q694 239 687 234Q685 232 395 95L107 -41H101Q90 -40 83 -26V523ZM376 368Q323 393 254 425T155 472L125 487Q123 487 123 249T125 11Q127 12 252 71T502 190L626 249L376 368'],

    // NORMAL SUBGROUP OF OR EQUAL TO
    0x22B4: [636,138,778,83,695,'694 71Q686 58 676 56H670L382 192Q92 329 90 331Q83 336 83 346Q83 359 96 364Q101 367 379 498T665 634Q671 636 674 636Q686 636 694 621V71ZM654 108T654 346T653 584T402 466T151 346L275 287Q399 228 524 169T652 108Q654 108 654 346ZM83 -120Q83 -116 84 -113T86 -108T89 -104T92 -102T96 -100T99 -98H678Q679 -98 681 -99T684 -101T688 -104T691 -108T693 -113T694 -120Q692 -130 679 -138H98Q84 -130 83 -120'],

    // CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
    0x22B5: [637,138,778,83,695,'83 620Q83 621 85 624T92 632T103 636Q107 636 389 503T680 365Q694 357 694 346Q694 336 687 331Q685 329 395 192L107 56H101Q90 58 83 71V620ZM376 465Q323 490 254 522T155 570L125 584Q123 584 123 346T125 108Q127 109 252 168T502 287L626 346L376 465ZM83 -120Q83 -116 84 -113T86 -108T89 -104T92 -102T96 -100T99 -98H678Q679 -98 681 -99T684 -101T688 -104T691 -108T693 -113T694 -120Q692 -130 679 -138H98Q84 -130 83 -120'],

    // MULTIMAP
    0x22B8: [408,-92,1111,55,1055,'1055 250Q1055 190 1012 141T896 92Q858 92 828 106T781 140T755 180T741 214L738 228V230H405Q71 230 68 232Q55 238 55 250T68 268Q71 270 405 270H738V272L740 280Q742 287 745 297T754 321T771 348T796 374T832 396T881 408H891Q969 408 1012 360T1055 250ZM896 132Q948 132 981 166T1014 250Q1014 301 985 330T920 367Q914 368 891 368Q853 368 816 338T778 250Q778 198 812 165T896 132'],

    // INTERCALATE
    0x22BA: [431,212,556,57,501,'318 -182Q302 -212 280 -212H278H275Q249 -212 239 -182L238 84V351H162L87 352Q57 362 57 391T84 429Q89 431 280 431H470L474 429Q477 427 479 426T484 423T490 417T495 410T499 402T500 391Q500 365 470 352L394 351H318V-182'],

    // XOR
    0x22BB: [716,0,611,55,555,'56 697Q56 706 62 711T75 716Q86 716 90 709Q91 708 104 680T147 592T199 483L305 261L411 483Q443 548 481 629Q512 694 518 705T535 716Q543 716 549 710T555 700Q555 693 501 577T388 340T325 210Q316 194 305 194Q292 194 285 210Q282 219 224 339T111 574T56 697ZM55 14T55 20T59 31T66 38T71 40H540Q555 32 555 20T540 0H71Q70 0 67 2T59 9'],

    // NAND
    0x22BC: [716,0,611,54,555,'55 698Q56 708 70 716H540Q554 708 555 698Q555 694 554 691T552 686T549 682T546 680T542 678T539 676H71Q70 676 68 677T65 679T61 682T58 686T56 691T55 698ZM555 18Q554 12 549 6T536 0H535Q525 0 515 17T459 132Q430 194 410 235L305 455L199 233Q176 185 147 125T105 36T90 7Q85 0 75 0Q63 0 58 11Q55 15 55 21Q58 31 170 266T285 507Q295 522 305 522T320 515Q322 513 439 268L555 24V18'],

    // DOT OPERATOR
    0x22C5: [189,0,278,55,222,'71 0Q59 4 55 16V96L56 176Q59 180 66 187L70 189H209Q219 181 222 174V15Q219 10 209 1L140 0H71'],

    // DIVISION TIMES
    0x22C7: [545,44,778,55,720,'366 543Q374 545 382 545Q405 545 419 538Q429 534 443 521T462 496Q466 478 466 467Q466 438 444 412Q422 390 388 390Q352 390 331 412Q311 434 311 467Q311 499 331 518Q345 533 366 543ZM146 472Q146 479 153 485T166 492Q171 492 187 476T279 385L386 278L495 385Q600 492 608 492Q615 492 621 486T628 472Q628 467 614 452T531 367L435 270H706Q720 256 720 250Q720 241 706 230H435L531 132Q600 63 614 48T628 27Q628 20 622 14T608 7Q600 7 495 114L386 221L279 114Q204 39 188 23T166 7Q159 7 153 13T146 27Q146 32 160 47T244 132L339 230H68Q55 243 55 250Q55 255 68 270H339L244 367Q175 436 161 451T146 472ZM466 34Q466 4 447 -20T388 -44Q353 -44 331 -22Q311 1 311 34Q311 66 331 85Q347 101 366 110Q374 112 382 112Q405 112 419 105Q429 100 443 87T462 63Q466 45 466 34'],

    // LEFT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22C9: [492,-8,778,146,628,'146 479Q159 492 166 492Q171 492 189 475T279 386L386 279L495 386Q598 492 608 492Q615 492 621 486T628 472Q628 464 522 357L415 250L522 144Q628 37 628 28Q628 21 622 15T608 8Q599 8 495 115L386 221L279 115Q204 40 188 24T166 8Q162 8 160 9T153 15T146 21V479ZM186 77L359 250L186 424V77'],

    // RIGHT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22CA: [492,-8,778,146,628,'146 472Q146 479 152 485T166 492Q171 492 189 475T279 386L386 279L495 386Q598 492 608 492Q615 492 628 479V21Q615 8 608 8Q599 8 495 115L386 221L279 115Q204 40 188 24T166 8Q159 8 153 14T146 28Q146 37 253 144L359 250L253 357Q146 464 146 472ZM588 77V424L499 337L415 250L588 77'],

    // LEFT SEMIDIRECT PRODUCT
    0x22CB: [694,23,778,55,722,'55 674Q55 682 62 688T76 694H77Q83 694 100 677T208 561Q320 440 410 342Q462 286 541 201Q677 55 699 30T722 -2Q722 -9 716 -15T701 -22T688 -17Q687 -15 542 141T394 301L388 306L240 146Q119 15 101 -3T75 -22T61 -16T55 -2Q55 4 67 19T158 117Q190 151 209 172L361 336L209 500Q62 657 57 667Q55 671 55 674'],

    // RIGHT SEMIDIRECT PRODUCT
    0x22CC: [694,22,778,55,722,'84 -22T76 -22T62 -16T55 -2Q55 4 78 30T249 215Q321 293 367 342Q672 672 683 682Q695 694 702 694Q710 694 716 688T722 674Q722 668 710 653T619 555Q587 521 568 500L416 336L568 172Q715 15 720 5Q722 1 722 -2Q722 -9 716 -15T702 -22H700Q693 -22 671 1T537 146L389 306Q387 304 340 253T237 143T135 33L89 -17Q84 -22 76 -22'],

    // REVERSED TILDE EQUALS
    0x22CD: [464,-36,778,56,722,'56 433Q56 464 71 464Q74 464 77 461Q82 454 82 438T91 397T123 347Q166 307 222 307Q264 307 308 331T386 385T465 438T556 463Q631 463 676 408T722 283Q722 250 708 250Q704 250 699 257Q695 265 693 286T682 330Q670 350 655 367Q612 407 556 407Q514 407 470 383T393 329T314 276T222 251Q148 251 102 306T56 433ZM57 56Q57 71 73 76H706Q722 70 722 56Q722 44 707 36H71Q57 43 57 56'],

    // CURLY LOGICAL OR
    0x22CE: [578,21,760,83,676,'83 558Q83 566 89 572T104 578Q108 578 116 577T146 570T190 555T239 526T286 480Q308 453 325 420T351 358T367 304T376 265T380 251T381 253Q381 262 395 312Q428 434 492 499T642 576Q654 578 655 578Q664 578 670 572T676 558Q676 543 657 540T599 524T525 476Q406 362 400 29V8Q400 -21 380 -21Q369 -21 362 -11Q360 -7 360 12Q360 115 348 200T308 360T231 480T111 537Q83 540 83 558'],

    // CURLY LOGICAL AND
    0x22CF: [578,22,760,83,676,'104 -22Q95 -22 89 -16T83 -2Q83 11 98 16T135 23T192 46T256 103Q360 233 360 549Q360 554 360 557T361 563T362 567T364 569T367 572T371 576Q377 578 380 578Q401 578 401 547Q401 543 401 537T400 527Q409 53 648 19Q676 16 676 -2Q676 -10 670 -16T655 -22Q654 -22 642 -20Q556 -9 492 56T395 244Q381 294 381 303Q381 305 380 305T374 275T352 201T310 110T234 27T117 -20Q105 -22 104 -22'],

    // DOUBLE SUBSET
    0x22D0: [541,41,778,84,694,'84 250Q84 372 166 450T360 539Q361 539 370 539T395 539T430 540T475 540T524 540H679Q694 532 694 520Q694 511 681 501L522 500H470H441Q366 500 338 496T266 472Q244 461 224 446T179 404T139 337T124 250V245Q124 157 185 89Q244 25 328 7Q348 2 366 2T522 0H681Q694 -10 694 -20Q694 -32 679 -40H526Q510 -40 480 -40T434 -41Q350 -41 289 -25T172 45Q84 127 84 250ZM694 134Q694 123 679 114H425H384Q350 114 326 121T277 154Q238 193 238 251Q238 322 295 361Q318 378 339 382T412 387Q423 387 459 387T520 386H679Q694 377 694 366Q694 354 679 346H519Q493 346 458 346T411 347Q360 347 341 342T303 315Q278 287 278 250Q278 210 301 187T351 156Q358 154 519 154H679Q694 146 694 134'],

    // DOUBLE SUPERSET
    0x22D1: [541,40,778,83,693,'83 520Q83 532 98 540H251Q267 540 297 540T343 541Q427 541 488 525T605 455Q693 374 693 250Q693 165 650 99T545 0T415 -39Q407 -40 251 -40H98Q83 -32 83 -20Q83 -10 96 0H255H308H337Q412 0 439 4T512 28Q533 39 553 54T599 96T639 163T654 250Q654 341 592 411Q557 449 512 472Q468 491 439 495T335 500H306H255L96 501Q83 511 83 520ZM83 366Q83 376 96 386H244Q280 386 317 386T378 386L402 387Q456 387 498 348T540 250Q540 203 512 168T446 120Q427 114 353 114H99Q84 120 84 134Q84 147 98 154H258Q284 154 319 154T366 153Q416 153 436 158T474 185Q500 214 500 250Q500 290 477 313T426 344Q419 346 258 346H98Q83 354 83 366'],

    // DOUBLE INTERSECTION
    0x22D2: [598,22,667,55,611,'88 -21T75 -21T55 -7V200Q55 231 55 280Q56 414 60 428Q61 430 61 431Q77 500 152 549T332 598Q443 598 522 544T610 405Q611 399 611 194V-7Q604 -22 591 -22Q582 -22 572 -9L570 405Q563 433 556 449T529 485Q498 519 445 538T334 558Q251 558 179 518T96 401Q95 396 95 193V-7Q88 -21 75 -21ZM229 -21H227Q215 -21 209 -7V166Q209 304 209 327T215 363Q226 398 259 421T333 444Q380 444 414 416T455 347Q457 339 457 166V-7Q449 -21 439 -21H437H435Q423 -21 417 -7V164Q417 303 417 325T411 358Q387 403 333 403T255 358Q250 347 250 325T249 164V-7Q241 -21 231 -21H229'],

    // DOUBLE UNION
    0x22D3: [598,22,667,55,611,'591 598H592Q604 598 611 583V376Q611 345 611 296Q610 162 606 148Q605 146 605 145Q586 68 507 23T333 -22Q268 -22 209 -1T106 66T56 173Q55 180 55 384L56 585Q66 598 75 598Q85 598 95 585V378L96 172L98 162Q112 95 181 57T332 18Q415 18 487 58T570 175Q571 180 571 383V583Q579 598 591 598ZM437 598Q450 598 457 583V410Q457 237 455 229Q448 189 414 161T333 132Q291 132 255 157T211 230Q209 237 209 412L210 585Q220 598 229 598Q242 598 249 583V412Q249 273 249 251T255 218Q279 173 333 173T411 218Q416 229 416 251T417 412V583Q425 598 437 598'],

    // PITCHFORK
    0x22D4: [736,22,667,56,611,'76 -22Q64 -22 56 -7V176L57 360L59 370Q66 401 83 426T123 468T171 495T221 513T265 522T298 527L311 528H314V625L315 723Q325 736 334 736Q346 736 354 721V528H356L368 527Q380 526 399 523T441 515T490 498T537 472T578 433T606 379Q611 359 611 171V-7Q604 -21 591 -21T571 -7V170Q571 313 571 337T565 375Q555 408 526 432T461 467T402 482T365 487H354V-7Q347 -21 334 -21T314 -7V487H303Q251 484 207 467Q121 438 99 367L97 357L96 174V-9Q86 -22 76 -22'],

    // LESS-THAN WITH DOT
    0x22D6: [541,41,778,82,694,'86 261Q92 267 381 404T673 541Q680 541 686 535T693 521T689 510Q684 504 418 379L151 250L418 121Q686 -4 689 -10Q693 -14 693 -21T687 -34T675 -41Q668 -41 380 96T86 239Q82 244 82 250Q82 257 86 261ZM610 250Q610 224 592 198T531 172Q498 172 475 195Q453 214 453 250Q453 308 513 328Q515 330 535 330Q569 328 589 304T610 250'],

    // GREATER-THAN WITH DOT
    0x22D7: [541,41,778,82,693,'82 521Q82 529 89 535T100 541Q107 541 395 404T689 261Q693 257 693 250T689 239Q684 234 396 97T100 -41Q95 -41 89 -35T82 -21Q82 -12 96 -4Q118 9 358 121L624 250L358 379Q91 503 86 510Q82 514 82 521ZM165 250Q165 282 188 306T239 330Q262 330 275 323Q303 312 318 283Q322 272 322 250Q322 213 300 195Q277 172 246 172Q224 172 213 177Q165 200 165 250'],

    // VERY MUCH LESS-THAN
    0x22D8: [568,67,1333,56,1277,'639 -48Q639 -54 634 -60T619 -67H618Q612 -67 536 -26Q430 33 329 88Q61 235 59 239Q56 243 56 250T59 261Q62 266 336 415T615 567L619 568Q622 567 625 567Q639 562 639 548Q639 540 633 534Q632 532 374 391L117 250L374 109Q632 -32 633 -34Q639 -40 639 -48ZM958 -48Q958 -54 953 -60T938 -67H937Q931 -67 855 -26Q749 33 648 88Q380 235 378 239Q375 243 375 250T378 261Q381 266 655 415T934 567L938 568Q941 567 944 567Q958 562 958 548Q958 540 952 534Q951 532 693 391L436 250L693 109Q951 -32 952 -34Q958 -40 958 -48ZM1277 -48Q1277 -54 1272 -60T1257 -67H1256Q1250 -67 1174 -26Q1068 33 967 88Q699 235 697 239Q694 243 694 250T697 261Q700 266 974 415T1253 567L1257 568Q1260 567 1263 567Q1277 562 1277 548Q1277 540 1271 534Q1270 532 1012 391L755 250L1012 109Q1270 -32 1271 -34Q1277 -40 1277 -48'],

    // VERY MUCH GREATER-THAN
    0x22D9: [568,68,1333,55,1277,'75 -67Q65 -67 60 -61T55 -48Q55 -40 61 -34Q62 -32 329 109L595 250L329 391Q62 532 61 534Q55 540 55 548Q55 562 69 567H77Q81 567 222 493T506 342T653 264Q667 250 653 236Q649 234 504 157T220 7T77 -67H75ZM364 547Q364 563 381 567L384 568Q387 568 518 499T795 353T955 269Q967 261 967 250T955 231Q925 216 780 139T513 -3T383 -67Q373 -67 369 -60T364 -47Q364 -40 370 -34Q373 -31 639 109L904 250L639 391Q373 531 370 534Q364 540 364 547ZM674 538T674 548T681 562T693 567Q699 567 816 505Q915 453 993 412Q1050 382 1132 339Q1241 282 1259 271T1277 250Q1277 241 1263 232Q1246 221 985 84Q698 -67 692 -67Q674 -67 674 -47Q674 -38 680 -33Q683 -30 947 109L1213 250L947 391Q683 530 680 533Q674 538 674 548'],

    // stix-less, equal, slanted, greater
    0x22DA: [886,386,778,83,674,'674 445Q674 438 669 432T655 425T369 531T90 640Q83 645 83 655Q83 668 95 673Q644 886 654 886Q662 886 668 880T674 866Q674 856 663 850Q649 843 411 751L160 655L407 560Q474 534 561 501Q646 469 660 462T674 445ZM84 250Q84 260 99 270H658Q674 264 674 250Q674 238 659 230H98Q84 237 84 250ZM83 55Q83 68 94 73Q98 76 104 76Q108 75 383 -30T664 -138Q674 -144 674 -155Q674 -165 667 -170Q664 -173 385 -279T104 -386Q85 -386 83 -368Q83 -354 92 -349Q93 -349 347 -251L597 -155L346 -59Q296 -40 223 -12Q118 28 101 36T83 55'],

    // stix-greater, equal, slanted, less
    0x22DB: [886,386,778,83,674,'111 425T102 425T88 431T83 445V446Q83 455 96 461Q111 469 203 504Q287 536 350 560L597 655L346 751Q94 848 92 850Q83 856 83 866Q83 873 88 879T104 886Q109 885 386 779T667 670Q674 665 674 655T667 640Q665 638 388 532ZM84 250Q84 260 99 270H658Q674 264 674 250Q674 238 659 230H98Q84 237 84 250ZM653 76Q656 76 660 75T669 68T674 56Q674 46 665 40Q663 38 411 -59L160 -155L410 -251Q664 -349 665 -349Q674 -354 674 -368Q672 -386 654 -386Q650 -386 371 -279T90 -170Q83 -165 83 -155Q83 -144 93 -138Q645 76 653 76'],

    // EQUAL TO OR PRECEDES
    0x22DE: [734,0,778,83,694,'113 424Q83 424 83 444Q83 453 96 464H121Q181 466 234 474T341 501T435 545T505 613T542 707Q545 734 564 734Q583 731 583 714Q583 658 560 613T500 538T414 486T321 453T229 434T156 426T113 424ZM112 270Q83 270 83 290Q83 301 94 307Q98 310 118 310Q624 310 653 556Q657 580 675 580Q693 577 693 559V552Q684 472 628 410T465 314Q436 303 372 290Q373 290 388 287T425 278T465 266Q674 199 693 28L694 17L692 14Q691 11 689 8T683 3T673 0Q657 0 653 24Q623 270 118 270H112'],

    // EQUAL TO OR SUCCEEDS
    0x22DF: [734,0,778,83,694,'195 713Q195 725 201 729T214 734Q227 734 231 722T238 691T255 641T299 580Q405 474 656 464H681Q694 451 694 443Q694 424 670 424H664Q535 424 415 465T235 595Q195 657 195 713ZM668 310Q694 310 694 290Q694 285 691 279Q684 271 664 270Q550 268 464 257T301 220T179 146T124 27Q119 0 103 0T83 16Q83 21 83 31T92 68T113 121T157 177T229 231Q295 268 405 290Q404 290 389 293T352 302T312 314Q138 371 96 500Q83 541 83 562Q83 568 89 574T103 580Q115 580 120 570T126 542T138 497T173 442Q289 310 659 310H668'],

    // stix-not (vert) precedes or contour equals
    0x22E0: [801,303,778,82,693,'82 344Q82 349 95 364H124Q266 364 398 390L429 397L509 595Q519 619 536 659Q581 766 590 783T609 801Q616 801 622 795T629 781Q629 776 553 595Q533 548 516 506T489 439T480 415Q482 415 505 426T538 444Q632 498 651 601Q654 621 658 628T673 635Q680 635 686 629T693 615Q693 591 678 546Q636 433 484 375L458 364L451 348Q443 332 443 329T455 324Q480 316 503 307T560 277T619 233T664 170T691 86Q693 68 691 64Q684 53 672 53Q664 53 658 59Q657 60 650 97T617 174T538 244Q515 257 476 273T428 289Q425 289 412 256Q381 179 344 90L262 -103H680Q682 -105 684 -108T688 -113T691 -118T693 -124Q693 -134 682 -141L464 -143H246L213 -219Q182 -292 178 -299Q172 -303 166 -303T153 -297T146 -283Q146 -282 174 -213T202 -143H95Q82 -128 82 -123T95 -103H220L302 97Q384 288 384 299Q384 302 341 308T235 319T124 324H95Q82 337 82 344ZM399 338Q403 338 406 346L409 353L375 344Q375 343 384 341T399 338'],

    // stix-not (vert) succeeds or contour equals
    0x22E1: [801,303,778,82,694,'146 -283Q146 -282 174 -213T202 -143H95Q82 -127 82 -123T95 -103H220L300 93Q343 196 374 270Q385 294 386 299L373 295Q331 287 289 268Q241 249 208 224T159 174T135 127T124 85T118 59Q112 53 103 53Q91 53 84 64Q82 68 84 86Q96 185 174 248T375 337L400 344Q399 344 381 348T351 355T316 364T276 379T235 398T193 424T155 456T122 497T98 546Q82 587 82 615Q82 622 88 628T102 635Q112 635 116 628T124 601Q128 579 134 562T159 515T207 463T290 418T415 384L422 381L506 586Q571 744 584 772T609 801Q616 801 622 795T629 781T544 577Q525 529 504 478T473 402T462 375Q480 373 500 373Q579 364 651 364H680Q682 361 686 357T691 351T693 344Q693 337 680 324H651Q553 324 451 310L433 308L349 104L262 -101L473 -103H682Q694 -115 694 -123Q694 -133 682 -141L464 -143H246L213 -219Q182 -292 178 -299Q172 -303 166 -303T153 -297T146 -283'],

    // LESS-THAN BUT NOT EQUIVALENT TO
    0x22E6: [730,359,778,55,719,'86 450Q93 455 380 592T673 730Q680 730 686 724T693 710Q693 702 688 699Q686 693 417 568L151 439L417 310Q685 185 688 179Q693 176 693 168Q693 161 687 155T675 148Q668 148 380 285T86 428Q74 438 86 450ZM55 -205Q55 -175 64 -142T92 -76T145 -22T222 -1Q288 -1 362 -66Q369 -72 372 -75T378 -79T382 -81T384 -79Q389 -74 439 21Q483 100 490 111T504 122Q510 122 518 118T526 103Q526 101 510 69T467 -12T419 -99L413 -112L433 -128Q498 -180 553 -180Q605 -180 646 -139Q672 -112 681 -77T693 -21T706 -1Q719 -1 719 -33Q719 -39 717 -57Q708 -141 655 -190Q625 -224 586 -232Q568 -237 551 -237Q487 -237 413 -172L391 -155Q391 -157 335 -255Q297 -325 286 -342T268 -359Q260 -359 254 -353T248 -339T304 -230L359 -126Q359 -124 337 -107T302 -81Q262 -57 221 -57Q170 -57 130 -93T84 -201Q82 -236 70 -236Q55 -236 55 -205'],

    // GREATER-THAN BUT NOT EQUIVALENT TO
    0x22E7: [730,359,778,55,719,'88 723Q95 730 99 730Q106 730 394 593T688 450Q693 447 693 439T688 428Q683 423 395 286T99 148Q94 148 88 155T82 168Q82 175 86 179Q89 184 357 310L624 439L357 568Q88 694 86 699Q81 703 81 711T88 723ZM55 -205Q55 -175 64 -142T92 -76T145 -22T222 -1Q288 -1 362 -66Q369 -72 372 -75T378 -79T382 -81T384 -79Q389 -74 439 21Q483 100 490 111T504 122Q510 122 518 118T526 103Q526 101 510 69T467 -12T419 -99L413 -112L433 -128Q498 -180 553 -180Q605 -180 646 -139Q672 -112 681 -77T693 -21T706 -1Q719 -1 719 -33Q719 -39 717 -57Q708 -141 655 -190Q625 -224 586 -232Q568 -237 551 -237Q487 -237 413 -172L391 -155Q391 -157 335 -255Q297 -325 286 -342T268 -359Q260 -359 254 -353T248 -339T304 -230L359 -126Q359 -124 337 -107T302 -81Q262 -57 221 -57Q170 -57 130 -93T84 -201Q82 -236 70 -236Q55 -236 55 -205'],

    // PRECEDES BUT NOT EQUIVALENT TO
    0x22E8: [730,359,778,55,719,'95 419Q81 433 81 439T95 459H124Q318 459 455 501Q515 521 556 550T615 607T641 659T652 702T659 725Q667 730 673 730Q680 730 686 724T693 710Q693 682 677 641Q668 616 654 594T622 554T586 522T545 497T504 477T464 462T428 452T397 444T375 439Q379 437 410 430T476 411T551 379T625 321T677 237Q693 196 693 168Q693 161 687 155T673 148Q662 148 658 154T651 181Q638 253 591 300T455 377Q318 419 124 419H95ZM55 -205Q55 -175 64 -142T92 -76T145 -22T222 -1Q288 -1 362 -66Q369 -72 372 -75T378 -79T382 -81T384 -79Q389 -74 439 21Q483 100 490 111T504 122Q510 122 518 118T526 103Q526 101 510 69T467 -12T419 -99L413 -112L433 -128Q498 -180 553 -180Q605 -180 646 -139Q672 -112 681 -77T693 -21T706 -1Q719 -1 719 -33Q719 -39 717 -57Q708 -141 655 -190Q625 -224 586 -232Q568 -237 551 -237Q487 -237 413 -172L391 -155Q391 -157 335 -255Q297 -325 286 -342T268 -359Q260 -359 254 -353T248 -339T304 -230L359 -126Q359 -124 337 -107T302 -81Q262 -57 221 -57Q170 -57 130 -93T84 -201Q82 -236 70 -236Q55 -236 55 -205'],

    // SUCCEEDS BUT NOT EQUIVALENT TO
    0x22E9: [730,359,778,55,719,'679 459Q693 445 693 439Q693 430 679 419H651Q455 419 319 377Q231 347 184 300T124 181Q120 161 116 155T102 148Q95 148 89 154T82 168Q82 192 97 237Q111 275 137 306T188 355T249 391T307 414T361 429T399 439Q397 440 364 447T298 467T224 499T149 557T97 641Q82 686 82 710Q82 717 88 723T102 730L115 725Q118 722 124 697Q137 625 184 578T319 501Q456 459 651 459H679ZM55 -205Q55 -175 64 -142T92 -76T145 -22T222 -1Q288 -1 362 -66Q369 -72 372 -75T378 -79T382 -81T384 -79Q389 -74 439 21Q483 100 490 111T504 122Q510 122 518 118T526 103Q526 101 510 69T467 -12T419 -99L413 -112L433 -128Q498 -180 553 -180Q605 -180 646 -139Q672 -112 681 -77T693 -21T706 -1Q719 -1 719 -33Q719 -39 717 -57Q708 -141 655 -190Q625 -224 586 -232Q568 -237 551 -237Q487 -237 413 -172L391 -155Q391 -157 335 -255Q297 -325 286 -342T268 -359Q260 -359 254 -353T248 -339T304 -230L359 -126Q359 -124 337 -107T302 -81Q262 -57 221 -57Q170 -57 130 -93T84 -201Q82 -236 70 -236Q55 -236 55 -205'],

    // NOT NORMAL SUBGROUP OF
    0x22EA: [707,208,778,82,693,'693 -30Q686 -41 673 -41Q661 -41 506 34L346 110L280 -44Q228 -162 216 -185T193 -208Q177 -208 173 -192Q173 -186 242 -30T311 128Q271 145 184 186T86 236Q82 240 82 246Q82 251 86 259Q96 267 271 350L449 434L506 565Q537 635 551 664T571 700T582 706Q587 706 593 701T600 690Q600 679 553 572Q504 463 504 461L586 501Q672 539 673 539Q679 539 693 525V-30ZM653 10V488L566 445L480 405L422 276Q415 260 405 236T388 199T376 171T368 151T366 145Q368 143 510 77T653 10ZM422 374Q422 376 420 376T285 313T151 248Q315 168 326 163Q415 356 422 374'],

    // DOES NOT CONTAIN AS NORMAL SUBGROUP
    0x22EB: [706,208,778,82,693,'82 525Q96 539 102 539Q103 539 122 530T186 501T266 463L426 388Q428 388 495 541Q564 694 569 699Q573 706 581 706Q587 706 593 702T600 691Q600 676 533 528Q515 486 506 465T485 418T470 381T466 370Q466 369 575 316Q676 269 689 259Q693 253 693 248Q693 242 689 236Q688 235 506 145Q328 63 324 59Q324 50 266 -70Q224 -169 214 -188T193 -208Q177 -208 173 -192Q173 -183 222 -77Q244 -29 257 2T269 34L186 -6Q108 -43 99 -43Q93 -43 82 -30V525ZM271 416Q129 485 126 485H125Q122 485 122 250Q122 10 124 10L211 50L295 92L411 350Q411 351 271 416ZM624 248L449 332L440 319Q434 297 393 214Q353 121 353 119Q355 119 489 182T624 248'],

    // stix-not, vert, left triangle, equals
    0x22EC: [802,303,778,82,693,'82 -123Q82 -114 93 -103H166L238 -101L293 50Q349 200 349 204L220 266Q166 291 140 304T100 325T84 336T82 344Q82 353 94 360Q112 372 282 453L473 541L482 568Q487 578 529 693Q559 785 569 795Q573 802 581 802Q587 802 593 797T599 786Q599 775 564 675L526 570Q526 568 561 584T633 617T673 635Q679 635 693 621V66Q686 54 679 54Q665 54 526 119Q491 137 458 153T405 177T386 184Q385 182 334 42T282 -101T482 -103H680Q682 -105 684 -108T688 -113T691 -118T693 -124Q693 -134 682 -141L473 -143H266L238 -219Q217 -278 210 -290T193 -303Q178 -303 173 -287Q173 -279 198 -214L222 -145Q222 -143 158 -143L93 -141L86 -136Q82 -131 82 -123ZM653 106V584L506 513L453 370Q442 339 428 300T407 243T400 224Q403 222 527 164T653 106ZM453 486Q453 488 451 488T300 417T151 344L224 308Q247 298 285 279T331 257L364 241L453 486'],

    // stix-not, vert, right triangle, equals
    0x22ED: [801,303,778,82,693,'82 621Q96 635 102 635T249 568L420 486L449 473L469 533Q563 789 569 797Q573 801 581 801Q598 801 600 786Q602 781 544 617L484 455Q531 435 584 408Q677 364 689 355Q693 351 693 344Q693 339 692 337T676 325T631 302T538 257Q504 241 465 223T406 195T386 186Q384 184 333 44T282 -101Q282 -103 482 -103H680Q682 -105 684 -108T688 -113T691 -118T693 -124Q693 -134 682 -141L473 -143H266L238 -219Q217 -278 210 -290T193 -303Q178 -303 173 -287Q173 -279 198 -214L222 -145Q222 -143 158 -143L93 -141L86 -136Q82 -131 82 -123Q82 -114 93 -103H166L238 -101L333 159Q326 159 220 106Q110 54 97 54Q89 54 82 66V621ZM298 501Q155 567 142 575L122 584V344Q122 106 124 106Q125 106 180 132T291 185T351 213Q355 217 393 326L433 435Q433 436 298 501ZM549 381Q472 417 471 417L406 241Q408 240 516 291T624 344L549 381']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/MathOperators.js");
