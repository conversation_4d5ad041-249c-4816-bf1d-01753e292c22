/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/SansSerif/Bold/Other.js
 *
 *  Copyright (c) 2011-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_SansSerif-bold'],
  {
    // LATIN SMALL LETTER DOTLESS I
    0x131: [458,0,256,54,201,'54 431Q63 458 102 458H127H149Q192 458 199 433Q201 427 201 229T199 25Q190 0 149 0H125L81 1Q61 7 54 28V431'],

    // LATIN SMALL LETTER DOTLESS J
    0x237: [458,205,286,-71,232,'-38 -84Q-36 -84 -14 -95T33 -106H38Q70 -103 78 -86Q83 -78 83 -49T84 180Q84 427 86 433Q93 458 136 458H158H180Q201 458 209 456T225 443Q230 436 231 418Q232 397 232 313V183V124V40Q232 -55 228 -87T203 -147Q166 -205 78 -205Q31 -205 -20 -189T-71 -159Q-71 -156 -59 -123Q-50 -96 -47 -91T-38 -84'],

    // GREEK CAPITAL LETTER GAMMA
    0x393: [691,0,581,92,534,'92 664Q98 683 118 690Q121 691 312 691T508 689Q534 682 534 644V632V618Q534 582 508 573L502 572Q496 572 489 572Q486 572 463 572T416 573Q333 573 291 575H253V303Q253 31 251 25Q242 0 199 0H170L119 1Q99 7 92 28V664'],

    // GREEK CAPITAL LETTER DELTA
    0x394: [694,0,917,60,856,'381 692Q386 694 458 694Q516 694 527 693T549 687Q564 680 575 663Q576 658 715 349T856 27Q856 6 838 1H826Q815 1 795 1T747 1T686 1T616 0T539 0T458 0T378 0T300 0T230 0T169 1T122 1T90 1H78Q60 6 60 27Q62 38 201 349T341 663Q356 687 381 692ZM627 148Q626 149 581 250T492 453L447 554Q447 553 446 552Q444 546 326 278L268 148Q268 147 448 147Q627 147 627 148'],

    // GREEK CAPITAL LETTER THETA
    0x398: [716,22,856,62,793,'62 340Q62 716 425 716Q511 716 576 696T681 642T747 559T783 458T793 341Q793 264 777 203T721 89T608 7T428 -22Q62 -22 62 340ZM638 333Q638 365 637 387T632 441T621 495T600 542T567 583T518 611T451 628Q443 629 427 629Q402 629 378 624T327 608T276 571T240 511Q217 453 217 345Q217 254 231 204T279 120Q333 69 428 69Q522 69 576 120Q638 183 638 333ZM279 349V373Q279 413 305 420Q309 422 427 422H487Q550 422 563 414T576 369V349Q576 345 576 337T577 324Q577 284 550 277Q545 275 428 275H369Q306 275 293 283T279 329V349'],

    // GREEK CAPITAL LETTER LAMDA
    0x39B: [694,0,672,41,630,'106 0H83Q41 0 41 28Q41 39 133 349T229 667Q242 694 296 694H335H375Q403 694 418 689T442 667Q445 660 537 350T630 28Q630 11 619 6T584 0H555H526Q478 0 465 27Q462 32 431 136T366 372T325 555V546Q320 503 287 376T222 141T186 27Q184 22 177 15T165 6Q154 0 106 0'],

    // GREEK CAPITAL LETTER XI
    0x39E: [688,0,733,46,686,'627 553Q609 553 512 554T366 555Q316 555 220 554T105 553Q96 553 90 553T82 554T78 554Q61 560 57 571T52 605V623L53 661Q59 680 79 687Q82 688 366 688Q649 688 654 686Q680 679 680 639V621V603Q680 563 654 554Q653 554 651 554T642 554T627 553ZM149 423Q152 424 366 424Q579 424 584 422Q610 415 610 376V358V340Q610 300 584 293Q579 291 366 291H232Q162 291 150 293T129 306Q122 315 122 360L123 397Q129 416 149 423ZM108 135Q143 135 226 134T363 133Q407 133 507 134T632 135H645Q675 135 684 110Q686 104 686 68V49Q686 9 660 2Q655 0 364 0L74 1Q57 7 49 21L47 28L46 65V83Q46 126 72 133Q80 135 108 135'],

    // GREEK CAPITAL LETTER PI
    0x3A0: [691,0,794,92,702,'92 664Q98 683 118 690Q121 691 396 691T676 689Q695 684 700 666Q702 660 702 345Q702 31 700 25Q696 9 684 5T646 0H621H596Q571 0 559 4T542 25Q540 31 540 307V582H253V307Q253 31 251 25Q242 0 199 0H170L119 1Q99 7 92 28V664'],

    // GREEK CAPITAL LETTER SIGMA
    0x3A3: [694,0,794,61,732,'322 124Q326 124 457 125T672 127H689Q721 127 730 102Q732 96 732 64V48Q732 9 706 2Q701 0 394 0L89 1Q76 5 69 13T62 29V36Q62 37 62 38Q62 47 70 58T126 126Q161 167 185 196Q302 335 302 336L187 463Q74 584 68 594Q61 603 61 639L62 667Q68 686 88 693Q91 694 396 694T706 692Q732 686 732 647V635V621Q732 585 706 576Q705 576 702 576T691 576T670 575L302 578Q302 577 394 475T490 371Q498 362 498 347Q498 336 488 323T408 226L322 124'],

    // GREEK CAPITAL LETTER UPSILON
    0x3A5: [715,0,856,62,793,'62 560Q62 607 94 644T169 698T253 715Q273 715 286 713T322 704T363 677T398 625Q413 597 423 556L428 540Q429 541 436 566T454 620T494 677T561 713Q570 715 593 715Q682 715 737 668T793 560Q793 549 793 545T786 533T767 520H670Q646 532 644 551T632 579Q618 594 591 594Q539 594 524 530T509 321V216Q509 31 507 25Q498 0 455 0H426L375 1Q355 7 348 28L347 232Q346 344 346 441Q346 442 343 468T335 521T312 571T266 594Q252 594 247 593Q228 586 220 576T212 557T209 539T191 523L185 520H88Q75 527 69 534T63 545T62 560'],

    // GREEK CAPITAL LETTER PHI
    0x3A6: [694,0,794,62,732,'62 292T62 347T80 445T124 511T183 552T243 574T292 584L315 587H319V627L320 667Q329 694 370 694H397H422Q466 694 473 669Q475 663 475 625V587H478Q479 587 500 584T548 575T608 553T668 513T713 446T732 347Q732 253 674 187Q655 167 628 152T576 128T530 116T493 109L478 107H475V69V50Q475 9 449 2Q444 0 395 0L347 1Q327 7 320 28L319 67V107H315L292 110Q269 114 243 119T184 142T124 182T80 249ZM319 197T319 347T318 497Q316 497 307 494T284 485T262 471Q220 438 220 347Q220 285 239 249Q248 234 261 223T286 208T308 200L317 197Q319 197 319 347ZM572 347V357Q572 387 569 407T548 452T496 491Q495 491 494 491T487 493T475 497V197Q518 210 541 232T571 303Q572 312 572 347'],

    // GREEK CAPITAL LETTER PSI
    0x3A8: [694,0,856,61,794,'61 585Q62 594 62 597T64 606T73 616T89 626H138Q196 626 208 620Q243 602 253 546T261 431T271 309T325 219Q342 205 349 205Q350 205 350 436L351 667Q360 694 401 694H428H454Q495 694 504 671Q506 663 506 436L507 205Q542 222 561 251T586 318T593 392T595 472T602 546Q614 614 661 625Q665 626 708 626H730Q766 626 780 618T794 582Q794 548 768 540Q755 538 754 501T750 410T736 298T680 191T560 120Q550 116 512 109H506V70V50Q506 9 480 2Q475 0 426 0L378 1Q358 7 351 28L350 68V109L335 111Q298 117 267 129T214 156T175 191T146 229T127 272T115 314T109 357T106 395T105 429Q104 537 87 540Q66 548 63 565Q61 570 61 585'],

    // GREEK CAPITAL LETTER OMEGA
    0x3A9: [716,1,794,49,744,'241 122Q225 154 191 199T131 278T83 363T61 464Q61 497 68 527T94 591T145 650T228 693T349 715Q354 715 370 715T396 716Q539 716 622 668Q658 647 682 617T715 556T728 505T732 465Q732 415 711 365T663 280T602 200T552 122H632Q649 122 669 122T693 123H697Q736 123 742 98Q744 92 744 62V47Q744 9 718 2Q713 0 591 0L471 1Q454 7 446 21Q444 27 444 45Q444 96 463 154T506 257T549 360T569 469Q569 504 563 530T538 580T485 616T396 629Q313 629 268 594T223 468Q223 419 243 361T286 258T330 152T350 41Q350 14 335 7T276 -1Q267 -1 241 -1T197 0L77 1Q57 7 50 28L49 59V74Q49 114 75 121Q81 123 100 123Q104 123 124 123T161 122H241'],

    // EN DASH
    0x2013: [327,-240,550,0,549,'0 284Q0 318 26 325Q30 327 274 327Q469 327 497 327T532 322Q549 310 549 283Q549 257 532 245Q525 241 498 241T275 240Q31 240 26 242Q0 249 0 284'],

    // EM DASH
    0x2014: [327,-240,1100,0,1099,'0 284Q0 318 26 325Q30 327 549 327T1073 325Q1099 318 1099 284Q1099 249 1073 242Q1068 240 549 240Q31 240 26 242Q0 249 0 284'],

    // LEFT SINGLE QUOTATION MARK
    0x2018: [694,-443,306,81,226,'87 443L81 449V581L119 636Q125 644 131 653T141 667T148 677T154 685T158 689T163 692T167 693T173 694H190H201Q226 694 226 683Q226 678 208 635T189 590Q189 588 204 588H219Q222 584 226 581V449L219 443H87'],

    // RIGHT SINGLE QUOTATION MARK
    0x2019: [694,-442,306,80,226,'81 687Q85 693 88 693Q89 694 154 694H219Q222 690 226 687V556L187 501Q182 494 177 486T169 474T163 465T157 457T153 451T149 447T145 445T141 443T137 443T132 442H116H105Q80 442 80 453Q80 458 98 501T117 546Q117 548 102 548L87 549L81 555V687'],

    // LEFT DOUBLE QUOTATION MARK
    0x201C: [694,-443,558,138,520,'144 443L138 449V581L176 636Q182 644 188 653T198 667T205 677T211 685T215 689T220 692T224 693T230 694H247H258Q283 694 283 683Q283 678 265 635T246 590Q246 588 261 588H276Q279 584 283 581V449L276 443H144ZM381 443L375 449V581L413 636Q419 644 425 653T435 667T442 677T448 685T452 689T457 692T461 693T467 694H484H495Q520 694 520 683Q520 678 502 635T483 590Q483 588 498 588H513Q516 584 520 581V449L513 443H381'],

    // RIGHT DOUBLE QUOTATION MARK
    0x201D: [694,-442,558,37,420,'38 687Q42 693 45 693Q46 694 111 694H176Q179 690 183 687V556L144 501Q139 494 134 486T126 474T120 465T114 457T110 451T106 447T102 445T98 443T94 443T89 442H73H62Q37 442 37 453Q37 458 55 501T74 546Q74 548 59 548L44 549L38 555V687ZM275 687Q279 693 282 693Q283 694 348 694H413Q416 690 420 687V556L381 501Q376 494 371 486T363 474T357 465T351 457T347 451T343 447T339 445T335 443T331 443T326 442H310H299Q274 442 274 453Q274 458 292 501T311 546Q311 548 296 548L281 549L275 555V687']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/SansSerif/Bold/Other.js");
