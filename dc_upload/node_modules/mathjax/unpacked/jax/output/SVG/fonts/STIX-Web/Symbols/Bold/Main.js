/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/STIX-Web/Symbols/Bold/Main.js
 *  
 *  Copyright (c) 2013-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax['SVG'].FONTDATA.FONTS['STIXMathJax_Symbols-bold'] = {
  directory: 'Symbols/Bold',
  family: 'STIXMathJax_Symbols',
  weight: 'bold',
  id: 'STIXWEBSYMBOLSB',
  0x20: [0,0,250,0,0,''],
  0xA0: [0,0,250,0,0,''],
  0x2302: [774,0,926,55,871,'871 0h-816v408l408 366l408 -366v-408zM783 88v284l-320 289l-320 -289v-284h640'],
  0x2310: [399,-108,750,65,685,'685 311h-532v-203h-88v291h620v-88'],
  0x2319: [399,-108,750,65,685,'685 108h-620v291h88v-203h532v-88'],
  0x2329: [732,193,445,69,399,'399 -193h-100l-230 455v15l226 455h100l-230 -462'],
  0x232A: [732,193,445,46,376,'376 262l-230 -455h-100l234 463l-230 462h100l226 -455v-15'],
  0x2336: [751,156,926,85,841,'841 -156h-756v66h333v775h-333v66h756l-1 -66h-334v-775h335v-66'],
  0x233D: [694,190,924,80,844,'506 694v-62c190 -22 338 -184 338 -380s-148 -358 -338 -380v-62h-88v62c-190 22 -338 184 -338 380s148 358 338 380v62h88zM506 543v-582c142 21 250 144 250 291s-108 270 -250 291zM418 -39v582c-142 -21 -250 -144 -250 -291s108 -270 250 -291'],
  0x233F: [732,200,728,55,673,'673 222h-274l-149 -422h-96l150 422h-249v88h280l151 422h96l-151 -422h242v-88'],
  0x23AF: [297,-209,315,0,315,'315 209h-315v88h315v-88'],
  0x27C8: [547,13,1025,62,943,'943 -13h-288c-175 0 -288 126 -288 280s113 280 291 280h285v-88h-301c-100 0 -187 -85 -187 -192c0 -108 88 -192 188 -192h300v-88zM330 -13h-84l-184 558h83'],
  0x27C9: [547,13,1025,62,943,'943 545l-184 -558h-84l185 558h83zM62 547h285c178 0 291 -126 291 -280s-113 -280 -288 -280h-288v88h300c100 0 188 84 188 192c0 107 -87 192 -187 192h-301v88'],
  0x2980: [705,200,675,105,570,'570 -200h-88v905h88v-905zM382 -200h-88v905h88v-905zM193 -200h-88v905h88v-905'],
  0x29B6: [634,130,864,50,814,'814 252c0 -211 -171 -382 -382 -382s-382 171 -382 382s171 382 382 382s382 -171 382 -382zM476 543v-582c143 21 250 146 250 291s-107 270 -250 291zM388 -39v582c-143 -21 -250 -146 -250 -291s107 -270 250 -291'],
  0x29B7: [634,130,864,50,814,'582 39h-88v426h88v-426zM368 39h-88v426h88v-426zM814 252c0 -211 -171 -382 -382 -382s-382 171 -382 382s171 382 382 382s382 -171 382 -382zM726 252c0 162 -132 294 -294 294s-294 -132 -294 -294s132 -294 294 -294s294 132 294 294'],
  0x29B8: [634,130,864,50,814,'814 252c0 -211 -171 -382 -382 -382s-382 171 -382 382s171 382 382 382s382 -171 382 -382zM258 489l411 -411c36 48 57 109 57 174c0 162 -132 294 -294 294c-65 0 -126 -21 -174 -57zM606 15l-411 411c-36 -48 -57 -109 -57 -174c0 -162 132 -294 294 -294 c65 0 126 21 174 57'],
  0x29C0: [634,130,864,50,814,'579 35l-391 197v39l391 198v-101l-232 -115l232 -115v-103zM814 252c0 -211 -171 -382 -382 -382s-382 171 -382 382s171 382 382 382s382 -171 382 -382zM726 252c0 162 -132 294 -294 294s-294 -132 -294 -294s132 -294 294 -294s294 132 294 294'],
  0x29C1: [634,130,864,50,814,'676 232l-391 -197v103l232 115l-232 115v101l391 -198v-39zM814 252c0 -211 -171 -382 -382 -382s-382 171 -382 382s171 382 382 382s382 -171 382 -382zM726 252c0 162 -132 294 -294 294s-294 -132 -294 -294s132 -294 294 -294s294 132 294 294'],
  0x29C4: [661,158,910,45,865,'865 -158h-820v819h820v-819zM777 -70v590l-590 -590h590zM730 573h-597v-597'],
  0x29C5: [661,158,910,45,865,'865 -158h-820v819h820v-819zM777 -20v593h-593zM727 -70l-594 594v-594h594'],
  0x29C6: [661,158,910,45,865,'663 338c0 -18 -14 -37 -31 -47c-31 -18 -87 -16 -137 -38c46 -18 112 -23 136 -38c17 -10 30 -30 30 -48c0 -27 -22 -58 -57 -58c-43 0 -81 72 -129 115c7 -47 37 -115 37 -142c0 -30 -19 -61 -57 -61s-57 32 -57 61c0 27 30 95 37 142c-37 -33 -79 -94 -97 -103 c-11 -6 -25 -11 -37 -11c-33 0 -55 26 -55 51c0 17 10 43 30 55c22 13 97 22 139 37c-42 15 -119 25 -143 39c-16 10 -29 28 -29 46c0 28 23 61 55 61c9 0 20 -3 31 -9c24 -13 66 -69 106 -102c-7 49 -37 114 -37 142c0 30 16 59 57 59c37 0 57 -29 57 -59 c0 -28 -30 -92 -37 -142c44 34 78 89 102 102c11 6 22 9 31 9c32 0 55 -33 55 -61zM865 -158h-820v819h820v-819zM777 -70v643h-644v-643h644'],
  0x29C7: [661,158,910,45,865,'662 253c0 -114 -93 -207 -207 -207s-207 93 -207 207s93 207 207 207s207 -93 207 -207zM592 253c0 83 -54 137 -137 137s-137 -54 -137 -137s54 -137 137 -137s137 54 137 137zM865 -158h-820v819h820v-819zM777 -70v643h-644v-643h644']
};

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/Symbols/Bold/Main.js");
