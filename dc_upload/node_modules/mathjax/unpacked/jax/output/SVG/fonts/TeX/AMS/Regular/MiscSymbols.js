/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/AMS/Regular/MiscSymbols.js
 *
 *  Copyright (c) 2011-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_AMS'],
  {
    // BLACK STAR
    0x2605: [694,111,944,49,895,'367 395Q374 416 398 492T442 627T463 688Q463 692 467 692Q471 694 472 694Q478 694 484 680T523 562Q553 469 576 400L577 395H731H819Q872 395 883 394T895 384Q895 380 891 376T832 333Q794 305 767 285Q643 195 643 194L690 47Q737 -96 737 -103Q737 -111 727 -111Q721 -111 594 -18L472 71L350 -18Q223 -111 217 -111Q207 -111 207 -103Q207 -96 254 47L301 194Q301 195 241 239T118 328T51 378Q49 382 49 384Q49 392 58 393T110 395H213H367']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/AMS/Regular/MiscSymbols.js");
