/*************************************************************
 *
 *  MathJax/jax/output/SVG/fonts/TeX/svg/SansSerif/Regular/Other.js
 *
 *  Copyright (c) 2011-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax.SVG.FONTDATA.FONTS['MathJax_SansSerif'],
  {
    // LATIN SMALL LETTER DOTLESS I
    0x131: [444,0,239,74,164,'74 0V444H164V0H74'],

    // LATIN SMALL LETTER DOTLESS J
    0x237: [444,205,267,-60,192,'-35 -95Q-4 -126 34 -126Q58 -126 76 -116T100 -88Q102 -82 102 181V444H192V180Q191 -45 191 -70T184 -113Q171 -152 140 -178T63 -205Q34 -205 4 -197T-43 -181T-59 -171L-47 -133L-35 -95'],

    // GREEK CAPITAL LETTER GAMMA
    0x393: [691,0,542,87,499,'87 0V691H499V611H345L191 612V0H87'],

    // GREEK CAPITAL LETTER DELTA
    0x394: [694,0,833,42,790,'203 348L362 694H470L629 348Q789 2 790 1Q790 0 416 0T42 1Q43 2 203 348ZM630 98Q630 100 584 198T481 422T407 603L405 610L403 600Q388 544 191 122L180 99L405 98H630'],

    // GREEK CAPITAL LETTER THETA
    0x398: [716,21,778,56,722,'56 344Q56 430 86 502T164 619T271 690T388 716Q448 716 506 691T613 619T692 501T722 344Q722 188 624 84T389 -21Q252 -21 154 83T56 344ZM624 345Q624 423 597 488T513 596T380 639Q343 639 305 621T232 568T175 475T153 344Q153 216 222 136T388 56Q487 56 555 138T624 345ZM209 299V397H568V299H209'],

    // GREEK CAPITAL LETTER LAMDA
    0x39B: [694,0,611,28,582,'294 606Q294 574 252 430T163 144T117 0H72Q28 0 28 1T141 348L254 694H357L469 348Q582 2 582 1T527 0L473 1L469 11Q469 13 427 141T343 411T296 599L294 610V606'],

    // GREEK CAPITAL LETTER XI
    0x39E: [688,0,667,42,624,'47 600V688H619V600H47ZM111 314V401H555V314H111ZM42 0V88H624V0H42'],

    // GREEK CAPITAL LETTER PI
    0x3A0: [691,0,708,86,621,'86 0V691H621V0H517V615H190V0H86'],

    // GREEK CAPITAL LETTER SIGMA
    0x3A3: [694,0,722,55,666,'55 0Q56 3 171 167T288 332Q288 334 172 474L55 615V694H666V614H428L190 615L412 347L322 218Q236 97 228 84L447 85H666V0H55'],

    // GREEK CAPITAL LETTER UPSILON
    0x3A5: [716,0,778,55,722,'55 565Q59 625 105 670T219 716H222Q310 716 353 627Q376 583 386 524L389 510L393 532Q397 555 407 584T433 644T482 695T557 716Q621 716 669 673T722 565V555H629V563Q627 592 607 615T557 638Q530 638 511 629T479 598T459 553T447 488T442 411T441 319V202V0H337V202Q337 453 331 497Q313 634 226 638Q185 638 167 612T148 563V555H55V565'],

    // GREEK CAPITAL LETTER PHI
    0x3A6: [694,0,722,55,666,'666 347Q666 326 661 302T638 247T594 190T520 140T413 107H410V0H312V54Q312 107 311 107Q286 107 229 128T125 192Q55 260 55 347Q55 396 77 438T131 507T200 552T265 579T311 587Q312 587 312 641V694H410V587H413Q476 576 524 552T598 502T640 444T661 390T666 347ZM310 510Q305 510 291 507T252 492T208 464T172 416T157 347T171 279T204 233T247 205T286 190T310 184H312V347Q312 510 310 510ZM564 347Q564 385 549 416T514 463T470 491T433 505T414 509L410 510V184Q413 184 426 187T464 200T510 227T548 275T564 347'],

    // GREEK CAPITAL LETTER PSI
    0x3A8: [694,0,778,55,722,'340 187V694H438V187Q481 206 495 219Q518 239 533 267T553 332T560 386T562 435Q562 576 593 608Q608 621 637 621H670H722V545H719Q718 545 715 545T710 544Q679 536 666 487Q664 474 662 429T654 344T633 259T580 175T486 119Q450 109 438 108V0H340V108L326 110Q122 149 117 415Q116 460 111 487Q98 536 67 544Q65 544 62 544T58 545H55V621H107Q160 621 163 620Q191 613 202 573Q213 536 213 473T220 351T256 249Q262 239 270 230T285 216T301 205T316 197T329 192T340 187'],

    // GREEK CAPITAL LETTER OMEGA
    0x3A9: [716,0,722,44,677,'55 462Q55 561 141 638T359 716Q492 716 579 640T666 462Q666 407 642 347T579 222T529 126Q515 91 515 86Q517 85 528 85Q530 85 552 85T596 86H677V0H425V14Q429 79 465 168L492 228Q494 232 504 254T516 283T527 310T539 340T548 368T556 399T560 428T562 460Q562 531 510 585T361 639Q263 639 211 585T159 460Q159 422 170 378T192 309T229 228L256 168Q292 79 296 14V0H44V86H125Q146 86 168 86T193 85L206 86Q206 103 183 148T131 241T79 352T55 462'],

    // EN DASH
    0x2013: [312,-236,500,0,499,'0 236V312H499V236H0'],

    // EM DASH
    0x2014: [312,-236,1000,0,999,'0 236V312H999V236H0'],

    // LEFT SINGLE QUOTATION MARK
    0x2018: [694,-471,278,90,189,'90 568L140 694H189L174 633Q159 572 158 571Q158 569 173 569H188V471H90V568'],

    // RIGHT SINGLE QUOTATION MARK
    0x2019: [694,-471,278,89,188,'90 596V694H188V597L139 471H89L104 532Q119 593 120 594Q120 596 105 596H90'],

    // LEFT DOUBLE QUOTATION MARK
    0x201C: [694,-471,500,174,467,'174 568L224 694H273L258 633Q243 572 242 571Q242 569 257 569H272V471H174V568ZM368 568L418 694H467L452 633Q437 572 436 571Q436 569 451 569H466V471H368V568'],

    // RIGHT DOUBLE QUOTATION MARK
    0x201D: [694,-471,500,32,325,'33 596V694H131V597L82 471H32L47 532Q62 593 63 594Q63 596 48 596H33ZM227 596V694H325V597L276 471H226L241 532Q256 593 257 594Q257 596 242 596H227']
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax.SVG.fontDir+"/SansSerif/Regular/Other.js");
