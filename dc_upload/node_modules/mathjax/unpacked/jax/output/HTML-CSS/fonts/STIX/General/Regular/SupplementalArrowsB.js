/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SupplementalArrowsB.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2900: [450,-57,926,56,871],      // RIGHTWARDS TWO-HEADED ARROW WITH VERTICAL STROKE
    0x2901: [450,-57,926,55,871],      // RIGHTWARDS TWO-HEADED ARROW WITH DOUBLE VERTICAL STROKE
    0x2902: [551,45,926,55,871],       // LEFTWARDS DOUBLE ARROW WITH VERTICAL STROKE
    0x2903: [551,45,926,55,871],       // RIGHTWARDS DOUBLE ARROW WITH VERTICAL STROKE
    0x2904: [551,45,926,20,906],       // LEFT RIGHT DOUBLE ARROW WITH VERTICAL STROKE
    0x2905: [450,-57,926,55,871],      // RIGHTWARDS TWO-HEADED ARROW FROM BAR
    0x2906: [551,45,926,55,871],       // LEFTWARDS DOUBLE ARROW FROM BAR
    0x2907: [551,45,926,55,871],       // RIGHTWARDS DOUBLE ARROW FROM BAR
    0x2908: [662,156,511,59,452],      // DOWNWARDS ARROW WITH HORIZONTAL STROKE
    0x2909: [662,156,511,59,452],      // UPWARDS ARROW WITH HORIZONTAL STROKE
    0x290A: [662,156,926,71,854],      // UPWARDS TRIPLE ARROW
    0x290B: [662,156,926,72,855],      // DOWNWARDS TRIPLE ARROW
    0x290C: [449,-57,926,55,871],      // LEFTWARDS DOUBLE DASH ARROW
    0x290D: [449,-57,926,55,871],      // RIGHTWARDS DOUBLE DASH ARROW
    0x290E: [449,-57,926,55,871],      // LEFTWARDS TRIPLE DASH ARROW
    0x290F: [449,-57,926,55,871],      // RIGHTWARDS TRIPLE DASH ARROW
    0x2910: [449,-57,1412,55,1357],    // RIGHTWARDS TWO-HEADED TRIPLE DASH ARROW
    0x2911: [449,-57,926,55,873],      // RIGHTWARDS ARROW WITH DOTTED STEM
    0x2912: [662,156,511,59,452],      // UPWARDS ARROW TO BAR
    0x2913: [662,156,511,59,452],      // DOWNWARDS ARROW TO BAR
    0x2914: [450,-57,926,55,871],      // RIGHTWARDS ARROW WITH TAIL WITH VERTICAL STROKE
    0x2915: [450,-57,926,55,871],      // RIGHTWARDS ARROW WITH TAIL WITH DOUBLE VERTICAL STROKE
    0x2916: [449,-57,926,55,871],      // RIGHTWARDS TWO-HEADED ARROW WITH TAIL
    0x2917: [450,-57,926,55,871],      // RIGHTWARDS TWO-HEADED ARROW WITH TAIL WITH VERTICAL STROKE
    0x2918: [450,-57,926,50,876],      // RIGHTWARDS TWO-HEADED ARROW WITH TAIL WITH DOUBLE VERTICAL STROKE
    0x2919: [449,-57,926,55,871],      // LEFTWARDS ARROW-TAIL
    0x291A: [449,-57,926,55,871],      // RIGHTWARDS ARROW-TAIL
    0x291B: [449,-57,926,55,871],      // LEFTWARDS DOUBLE ARROW-TAIL
    0x291C: [449,-57,926,55,871],      // RIGHTWARDS DOUBLE ARROW-TAIL
    0x291D: [449,-57,926,55,871],      // LEFTWARDS ARROW TO BLACK DIAMOND
    0x291E: [449,-57,926,55,871],      // RIGHTWARDS ARROW TO BLACK DIAMOND
    0x291F: [450,-57,926,55,871],      // LEFTWARDS ARROW FROM BAR TO BLACK DIAMOND
    0x2920: [450,-57,926,55,871],      // RIGHTWARDS ARROW FROM BAR TO BLACK DIAMOND
    0x2921: [662,156,926,55,871],      // NORTH WEST AND SOUTH EAST ARROW
    0x2922: [660,156,926,55,873],      // NORTH EAST AND SOUTH WEST ARROW
    0x2923: [662,156,926,55,871],      // NORTH WEST ARROW WITH HOOK
    0x2924: [662,156,926,55,871],      // NORTH EAST ARROW WITH HOOK
    0x2925: [662,156,926,55,871],      // SOUTH EAST ARROW WITH HOOK
    0x2926: [662,156,926,55,871],      // SOUTH WEST ARROW WITH HOOK
    0x2927: [662,156,926,55,873],      // NORTH WEST ARROW AND NORTH EAST ARROW
    0x2928: [662,156,926,53,871],      // NORTH EAST ARROW AND SOUTH EAST ARROW
    0x2929: [662,156,926,53,871],      // SOUTH EAST ARROW AND SOUTH WEST ARROW
    0x292A: [662,156,926,55,873],      // SOUTH WEST ARROW AND NORTH WEST ARROW
    0x292B: [662,156,926,55,871],      // RISING DIAGONAL CROSSING FALLING DIAGONAL
    0x292C: [662,156,926,55,871],      // FALLING DIAGONAL CROSSING RISING DIAGONAL
    0x292D: [662,156,926,55,871],      // SOUTH EAST ARROW CROSSING NORTH EAST ARROW
    0x292E: [662,156,926,55,871],      // NORTH EAST ARROW CROSSING SOUTH EAST ARROW
    0x292F: [662,156,926,55,871],      // FALLING DIAGONAL CROSSING NORTH EAST ARROW
    0x2930: [662,154,926,55,873],      // RISING DIAGONAL CROSSING SOUTH EAST ARROW
    0x2931: [662,156,926,54,870],      // NORTH EAST ARROW CROSSING NORTH WEST ARROW
    0x2932: [662,156,926,55,871],      // NORTH WEST ARROW CROSSING NORTH EAST ARROW
    0x2933: [449,-57,926,55,871],      // WAVE ARROW POINTING DIRECTLY RIGHT
    0x2934: [562,0,926,141,797],       // ARROW POINTING RIGHTWARDS THEN CURVING UPWARDS
    0x2935: [562,0,926,141,797],       // ARROW POINTING RIGHTWARDS THEN CURVING DOWNWARDS
    0x2936: [493,163,784,87,649],      // ARROW POINTING DOWNWARDS THEN CURVING LEFTWARDS
    0x2937: [493,163,784,135,697],     // ARROW POINTING DOWNWARDS THEN CURVING RIGHTWARDS
    0x2938: [657,153,511,70,415],      // RIGHT-SIDE ARC CLOCKWISE ARROW
    0x2939: [657,153,511,96,441],      // LEFT-SIDE ARC ANTICLOCKWISE ARROW
    0x293A: [423,-78,926,69,866],      // TOP ARC ANTICLOCKWISE ARROW
    0x293B: [423,-78,926,60,857],      // BOTTOM ARC ANTICLOCKWISE ARROW
    0x293C: [423,-64,926,59,856],      // TOP ARC CLOCKWISE ARROW WITH MINUS
    0x293D: [423,29,926,69,866],       // TOP ARC ANTICLOCKWISE ARROW WITH PLUS
    0x293E: [563,116,926,69,856],      // LOWER RIGHT SEMICIRCULAR CLOCKWISE ARROW
    0x293F: [563,116,926,69,856],      // LOWER LEFT SEMICIRCULAR ANTICLOCKWISE ARROW
    0x2940: [788,116,926,92,834],      // ANTICLOCKWISE CLOSED CIRCLE ARROW
    0x2941: [788,116,926,92,834],      // CLOCKWISE CLOSED CIRCLE ARROW
    0x2942: [598,92,926,55,871],       // RIGHTWARDS ARROW ABOVE SHORT LEFTWARDS ARROW
    0x2943: [598,92,926,55,871],       // LEFTWARDS ARROW ABOVE SHORT RIGHTWARDS ARROW
    0x2944: [598,92,926,55,871],       // SHORT RIGHTWARDS ARROW ABOVE LEFTWARDS ARROW
    0x2945: [449,69,926,55,871],       // RIGHTWARDS ARROW WITH PLUS BELOW
    0x2946: [449,69,926,55,871],       // LEFTWARDS ARROW WITH PLUS BELOW
    0x2947: [449,-57,926,55,871],      // RIGHTWARDS ARROW THROUGH X
    0x2948: [449,-57,926,38,888],      // LEFT RIGHT ARROW THROUGH SMALL CIRCLE
    0x2949: [662,154,511,60,451],      // UPWARDS TWO-HEADED ARROW FROM SMALL CIRCLE
    0x294A: [439,-67,926,38,888],      // LEFT BARB UP RIGHT BARB DOWN HARPOON
    0x294B: [439,-67,926,38,888],      // LEFT BARB DOWN RIGHT BARB UP HARPOON
    0x294C: [662,156,511,69,441],      // UP BARB RIGHT DOWN BARB LEFT HARPOON
    0x294D: [662,156,511,69,441],      // UP BARB LEFT DOWN BARB RIGHT HARPOON
    0x294E: [439,-220,926,38,888],     // LEFT BARB UP RIGHT BARB UP HARPOON
    0x294F: [662,156,511,222,441],     // UP BARB RIGHT DOWN BARB RIGHT HARPOON
    0x2950: [286,-67,926,38,888],      // LEFT BARB DOWN RIGHT BARB DOWN HARPOON
    0x2951: [662,156,511,69,288],      // UP BARB LEFT DOWN BARB LEFT HARPOON
    0x2952: [448,-58,926,55,871],      // LEFTWARDS HARPOON WITH BARB UP TO BAR
    0x2953: [448,-58,926,55,871],      // RIGHTWARDS HARPOON WITH BARB UP TO BAR
    0x2954: [662,156,511,60,451],      // UPWARDS HARPOON WITH BARB RIGHT TO BAR
    0x2955: [662,156,511,60,451],      // DOWNWARDS HARPOON WITH BARB RIGHT TO BAR
    0x2956: [448,-58,926,55,871],      // LEFTWARDS HARPOON WITH BARB DOWN TO BAR
    0x2957: [448,-58,926,55,871],      // RIGHTWARDS HARPOON WITH BARB DOWN TO BAR
    0x2958: [662,156,511,60,451],      // UPWARDS HARPOON WITH BARB LEFT TO BAR
    0x2959: [662,156,511,60,451],      // DOWNWARDS HARPOON WITH BARB LEFT TO BAR
    0x295A: [448,-58,926,55,871],      // LEFTWARDS HARPOON WITH BARB UP FROM BAR
    0x295B: [448,-58,926,55,871],      // RIGHTWARDS HARPOON WITH BARB UP FROM BAR
    0x295C: [662,156,511,60,451],      // UPWARDS HARPOON WITH BARB RIGHT FROM BAR
    0x295D: [662,156,511,60,451],      // DOWNWARDS HARPOON WITH BARB RIGHT FROM BAR
    0x295E: [448,-58,926,55,871],      // LEFTWARDS HARPOON WITH BARB DOWN FROM BAR
    0x295F: [448,-58,926,55,871],      // RIGHTWARDS HARPOON WITH BARB DOWN FROM BAR
    0x2960: [662,156,511,59,450],      // UPWARDS HARPOON WITH BARB LEFT FROM BAR
    0x2961: [662,156,511,59,450],      // DOWNWARDS HARPOON WITH BARB LEFT FROM BAR
    0x2962: [539,33,926,55,871],       // LEFTWARDS HARPOON WITH BARB UP ABOVE LEFTWARDS HARPOON WITH BARB DOWN
    0x2963: [662,156,685,57,629],      // UPWARDS HARPOON WITH BARB LEFT BESIDE UPWARDS HARPOON WITH BARB RIGHT
    0x2964: [539,33,926,55,871],       // RIGHTWARDS HARPOON WITH BARB UP ABOVE RIGHTWARDS HARPOON WITH BARB DOWN
    0x2965: [662,156,685,57,629],      // DOWNWARDS HARPOON WITH BARB LEFT BESIDE DOWNWARDS HARPOON WITH BARB RIGHT
    0x2966: [539,-120,926,55,871],     // LEFTWARDS HARPOON WITH BARB UP ABOVE RIGHTWARDS HARPOON WITH BARB UP
    0x2967: [386,33,926,55,871],       // LEFTWARDS HARPOON WITH BARB DOWN ABOVE RIGHTWARDS HARPOON WITH BARB DOWN
    0x2968: [539,-120,926,55,871],     // RIGHTWARDS HARPOON WITH BARB UP ABOVE LEFTWARDS HARPOON WITH BARB UP
    0x2969: [386,33,926,55,871],       // RIGHTWARDS HARPOON WITH BARB DOWN ABOVE LEFTWARDS HARPOON WITH BARB DOWN
    0x296A: [539,-120,926,55,871],     // LEFTWARDS HARPOON WITH BARB UP ABOVE LONG DASH
    0x296B: [386,33,926,55,871],       // LEFTWARDS HARPOON WITH BARB DOWN BELOW LONG DASH
    0x296C: [539,-120,926,55,871],     // RIGHTWARDS HARPOON WITH BARB UP ABOVE LONG DASH
    0x296D: [386,33,926,55,871],       // RIGHTWARDS HARPOON WITH BARB DOWN BELOW LONG DASH
    0x296E: [662,156,685,57,629],      // UPWARDS HARPOON WITH BARB LEFT BESIDE DOWNWARDS HARPOON WITH BARB RIGHT
    0x296F: [662,156,685,57,629],      // DOWNWARDS HARPOON WITH BARB LEFT BESIDE UPWARDS HARPOON WITH BARB RIGHT
    0x2970: [386,-120,926,55,871],     // RIGHT DOUBLE ARROW WITH ROUNDED HEAD
    0x2971: [565,-57,926,55,871],      // EQUALS SIGN ABOVE RIGHTWARDS ARROW
    0x2972: [508,-57,926,55,871],      // TILDE OPERATOR ABOVE RIGHTWARDS ARROW
    0x2973: [449,2,926,55,871],        // LEFTWARDS ARROW ABOVE TILDE OPERATOR
    0x2974: [449,2,926,55,871],        // RIGHTWARDS ARROW ABOVE TILDE OPERATOR
    0x2975: [449,141,926,55,871],      // RIGHTWARDS ARROW ABOVE ALMOST EQUAL TO
    0x2976: [607,283,685,64,621],      // LESS-THAN ABOVE LEFTWARDS ARROW
    0x2977: [532,26,926,45,871],       // LEFTWARDS ARROW THROUGH LESS-THAN
    0x2978: [608,282,685,64,621],      // GREATER-THAN ABOVE RIGHTWARDS ARROW
    0x2979: [627,262,685,64,621],      // SUBSET ABOVE RIGHTWARDS ARROW
    0x297A: [532,26,926,45,871],       // LEFTWARDS ARROW THROUGH SUBSET
    0x297B: [627,262,685,63,620],      // SUPERSET ABOVE LEFTWARDS ARROW
    0x297C: [511,5,926,135,791],       // LEFT FISH TAIL
    0x297D: [511,5,926,135,791],       // RIGHT FISH TAIL
    0x297E: [581,75,685,84,600],       // UP FISH TAIL
    0x297F: [581,75,685,84,600]        // DOWN FISH TAIL
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/SupplementalArrowsB.js");
