/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MathOperators.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold'],
  {
    0x2200: [676,0,599,5,594],         // FOR ALL
    0x2201: [785,29,539,63,476],       // COMPLEMENT
    0x2202: [686,10,559,44,559],       // PARTIAL DIFFERENTIAL
    0x2203: [676,0,599,76,523],        // THERE EXISTS
    0x2204: [803,127,599,76,523],      // THERE DOES NOT EXIST
    0x2205: [594,90,787,50,737],       // EMPTY SET
    0x2206: [676,0,681,23,658],        // INCREMENT
    0x2207: [676,0,681,23,658],        // NABLA
    0x2208: [547,13,750,82,668],       // ELEMENT OF
    0x2209: [680,146,750,82,668],      // stix-negated (vert) set membership, variant
    0x220A: [499,-35,500,60,440],      // SMALL ELEMENT OF
    0x220B: [547,13,750,82,668],       // CONTAINS AS MEMBER
    0x220C: [680,146,750,82,668],      // stix-negated (vert) contains
    0x220D: [499,-35,500,60,440],      // SMALL CONTAINS AS MEMBER
    0x220F: [763,259,1000,37,963],     // N-ARY PRODUCT
    0x2210: [763,259,982,28,954],      // N-ARY COPRODUCT
    0x2211: [763,259,914,40,873],      // N-ARY SUMMATION
    0x2212: [297,-209,750,66,685],     // MINUS SIGN
    0x2213: [657,12,770,65,685],       // MINUS-OR-PLUS SIGN
    0x2214: [793,57,750,65,685],       // DOT PLUS
    0x2215: [732,193,584,78,506],      // DIVISION SLASH
    0x2216: [411,-93,452,25,427],      // SET MINUS
    0x2217: [502,-34,585,82,503],      // ASTERISK OPERATOR
    0x2218: [409,-95,394,40,354],      // RING OPERATOR
    0x2219: [414,-91,493,85,408],      // BULLET OPERATOR
    0x221A: [946,259,965,130,1016],    // SQUARE ROOT
    0x221D: [450,0,772,80,692],        // PROPORTIONAL TO
    0x221E: [450,0,964,80,884],        // INFINITY
    0x221F: [584,0,685,50,634],        // RIGHT ANGLE
    0x2220: [569,0,792,50,708],        // ANGLE
    0x2221: [569,74,792,50,708],       // MEASURED ANGLE
    0x2222: [534,26,695,27,667],       // SPHERICAL ANGLE
    0x2223: [690,189,288,100,188],     // DIVIDES
    0x2224: [690,189,411,23,388],      // DOES NOT DIVIDE
    0x2225: [690,189,487,100,387],     // PARALLEL TO
    0x2226: [690,189,617,23,594],      // NOT PARALLEL TO
    0x2227: [536,28,640,52,588],       // LOGICAL AND
    0x2228: [536,28,640,52,588],       // LOGICAL OR
    0x2229: [541,33,650,66,584],       // stix-intersection, serifs
    0x222A: [541,33,650,66,584],       // stix-union, serifs
    0x222B: [824,320,553,32,733],      // INTEGRAL
    0x222C: [824,320,863,32,1043],     // DOUBLE INTEGRAL
    0x222D: [824,320,1174,32,1354],    // TRIPLE INTEGRAL
    0x222E: [824,320,591,30,731],      // CONTOUR INTEGRAL
    0x222F: [824,320,903,32,1043],     // SURFACE INTEGRAL
    0x2230: [824,320,1214,32,1354],    // VOLUME INTEGRAL
    0x2231: [824,320,593,32,733],      // CLOCKWISE INTEGRAL
    0x2232: [824,320,593,32,733],      // CLOCKWISE CONTOUR INTEGRAL
    0x2233: [824,320,593,32,733],      // ANTICLOCKWISE CONTOUR INTEGRAL
    0x2234: [575,41,750,66,685],       // THEREFORE
    0x2235: [575,41,750,66,685],       // BECAUSE
    0x2236: [575,41,554,190,364],      // RATIO
    0x2237: [575,41,750,68,683],       // PROPORTION
    0x2238: [543,-209,750,66,685],     // DOT MINUS
    0x2239: [543,37,750,66,686],       // EXCESS
    0x223A: [575,41,750,66,685],       // GEOMETRIC PROPORTION
    0x223B: [565,59,750,67,682],       // HOMOTHETIC
    0x223C: [374,-132,750,67,682],     // TILDE OPERATOR
    0x223D: [374,-132,750,67,682],     // REVERSED TILDE
    0x223E: [419,-85,750,68,683],      // stix-most positive
    0x223F: [484,-67,750,66,684],      // stix-reverse sine wave
    0x2240: [575,40,348,53,295],       // WREATH PRODUCT
    0x2241: [444,-62,750,67,682],      // stix-not, vert, similar
    0x2242: [463,-45,750,68,683],      // MINUS TILDE
    0x2243: [463,-45,750,68,683],      // ASYMPTOTICALLY EQUAL TO
    0x2244: [530,54,750,68,683],       // stix-not (vert) similar or equal
    0x2245: [568,60,750,68,683],       // APPROXIMATELY EQUAL TO
    0x2246: [568,150,750,68,683],      // APPROXIMATELY BUT NOT ACTUALLY EQUAL TO
    0x2247: [642,152,750,68,683],      // stix-not (vert) similar over two-line equals
    0x2248: [508,-26,750,68,683],      // ALMOST EQUAL TO
    0x2249: [583,48,750,68,683],       // stix-not, vert, approximate
    0x224A: [568,75,750,68,683],       // ALMOST EQUAL OR EQUAL TO
    0x224B: [613,109,750,68,683],      // TRIPLE TILDE
    0x224C: [568,60,750,68,683],       // stix-all equal to (lazy S over equals) (formerly 224C; that shape changed)
    0x224D: [518,13,750,68,683],       // EQUIVALENT TO
    0x224E: [484,-22,750,68,683],      // GEOMETRICALLY EQUIVALENT TO
    0x224F: [484,-107,750,68,683],     // DIFFERENCE BETWEEN
    0x2250: [667,-107,750,68,682],     // APPROACHES THE LIMIT
    0x2251: [667,161,750,68,682],      // GEOMETRICALLY EQUAL TO
    0x2252: [667,161,750,68,682],      // APPROXIMATELY EQUAL TO OR THE IMAGE OF
    0x2253: [667,161,750,68,682],      // IMAGE OF OR APPROXIMATELY EQUAL TO
    0x2254: [483,-50,932,68,864],      // COLON EQUALS
    0x2255: [483,-50,932,68,864],      // EQUALS COLON
    0x2256: [471,-63,750,68,682],      // RING IN EQUAL TO
    0x2257: [809,-107,750,68,682],     // RING EQUAL TO
    0x2258: [761,-107,750,68,682],     // CORRESPONDS TO
    0x2259: [836,-107,750,68,682],     // ESTIMATES
    0x225A: [836,-107,750,68,682],     // EQUIANGULAR TO
    0x225B: [841,-107,750,68,682],     // STAR EQUALS
    0x225C: [844,-107,750,68,682],     // DELTA EQUAL TO
    0x225D: [838,-107,750,55,735],     // EQUAL TO BY DEFINITION
    0x225E: [721,-107,750,68,682],     // MEASURED BY
    0x225F: [880,-107,750,68,682],     // QUESTIONED EQUAL TO
    0x2260: [662,156,750,68,682],      // stix-not (vert) equals
    0x2261: [507,-27,750,68,682],      // IDENTICAL TO
    0x2262: [688,156,750,68,682],      // stix-not (vert) three-line equals
    0x2263: [592,57,750,68,682],       // STRICTLY EQUIVALENT TO
    0x2264: [627,121,750,80,670],      // LESS-THAN OR EQUAL TO
    0x2265: [627,120,750,80,670],      // GREATER-THAN OR EQUAL TO
    0x2266: [729,222,750,80,670],      // LESS-THAN OVER EQUAL TO
    0x2267: [729,222,750,80,670],      // GREATER-THAN OVER EQUAL TO
    0x2268: [729,294,750,80,670],      // stix-less, vert, not double equals
    0x2269: [729,294,750,80,670],      // stix-gt, vert, not double equals
    0x226A: [534,24,1000,38,961],      // MUCH LESS-THAN
    0x226B: [534,24,1000,38,961],      // MUCH GREATER-THAN
    0x226C: [732,193,417,46,371],      // BETWEEN
    0x226D: [591,87,750,68,683],       // stix-not (vert) asymptotically equal to
    0x226E: [625,115,750,80,670],      // stix-not, vert, less-than
    0x226F: [625,115,750,80,670],      // stix-not, vert, greater-than
    0x2270: [717,235,750,80,670],      // stix-not, vert, less-than-or-equal
    0x2271: [717,235,750,80,670],      // stix-not, vert, greater-than-or-equal
    0x2272: [690,182,750,67,682],      // stix-less-than or (contour) similar
    0x2273: [690,182,750,67,682],      // stix-greater-than or (contour) similar
    0x2274: [780,282,750,67,682],      // stix-not, vert, less, similar
    0x2275: [780,282,750,67,682],      // stix-not, vert, greater, similar
    0x2276: [734,226,750,80,670],      // LESS-THAN OR GREATER-THAN
    0x2277: [734,226,750,80,670],      // GREATER-THAN OR LESS-THAN
    0x2278: [824,316,750,80,670],      // stix-not, vert, less, greater
    0x2279: [824,316,750,80,670],      // stix-not, vert, greater, less
    0x227A: [531,23,750,80,670],       // PRECEDES
    0x227B: [531,23,750,80,670],       // SUCCEEDS
    0x227C: [645,138,750,80,670],      // PRECEDES OR EQUAL TO
    0x227D: [645,138,750,80,670],      // SUCCEEDS OR EQUAL TO
    0x227E: [676,169,750,67,682],      // PRECEDES OR EQUIVALENT TO
    0x227F: [676,169,750,67,682],      // SUCCEEDS OR EQUIVALENT TO
    0x2280: [625,115,750,80,670],      // DOES NOT PRECEDE
    0x2281: [625,115,750,80,670],      // stix-not (vert) succeeds
    0x2282: [547,13,750,82,668],       // SUBSET OF
    0x2283: [547,13,750,82,668],       // SUPERSET OF
    0x2284: [680,146,750,82,668],      // stix-not subset [vertical negation]
    0x2285: [680,146,750,82,668],      // stix-not superset [vertical negation]
    0x2286: [647,101,750,82,668],      // SUBSET OF OR EQUAL TO
    0x2287: [647,101,750,82,668],      // SUPERSET OF OR EQUAL TO
    0x2288: [747,201,750,82,668],      // stix-/nsubseteq N: not (vert) subset, equals
    0x2289: [747,201,750,82,668],      // stix-/nsupseteq N: not (vert) superset, equals
    0x228A: [734,200,750,82,668],      // stix-subset, not equals, variant
    0x228B: [734,200,750,82,668],      // stix-superset, not equals, variant
    0x228C: [541,33,650,66,584],       // MULTISET
    0x228D: [541,33,650,66,584],       // MULTISET MULTIPLICATION
    0x228E: [541,33,650,66,584],       // MULTISET UNION
    0x228F: [532,27,750,87,663],       // SQUARE IMAGE OF
    0x2290: [532,27,750,87,663],       // SQUARE ORIGINAL OF
    0x2291: [644,93,750,87,663],       // SQUARE IMAGE OF OR EQUAL TO
    0x2292: [644,93,750,87,663],       // SQUARE ORIGINAL OF OR EQUAL TO
    0x2293: [541,33,650,66,584],       // stix-square intersection, serifs
    0x2294: [541,33,650,66,584],       // stix-square union, serifs
    0x2295: [634,130,864,50,814],      // stix-circled plus (with rim)
    0x2296: [634,130,864,50,814],      // CIRCLED MINUS
    0x2297: [634,130,864,50,814],      // stix-circled times (with rim)
    0x2298: [634,130,864,50,814],      // CIRCLED DIVISION SLASH
    0x2299: [594,90,784,50,734],       // CIRCLED DOT OPERATOR
    0x229A: [634,130,842,39,803],      // CIRCLED RING OPERATOR
    0x229B: [634,130,864,50,814],      // CIRCLED ASTERISK OPERATOR
    0x229C: [634,130,864,50,814],      // stix-two horizontal bars in circle
    0x229D: [634,130,864,50,814],      // CIRCLED DASH
    0x229E: [661,158,910,45,865],      // SQUARED PLUS
    0x229F: [661,158,910,45,865],      // SQUARED MINUS
    0x22A0: [661,158,910,45,865],      // SQUARED TIMES
    0x22A1: [661,158,910,45,865],      // SQUARED DOT OPERATOR
    0x22A2: [676,0,750,91,659],        // RIGHT TACK
    0x22A3: [676,0,750,91,659],        // LEFT TACK
    0x22A4: [676,0,750,91,659],        // DOWN TACK
    0x22A5: [676,0,750,91,659],        // UP TACK
    0x22A6: [676,0,555,91,464],        // ASSERTION
    0x22A7: [676,0,555,91,464],        // MODELS
    0x22A8: [676,0,750,91,659],        // TRUE
    0x22A9: [676,0,972,91,882],        // FORCES
    0x22AA: [676,0,944,91,856],        // TRIPLE VERTICAL BAR RIGHT TURNSTILE
    0x22AB: [676,0,944,91,856],        // DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
    0x22AC: [676,0,913,21,822],        // DOES NOT PROVE
    0x22AD: [676,0,912,21,822],        // NOT TRUE
    0x22AE: [676,0,1096,21,1024],      // DOES NOT FORCE
    0x22AF: [676,0,1104,21,1016],      // NEGATED DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
    0x22B0: [543,38,750,98,652],       // PRECEDES UNDER RELATION
    0x22B1: [543,38,750,98,652],       // SUCCEEDS UNDER RELATION
    0x22B2: [534,24,750,81,669],       // NORMAL SUBGROUP OF
    0x22B3: [534,24,750,81,669],       // CONTAINS AS NORMAL SUBGROUP
    0x22B4: [621,113,750,81,669],      // NORMAL SUBGROUP OF OR EQUAL TO
    0x22B5: [621,113,750,81,669],      // CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
    0x22B6: [436,-96,1216,50,1166],    // ORIGINAL OF
    0x22B7: [436,-96,1216,50,1166],    // IMAGE OF
    0x22B8: [436,-96,884,50,834],      // MULTIMAP
    0x22B9: [563,57,750,65,685],       // HERMITIAN CONJUGATE MATRIX
    0x22BA: [461,216,498,74,424],      // INTERCALATE
    0x22BB: [536,189,640,52,588],      // XOR
    0x22BC: [697,28,640,52,588],       // NAND
    0x22BD: [697,28,640,52,588],       // NOR
    0x22BE: [630,0,750,60,690],        // RIGHT ANGLE WITH ARC
    0x22BF: [662,158,910,45,865],      // RIGHT TRIANGLE
    0x22C0: [763,259,977,54,923],      // N-ARY LOGICAL AND
    0x22C1: [763,259,977,54,923],      // N-ARY LOGICAL OR
    0x22C2: [768,264,961,94,867],      // N-ARY INTERSECTION
    0x22C3: [768,264,961,94,867],      // N-ARY UNION
    0x22C4: [515,-17,584,43,541],      // DIAMOND OPERATOR
    0x22C7: [595,63,750,66,685],       // DIVISION TIMES
    0x22C8: [604,72,870,67,803],       // BOWTIE
    0x22C9: [604,72,870,57,817],       // LEFT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22CA: [604,72,870,53,813],       // RIGHT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22CB: [604,72,870,97,773],       // LEFT SEMIDIRECT PRODUCT
    0x22CC: [604,72,870,97,773],       // RIGHT SEMIDIRECT PRODUCT
    0x22CD: [463,-45,750,68,683],      // REVERSED TILDE EQUALS
    0x22CE: [536,28,640,41,599],       // CURLY LOGICAL OR
    0x22CF: [536,28,640,41,599],       // CURLY LOGICAL AND
    0x22D0: [600,67,750,63,687],       // DOUBLE SUBSET
    0x22D1: [600,67,750,63,687],       // DOUBLE SUPERSET
    0x22D2: [541,33,750,65,685],       // DOUBLE INTERSECTION
    0x22D3: [541,33,750,65,685],       // DOUBLE UNION
    0x22D4: [643,33,650,66,584],       // PITCHFORK
    0x22D5: [690,189,685,48,637],      // EQUAL AND PARALLEL TO
    0x22D6: [534,24,750,80,670],       // LESS-THAN WITH DOT
    0x22D7: [534,24,750,80,670],       // GREATER-THAN WITH DOT
    0x22D8: [534,24,1336,40,1296],     // VERY MUCH LESS-THAN
    0x22D9: [534,24,1336,40,1296],     // VERY MUCH GREATER-THAN
    0x22DA: [916,408,750,80,670],      // stix-less, equal, slanted, greater
    0x22DB: [916,408,750,80,670],      // stix-greater, equal, slanted, less
    0x22DC: [627,120,750,80,670],      // EQUAL TO OR LESS-THAN
    0x22DD: [627,120,750,80,670],      // EQUAL TO OR GREATER-THAN
    0x22DE: [645,138,750,80,670],      // EQUAL TO OR PRECEDES
    0x22DF: [645,138,750,80,670],      // EQUAL TO OR SUCCEEDS
    0x22E0: [735,199,750,80,670],      // stix-not (vert) precedes or contour equals
    0x22E1: [735,199,750,80,670],      // stix-not (vert) succeeds or contour equals
    0x22E2: [792,241,750,87,663],      // NOT SQUARE IMAGE OF OR EQUAL TO
    0x22E3: [792,241,750,87,663],      // NOT SQUARE ORIGINAL OF OR EQUAL TO
    0x22E6: [690,200,750,67,682],      // LESS-THAN BUT NOT EQUIVALENT TO
    0x22E7: [690,200,750,67,682],      // GREATER-THAN BUT NOT EQUIVALENT TO
    0x22E8: [676,187,750,67,682],      // PRECEDES BUT NOT EQUIVALENT TO
    0x22E9: [676,187,750,67,682],      // SUCCEEDS BUT NOT EQUIVALENT TO
    0x22EA: [625,115,750,81,669],      // NOT NORMAL SUBGROUP OF
    0x22EB: [625,115,750,81,669],      // DOES NOT CONTAIN AS NORMAL SUBGROUP
    0x22EC: [711,228,750,81,669],      // stix-not, vert, left triangle, equals
    0x22ED: [711,228,750,81,669],      // stix-not, vert, right triangle, equals
    0x22EE: [678,174,584,205,375],     // VERTICAL ELLIPSIS
    0x22EF: [351,-181,977,62,914],     // MIDLINE HORIZONTAL ELLIPSIS
    0x22F0: [579,75,977,162,815],      // UP RIGHT DIAGONAL ELLIPSIS
    0x22F1: [579,75,977,162,815],      // DOWN RIGHT DIAGONAL ELLIPSIS
    0x22F6: [735,13,750,82,668],       // ELEMENT OF WITH OVERBAR
    0x22FD: [735,13,750,82,668]        // CONTAINS WITH OVERBAR
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Bold/MathOperators.js");
