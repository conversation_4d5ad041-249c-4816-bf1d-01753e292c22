/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscSymbols.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2605: [655,66,870,60,810],       // BLACK STAR
    0x2606: [655,66,870,60,810],       // WHITE STAR
    0x2609: [583,79,762,50,712],       // SUN
    0x260C: [634,131,581,54,553],      // CONJUNCTION
    0x260E: [676,0,1000,32,967],       // BLACK TELEPHONE
    0x2612: [662,158,910,45,865],      // BALLOT BOX WITH X
    0x2621: [630,35,619,70,549],       // CAUTION SIGN
    0x2639: [728,82,1150,170,980],     // WHITE FROWNING FACE
    0x263A: [728,82,1150,170,980],     // WHITE SMILING FACE
    0x263B: [728,82,1150,170,980],     // BLACK SMILING FACE
    0x263C: [623,122,837,46,791],      // WHITE SUN WITH RAYS
    0x263D: [728,82,641,40,601],       // FIRST QUARTER MOON
    0x263E: [728,82,641,40,601],       // LAST QUARTER MOON
    0x263F: [702,198,603,65,538],      // MERCURY
    0x2640: [638,135,603,65,538],      // FEMALE SIGN
    0x2641: [638,135,603,65,538],      // EARTH
    0x2642: [634,131,660,54,620],      // MALE SIGN
    0x2643: [732,176,970,66,904],      // JUPITER
    0x2644: [793,140,970,63,866],      // SATURN
    0x2646: [760,110,840,60,780],      // NEPTUNE
    0x2647: [730,110,632,76,576],      // PLUTO
    0x2648: [760,110,964,25,939],      // ARIES
    0x2649: [644,139,781,43,738],      // TAURUS
    0x2664: [609,99,685,34,651],       // WHITE SPADE SUIT
    0x2665: [603,105,685,34,651],      // BLACK HEART SUIT
    0x2666: [609,105,685,41,643],      // BLACK DIAMOND SUIT
    0x2667: [603,99,685,34,651],       // WHITE CLUB SUIT
    0x2669: [714,125,390,45,345],      // QUARTER NOTE
    0x266A: [714,125,560,50,510],      // EIGHTH NOTE
    0x266B: [842,125,840,40,721],      // BEAMED EIGHTH NOTES
    0x267E: [775,271,1186,70,1116],    // PERMANENT PAPER SIGN
    0x2680: [669,23,1032,170,862],     // DIE FACE-1
    0x2681: [669,23,1032,170,862],     // DIE FACE-2
    0x2682: [669,23,1032,170,862],     // DIE FACE-3
    0x2683: [669,23,1032,170,862],     // DIE FACE-4
    0x2684: [669,23,1032,170,862],     // DIE FACE-5
    0x2685: [669,23,1032,170,862],     // DIE FACE-6
    0x2686: [687,42,1032,152,881],     // WHITE CIRCLE WITH DOT RIGHT
    0x2687: [687,42,1032,152,881],     // WHITE CIRCLE WITH TWO DOTS
    0x2688: [687,42,1032,152,881],     // BLACK CIRCLE WITH WHITE DOT RIGHT
    0x2689: [687,42,1032,152,881],     // BLACK CIRCLE WITH TWO WHITE DOTS
    0x26A0: [1023,155,1510,25,1485],   // WARNING SIGN
    0x26A5: [784,281,660,54,620],      // MALE AND FEMALE SIGN
    0x26AA: [583,79,762,50,712],       // MEDIUM WHITE CIRCLE
    0x26AB: [583,79,762,50,712],       // MEDIUM BLACK CIRCLE
    0x26AC: [487,-14,565,46,519],      // MEDIUM SMALL WHITE CIRCLE
    0x26B2: [638,135,603,65,538],      // NEUTER
    0x26E2: [773,80,700,94,606]        // ??
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MiscSymbols.js");
