/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/BoldItalic/PrivateUse.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXNonUnicode-bold-italic'],
  {
    0xE09C: [775,235,776,40,765],      // stix-capital A italic double-slashed
    0xE09D: [775,235,759,44,779],      // stix-capital E italic double-slashed
    0xE09E: [775,235,658,44,771],      // stix-capital F italic double-slashed
    0xE0B3: [703,205,556,-188,517],    // stix-small fj ligature
    0xE1F6: [688,13,500,89,578],       // stix-Mathematical sans-serif bold italic digit 0
    0xE1F7: [688,0,500,204,505],       // stix-Mathematical sans-serif bold italic digit 1
    0xE1F8: [688,0,500,20,581],        // stix-Mathematical sans-serif bold italic digit 2
    0xE1F9: [688,13,500,32,586],       // stix-Mathematical sans-serif bold italic digit 3
    0xE1FA: [688,0,500,55,583],        // stix-Mathematical sans-serif bold italic digit 4
    0xE1FB: [676,13,500,27,651],       // stix-Mathematical sans-serif bold italic digit 5
    0xE1FC: [688,13,500,80,638],       // stix-Mathematical sans-serif bold italic digit 6
    0xE1FD: [676,0,500,120,639],       // stix-Mathematical sans-serif bold italic digit 7
    0xE1FE: [688,13,500,63,594],       // stix-Mathematical sans-serif bold italic digit 8
    0xE1FF: [688,13,500,28,588],       // stix-Mathematical sans-serif bold italic digit 9
    0xE200: [669,0,733,7,667],         // stix-mathematical bold oblique double-struck capital A
    0xE201: [669,0,729,18,714],        // stix-mathematical bold oblique double-struck capital B
    0xE203: [669,0,680,18,703],        // stix-mathematical bold oblique double-struck capital E
    0xE204: [669,0,474,18,703],        // stix-mathematical bold oblique double-struck capital F
    0xE205: [685,14,718,35,708],       // stix-mathematical bold oblique double-struck capital G
    0xE206: [669,0,382,22,411],        // stix-mathematical bold oblique double-struck capital I
    0xE207: [669,14,603,19,644],       // stix-mathematical bold oblique double-struck capital J
    0xE208: [669,0,766,18,766],        // stix-mathematical bold oblique double-struck capital K
    0xE209: [669,0,613,18,568],        // stix-mathematical bold oblique double-struck capital L
    0xE20A: [669,0,912,26,943],        // stix-mathematical bold oblique double-struck capital M
    0xE20B: [685,14,749,35,734],       // stix-mathematical bold oblique double-struck capital O
    0xE20C: [685,14,686,30,711],       // stix-mathematical bold oblique double-struck capital S
    0xE20D: [669,0,445,30,653],        // stix-mathematical bold oblique double-struck capital T
    0xE20E: [669,14,709,35,755],       // stix-mathematical bold oblique double-struck capital U
    0xE20F: [669,0,504,42,705],        // stix-mathematical bold oblique double-struck capital V
    0xE210: [669,0,891,81,991],        // stix-mathematical bold oblique double-struck capital W
    0xE211: [669,0,759,7,832],         // stix-mathematical bold oblique double-struck capital X
    0xE212: [669,0,462,10,714],        // stix-mathematical bold oblique double-struck capital Y
    0xE213: [462,13,634,45,589],       // stix-mathematical bold oblique double-struck small letter a
    0xE214: [699,13,661,34,619],       // stix-mathematical bold oblique double-struck small letter b
    0xE215: [462,13,571,45,545],       // stix-mathematical bold oblique double-struck small letter c
    0xE217: [462,13,575,45,540],       // ??
    0xE218: [699,0,438,45,618],        // stix-mathematical bold oblique double-struck small letter f
    0xE219: [462,205,666,28,642],      // stix-mathematical bold oblique double-struck small letter g
    0xE21A: [699,0,661,34,616],        // stix-mathematical bold oblique double-struck small letter h
    0xE21D: [699,0,641,34,616],        // stix-mathematical bold oblique double-struck small letter k
    0xE21E: [699,0,372,34,413],        // stix-mathematical bold oblique double-struck small letter l
    0xE21F: [462,0,942,35,897],        // stix-mathematical bold oblique double-struck small letter m
    0xE220: [462,0,661,34,616],        // stix-mathematical bold oblique double-struck small letter n
    0xE221: [462,13,586,45,551],       // stix-mathematical bold oblique double-struck small letter o
    0xE222: [462,205,680,3,645],       // stix-mathematical bold oblique double-struck small letter p
    0xE223: [462,205,662,45,630],      // stix-mathematical bold oblique double-struck small letter q
    0xE224: [462,0,403,33,538],        // stix-mathematical bold oblique double-struck small letter r
    0xE225: [462,13,533,33,519],       // stix-mathematical bold oblique double-struck small letter s
    0xE226: [676,14,403,22,422],       // stix-mathematical bold oblique double-struck small letter t
    0xE227: [449,13,661,45,627],       // stix-mathematical bold oblique double-struck small letter u
    0xE228: [449,0,477,32,534],        // stix-mathematical bold oblique double-struck small letter v
    0xE229: [449,0,733,55,763],        // stix-mathematical bold oblique double-struck small letter w
    0xE22A: [449,0,562,-12,589],       // stix-mathematical bold oblique double-struck small letter x
    0xE22B: [449,205,584,-9,643],      // stix-mathematical bold oblique double-struck small letter y
    0xE22C: [449,0,619,35,594],        // stix-mathematical bold oblique double-struck small letter z
    0xE247: [711,47,871,38,834],       // stix-mathematical bold calligraphic capital A
    0xE248: [703,10,755,33,740],       // stix-mathematical bold calligraphic capital B
    0xE249: [704,12,667,36,669],       // stix-mathematical bold calligraphic capital C
    0xE24A: [696,0,802,30,808],        // stix-mathematical bold calligraphic capital D
    0xE24B: [704,8,609,41,626],        // stix-mathematical bold calligraphic capital E
    0xE24C: [696,0,645,34,738],        // stix-mathematical bold calligraphic capital F
    0xE24D: [704,144,615,43,615],      // stix-mathematical bold calligraphic capital G
    0xE24E: [696,24,849,22,858],       // stix-mathematical bold calligraphic capital H
    0xE24F: [696,0,621,36,623],        // stix-mathematical bold calligraphic capital I
    0xE250: [695,116,645,36,811],      // stix-mathematical bold calligraphic capital J
    0xE251: [703,14,856,38,820],       // stix-mathematical bold calligraphic capital K
    0xE252: [704,8,726,38,688],        // stix-mathematical bold calligraphic capital L
    0xE253: [705,45,1186,38,1146],     // stix-mathematical bold calligraphic capital M
    0xE254: [835,39,997,36,1098],      // stix-mathematical bold calligraphic capital N
    0xE255: [707,10,772,43,782],       // stix-mathematical bold calligraphic capital O
    0xE256: [696,0,645,36,731],        // stix-mathematical bold calligraphic capital Q
    0xE257: [704,145,778,43,737],      // stix-mathematical bold calligraphic capital P
    0xE258: [697,13,869,36,831],       // stix-mathematical bold calligraphic capital R
    0xE259: [705,7,667,36,699],        // stix-mathematical bold calligraphic capital S
    0xE25A: [783,0,547,33,747],        // stix-mathematical bold calligraphic capital T
    0xE25B: [700,14,787,33,936],       // stix-mathematical bold calligraphic capital U
    0xE25C: [711,31,652,36,706],       // stix-mathematical bold calligraphic capital V
    0xE25D: [711,34,956,36,1010],      // stix-mathematical bold calligraphic capital W
    0xE25E: [710,14,720,36,781],       // stix-mathematical bold calligraphic capital X
    0xE25F: [711,144,720,36,773],      // stix-mathematical bold calligraphic capital Y
    0xE260: [702,98,778,36,744],       // stix-mathematical bold calligraphic capital Z
    0xE264: [473,10,600,47,554],       // stix-old style digit 0
    0xE268: [473,0,600,95,450],        // stix-old style digit 1
    0xE26C: [473,0,600,54,531],        // stix-old style digit 2
    0xE270: [463,217,600,31,547],      // stix-old style digit 3
    0xE274: [450,217,600,30,564],      // stix-old style digit 4
    0xE278: [450,218,600,25,561],      // stix-old style digit 5
    0xE27C: [670,10,600,55,545],       // stix-old style digit 6
    0xE280: [450,217,600,24,582],      // stix-old style digit 7
    0xE284: [670,10,600,41,560],       // stix-old style digit 8
    0xE288: [463,217,600,49,539],      // stix-old style digit 9
    0xE295: [775,235,776,40,739],      // stix-capital A bold italic slashed
    0xE297: [775,235,762,44,747],      // stix-capital B bold italic slashed
    0xE299: [775,235,711,57,753],      // stix-capital C bold italic slashed
    0xE29B: [775,235,870,44,840],      // stix-capital D bold italic slashed
    0xE29D: [775,235,759,44,779],      // stix-capital E bold italic slashed
    0xE29F: [775,235,658,44,771],      // stix-capital F bold italic slashed
    0xE2A1: [775,235,789,57,787],      // stix-capital G bold italic slashed
    0xE2A3: [775,235,915,44,940],      // stix-capital H bold italic slashed
    0xE2A5: [775,235,502,46,525],      // stix-capital I bold italic slashed
    0xE2A7: [775,235,648,68,688],      // stix-capital J bold italic slashed
    0xE2A9: [775,207,814,44,838],      // stix-capital K bold italic slashed
    0xE2AB: [775,235,764,44,718],      // stix-capital L bold italic slashed
    0xE2AD: [775,235,1044,44,1069],    // stix-capital M bold italic slashed
    0xE2AF: [775,235,857,44,882],      // stix-capital N bold italic slashed
    0xE2B1: [775,235,802,57,777],      // stix-capital O bold italic slashed
    0xE2B3: [775,207,626,19,790],      // stix-capital P bold italic slashed
    0xE2B5: [775,245,834,57,777],      // stix-capital Q bold italic slashed
    0xE2B7: [775,235,783,44,757],      // stix-capital R bold italic slashed
    0xE2B9: [775,235,589,57,621],      // stix-capital S bold italic slashed
    0xE2BB: [775,235,562,30,696],      // stix-capital T bold italic slashed
    0xE2BD: [775,235,745,74,813],      // stix-capital U bold italic slashed
    0xE2BF: [775,235,597,66,774],      // stix-capital V bold italic slashed
    0xE2C1: [775,235,980,66,1131],     // stix-capital W bold italic slashed
    0xE2C3: [775,235,803,34,819],      // stix-capital X bold italic slashed
    0xE2C5: [775,235,569,25,706],      // stix-capital Y bold italic slashed
    0xE2C7: [775,235,720,42,701],      // stix-capital Z bold italic slashed
    0xE2C9: [775,235,630,46,595],      // stix-lowercase a bold italic slashed
    0xE2CB: [775,235,585,57,564],      // stix-lowercase b bold italic slashed
    0xE2CD: [775,235,511,33,506],      // stix-lowercase c bold italic slashed
    0xE2CF: [775,235,646,31,638],      // stix-lowercase d bold italic slashed
    0xE2D1: [775,235,512,44,516],      // stix-lowercase e bold italic slashed
    0xE2D3: [775,235,654,-29,762],     // stix-lowercase f bold italic slashed
    0xE2D5: [775,235,601,24,599],      // stix-lowercase g bold italic slashed
    0xE2D7: [775,235,611,35,577],      // stix-lowercase h bold italic slashed
    0xE2D9: [775,207,373,34,488],      // stix-lowercase i bold italic slashed
    0xE2DB: [775,235,600,-29,763],     // stix-lowercase j bold italic slashed
    0xE2DD: [775,235,622,35,660],      // stix-lowercase k bold italic slashed
    0xE2DF: [775,207,381,30,484],      // stix-lowercase l bold italic slashed
    0xE2E1: [775,235,873,35,838],      // stix-lowercase m bold italic slashed
    0xE2E3: [775,235,611,35,581],      // stix-lowercase n bold italic slashed
    0xE2E5: [775,235,571,46,548],      // stix-lowercase o bold italic slashed
    0xE2E7: [775,235,636,-25,649],     // stix-lowercase p bold italic slashed
    0xE2E9: [775,207,580,46,568],      // stix-lowercase q bold italic slashed
    0xE2EB: [775,235,437,35,567],      // stix-lowercase r bold italic slashed
    0xE2ED: [775,235,512,42,515],      // stix-lowercase s bold italic slashed
    0xE2EF: [775,207,411,32,486],      // stix-lowercase t bold italic slashed
    0xE2F1: [775,235,632,60,597],      // stix-lowercase u bold italic slashed
    0xE2F3: [775,207,554,52,558],      // stix-lowercase v bold italic slashed
    0xE2F5: [775,207,814,17,799],      // stix-lowercase w bold italic slashed
    0xE2F7: [775,235,647,35,622],      // stix-lowercase x bold italic slashed
    0xE2F9: [775,207,599,20,640],      // stix-lowercase y bold italic slashed
    0xE2FB: [775,235,531,35,555],      // stix-lowercase z bold italic slashed
    0xE32D: [775,207,671,46,675],      // stix-MATHEMATICAL BOLD ITALIC SMALL ALPHA SLASHED
    0xE32F: [775,207,664,-65,706],     // stix-MATHEMATICAL BOLD ITALIC SMALL BETA SLASHED
    0xE331: [775,207,588,-100,671],    // stix-MATHEMATICAL BOLD ITALIC SMALL GAMMA SLASHED
    0xE333: [775,207,571,46,547],      // stix-MATHEMATICAL BOLD ITALIC SMALL DELTA SLASHED
    0xE335: [775,207,508,44,515],      // stix-MATHEMATICAL BOLD ITALIC SMALL EPSILON SLASHED
    0xE337: [775,207,505,-54,629],     // stix-MATHEMATICAL BOLD ITALIC SMALL ZETA SLASHED
    0xE339: [775,207,579,20,583],      // stix-MATHEMATICAL BOLD ITALIC SMALL ETA SLASHED
    0xE33B: [775,207,615,46,602],      // stix-MATHEMATICAL BOLD ITALIC SMALL THETA SLASHED
    0xE33D: [775,207,355,29,483],      // stix-MATHEMATICAL BOLD ITALIC SMALL IOTA SLASHED
    0xE33F: [775,207,594,35,656],      // stix-MATHEMATICAL BOLD ITALIC SMALL KAPPA SLASHED
    0xE341: [775,207,598,18,642],      // stix-MATHEMATICAL BOLD ITALIC SMALL LAMBDA SLASHED
    0xE343: [775,207,697,-34,737],     // stix-MATHEMATICAL BOLD ITALIC SMALL MU SLASHED
    0xE345: [775,207,571,35,584],      // stix-MATHEMATICAL BOLD ITALIC SMALL NU SLASHED
    0xE347: [775,207,504,-54,629],     // stix-MATHEMATICAL BOLD ITALIC SMALL XI SLASHED
    0xE349: [775,235,500,32,506],      // stix-MATHEMATICAL BOLD ITALIC SMALL OMICRON SLASHED
    0xE34B: [775,207,652,1,772],       // stix-MATHEMATICAL BOLD ITALIC SMALL PI SLASHED
    0xE34D: [775,207,636,27,652],      // stix-MATHEMATICAL BOLD ITALIC SMALL RHO SLASHED
    0xE34F: [775,207,504,23,514],      // stix-MATHEMATICAL BOLD ITALIC SMALL FINAL SIGMA SLASHED
    0xE351: [775,207,595,46,641],      // stix-MATHEMATICAL BOLD ITALIC SMALL SIGMA SLASHED
    0xE353: [775,207,474,20,521],      // stix-MATHEMATICAL BOLD ITALIC SMALL TAU SLASHED
    0xE355: [775,207,582,20,584],      // stix-small upsilon, Greek slashed
    0xE357: [775,207,726,1,772],       // stix-MATHEMATICAL BOLD ITALIC SMALL PHI SLASHED
    0xE359: [775,207,622,-41,730],     // stix-MATHEMATICAL BOLD ITALIC SMALL CHI SLASHED
    0xE35B: [775,207,720,37,808],      // stix-MATHEMATICAL BOLD ITALIC SMALL PSI SLASHED
    0xE35D: [775,207,782,24,795],      // stix-MATHEMATICAL BOLD ITALIC SMALL OMEGA SLASHED
    0xE35F: [775,207,608,20,681],      // stix-MATHEMATICAL BOLD ITALIC THETA SYMBOL SLASHED
    0xE361: [775,207,727,0,771],       // stix-MATHEMATICAL BOLD ITALIC PHI SYMBOL SLASHED
    0xE363: [775,207,925,6,978],       // stix-MATHEMATICAL BOLD ITALIC PI SYMBOL SLASHED
    0xE367: [775,235,475,-35,509],     // stix-small stigma, Greek slashed
    0xE36B: [775,235,525,-68,651],     // stix-small digamma, Greek slashed
    0xE36F: [775,235,485,16,466],      // stix-small koppa, Greek slashed
    0xE373: [775,235,530,12,731],      // stix-small sampi, Greek slashed
    0xE375: [775,235,569,-50,592],     // stix-MATHEMATICAL BOLD ITALIC KAPPA SYMBOL SLASHED
    0xE377: [775,207,571,46,547],      // stix-MATHEMATICAL BOLD ITALIC RHO SYMBOL SLASHED
    0xE379: [775,207,601,46,579],      // stix-MATHEMATICAL BOLD ITALIC PARTIAL DIFFERENTIAL SLASHED
    0xE37B: [775,207,525,46,543],      // stix-MATHEMATICAL BOLD ITALIC EPSILON SYMBOL SLASHED
    0xE37E: [775,235,792,-40,777],     // stix-capital C script slashed
    0xE380: [707,14,670,10,662],       // stix-small d italic with straight bar through it
    0xE382: [707,14,622,14,598],       // stix-small k italic with straight bar through it
    0xE384: [628,14,411,18,390],       // stix-small t italic with straight bar through it
    0xE386: [473,14,355,15,338],       // stix-small Greek iota with straight bar through it
    0xE388: [666,0,493,25,508],        // stix-small Greek lambda with straight bar through it
    0xE389: [666,0,480,16,472],        // LATIN SMALL LETTER LAMBDA WITH STROKE
    0xE3C5: [462,207,514,47,475],      // stix-mathematical bold italic small dotless j
    0xE3C6: [462,9,357,55,274]         // stix-mathematical bold italic small dotless i
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/NonUnicode/BoldItalic/PrivateUse.js");
