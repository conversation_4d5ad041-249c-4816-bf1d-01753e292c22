/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscTechnical.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2300: [487,-14,606,25,581],      // DIAMETER SIGN
    0x2302: [774,0,926,55,871],        // HOUSE
    0x2305: [577,0,620,48,572],        // PROJECTIVE
    0x2306: [728,0,620,48,572],        // PERSPECTIVE
    0x230C: [166,215,463,52,412],      // BOTTOM RIGHT CROP
    0x230D: [166,215,463,52,412],      // BOTTOM LEFT CROP
    0x230E: [876,-495,463,52,412],     // TOP RIGHT CROP
    0x230F: [876,-495,463,52,412],     // TOP LEFT CROP
    0x2310: [393,-115,600,48,552],     // REVERSED NOT SIGN
    0x2311: [439,-65,523,75,449],      // SQUARE LOZENGE
    0x2312: [331,0,762,50,712],        // ARC
    0x2313: [331,0,762,50,712],        // SEGMENT
    0x2315: [582,189,847,26,796],      // TELEPHONE RECORDER
    0x2316: [748,246,1100,53,1047],    // POSITION INDICATOR
    0x2317: [749,245,1100,53,1047],    // VIEWDATA SQUARE
    0x2318: [662,156,926,55,871],      // PLACE OF INTEREST SIGN
    0x2319: [393,-115,600,48,552],     // TURNED NOT SIGN
    0x231A: [671,69,685,64,622],       // WATCH
    0x231C: [662,-281,463,51,411],     // TOP LEFT CORNER
    0x231D: [662,-281,463,51,411],     // TOP RIGHT CORNER
    0x231E: [164,217,463,51,411],      // BOTTOM LEFT CORNER
    0x231F: [164,217,463,52,412],      // BOTTOM RIGHT CORNER
    0x2329: [713,213,400,77,335],      // LEFT-POINTING ANGLE BRACKET
    0x232A: [713,213,400,65,323],      // RIGHT-POINTING ANGLE BRACKET
    0x232C: [692,186,926,83,843],      // BENZENE RING
    0x232D: [592,88,986,55,931],       // CYLINDRICITY
    0x232E: [450,140,624,-18,574],     // ALL AROUND-PROFILE
    0x2332: [562,56,889,80,809],       // CONICAL TAPER
    0x2336: [751,156,926,85,841],      // APL FUNCTIONAL SYMBOL I-BEAM
    0x233D: [683,179,910,84,826],      // APL FUNCTIONAL SYMBOL CIRCLE STILE
    0x233F: [703,176,683,60,623],      // APL FUNCTIONAL SYMBOL SLASH BAR
    0x2340: [703,176,683,60,623],      // APL FUNCTIONAL SYMBOL BACKSLASH BAR
    0x2353: [751,176,794,55,739],      // APL FUNCTIONAL SYMBOL QUAD UP CARET
    0x2370: [751,176,794,55,739],      // APL FUNCTIONAL SYMBOL QUAD QUESTION
    0x237C: [584,220,871,50,820],      // RIGHT ANGLE WITH DOWNWARDS ZIGZAG ARROW
    0x2393: [386,-120,913,85,841],     // DIRECT CURRENT SYMBOL FORM TWO
    0x2394: [633,127,926,24,902],      // SOFTWARE-FUNCTION SYMBOL
    0x23B4: [766,-574,926,55,871],     // TOP SQUARE BRACKET
    0x23B5: [109,83,926,55,871],       // BOTTOM SQUARE BRACKET
    0x23B6: [495,-11,926,55,871],      // BOTTOM SQUARE BRACKET OVER TOP SQUARE BRACKET
    0x23CE: [731,225,926,50,856],      // RETURN SYMBOL
    0x23DC: [100,100,1000,0,1000],     // TOP PARENTHESIS (mathematical use)
    0x23DD: [764,-564,1000,0,1000],    // BOTTOM PARENTHESIS (mathematical use)
    0x23DE: [214,114,1000,0,1000],     // TOP CURLY BRACKET (mathematical use)
    0x23DF: [892,-564,1000,0,1000],    // BOTTOM CURLY BRACKET (mathematical use)
    0x23E0: [100,114,1000,0,1000],     // TOP TORTOISE SHELL BRACKET (mathematical use)
    0x23E1: [778,-564,1000,0,1000],    // BOTTOM TORTOISE SHELL BRACKET (mathematical use)
    0x23E2: [558,53,1144,54,1090],     // WHITE TRAPEZIUM
    0x23E3: [680,178,910,82,828],      // BENZENE RING WITH CIRCLE
    0x23E4: [286,-220,1094,47,1047],   // STRAIGHTNESS
    0x23E5: [527,20,1018,23,995],      // FLATNESS
    0x23E6: [434,-72,926,55,871],      // AC CURRENT
    0x23E7: [606,97,798,194,733]       // ELECTRICAL INTERSECTION
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MiscTechnical.js");
