/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathItalic.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-italic'],
  {
    skew: {
      0x1D434: .16,
      0x1D435: .14,
      0x1D436: .12,
      0x1D437: .1,
      0x1D438: .1,
      0x1D439: .1,
      0x1D43A: .12,
      0x1D43B: .1,
      0x1D43C: .1,
      0x1D43D: .1,
      0x1D43E: .1,
      0x1D43F: .05,
      0x1D440: .08,
      0x1D441: .08,
      0x1D442: .12,
      0x1D443: .05,
      0x1D444: .12,
      0x1D445: .1,
      0x1D446: .12,
      0x1D44B: .12,
      0x1D44C: -.05,
      0x1D44D: .1,
      0x1D44E: .09,
      0x1D44F: .03,
      0x1D450: .09,
      0x1D451: .2,
      0x1D452: .09,
      0x1D453: .19,
      0x1D454: .08,
      0x1D456: .08,
      0x1D457: .16,
      0x1D459: .09,
      0x1D45A: .09,
      0x1D45B: .05,
      0x1D45C: .09,
      0x1D45D: .09,
      0x1D45E: .1,
      0x1D45F: .07,
      0x1D460: .07,
      0x1D461: .07,
      0x1D462: .05,
      0x1D463: .08,
      0x1D464: .1,
      0x1D465: .05,
      0x1D466: .1,
      0x1D467: .07,
    },
    0x1D434: [667,0,717,35,685],       // MATHEMATICAL ITALIC CAPITAL A
    0x1D435: [653,0,696,38,686],       // MATHEMATICAL ITALIC CAPITAL B
    0x1D436: [659,12,671,50,711],      // MATHEMATICAL ITALIC CAPITAL C
    0x1D437: [653,0,790,38,765],       // MATHEMATICAL ITALIC CAPITAL D
    0x1D438: [653,0,714,38,734],       // MATHEMATICAL ITALIC CAPITAL E
    0x1D439: [653,0,618,38,723],       // MATHEMATICAL ITALIC CAPITAL F
    0x1D43A: [668,12,734,50,734],      // MATHEMATICAL ITALIC CAPITAL G
    0x1D43B: [653,0,873,38,923],       // MATHEMATICAL ITALIC CAPITAL H
    0x1D43C: [653,0,480,38,530],       // MATHEMATICAL ITALIC CAPITAL I
    0x1D43D: [653,12,540,60,620],      // MATHEMATICAL ITALIC CAPITAL J
    0x1D43E: [653,0,762,38,802],       // MATHEMATICAL ITALIC CAPITAL K
    0x1D43F: [653,0,708,38,668],       // MATHEMATICAL ITALIC CAPITAL L
    0x1D440: [653,0,1005,38,1055],     // MATHEMATICAL ITALIC CAPITAL M
    0x1D441: [653,0,851,38,901],       // MATHEMATICAL ITALIC CAPITAL N
    0x1D442: [669,11,732,50,712],      // MATHEMATICAL ITALIC CAPITAL O
    0x1D443: [653,0,594,38,704],       // MATHEMATICAL ITALIC CAPITAL P
    0x1D444: [667,152,781,50,731],     // MATHEMATICAL ITALIC CAPITAL Q
    0x1D445: [653,0,740,38,725],       // MATHEMATICAL ITALIC CAPITAL R
    0x1D446: [668,10,650,50,680],      // MATHEMATICAL ITALIC CAPITAL S
    0x1D447: [653,0,550,25,670],       // MATHEMATICAL ITALIC CAPITAL T
    0x1D448: [653,13,705,65,775],      // MATHEMATICAL ITALIC CAPITAL U
    0x1D449: [653,16,575,60,760],      // MATHEMATICAL ITALIC CAPITAL V
    0x1D44A: [653,16,916,60,1101],     // MATHEMATICAL ITALIC CAPITAL W
    0x1D44B: [653,0,790,25,810],       // MATHEMATICAL ITALIC CAPITAL X
    0x1D44C: [653,0,535,35,695],       // MATHEMATICAL ITALIC CAPITAL Y
    0x1D44D: [653,0,772,60,802],       // MATHEMATICAL ITALIC CAPITAL Z
    0x1D44E: [441,10,502,40,472],      // MATHEMATICAL ITALIC SMALL A
    0x1D44F: [668,11,470,45,450],      // MATHEMATICAL ITALIC SMALL B
    0x1D450: [441,11,415,40,400],      // MATHEMATICAL ITALIC SMALL C
    0x1D451: [668,12,532,40,527],      // MATHEMATICAL ITALIC SMALL D
    0x1D452: [441,11,445,40,410],      // MATHEMATICAL ITALIC SMALL E
    0x1D453: [668,187,555,40,615],     // MATHEMATICAL ITALIC SMALL F
    0x1D454: [441,187,492,20,492],     // MATHEMATICAL ITALIC SMALL G
    0x1D456: [616,11,311,50,257],      // MATHEMATICAL ITALIC SMALL I
    0x1D457: [616,187,389,-16,372],    // MATHEMATICAL ITALIC SMALL J
    0x1D458: [668,11,542,45,527],      // MATHEMATICAL ITALIC SMALL K
    0x1D459: [668,10,318,45,278],      // MATHEMATICAL ITALIC SMALL L
    0x1D45A: [441,8,710,30,680],       // MATHEMATICAL ITALIC SMALL M
    0x1D45B: [441,8,497,30,467],       // MATHEMATICAL ITALIC SMALL N
    0x1D45C: [441,11,458,40,438],      // MATHEMATICAL ITALIC SMALL O
    0x1D45D: [441,183,489,-30,474],    // MATHEMATICAL ITALIC SMALL P
    0x1D45E: [441,183,458,40,463],     // MATHEMATICAL ITALIC SMALL Q
    0x1D45F: [441,0,408,30,393],       // MATHEMATICAL ITALIC SMALL R
    0x1D460: [441,11,440,50,390],      // MATHEMATICAL ITALIC SMALL S
    0x1D461: [567,9,313,40,283],       // MATHEMATICAL ITALIC SMALL T
    0x1D462: [441,9,474,30,444],       // MATHEMATICAL ITALIC SMALL U
    0x1D463: [458,9,506,72,479],       // MATHEMATICAL ITALIC SMALL V
    0x1D464: [460,9,775,72,748],       // MATHEMATICAL ITALIC SMALL W
    0x1D465: [441,9,550,30,510],       // MATHEMATICAL ITALIC SMALL X
    0x1D466: [440,183,496,30,496],     // MATHEMATICAL ITALIC SMALL Y
    0x1D467: [450,14,499,42,467]       // MATHEMATICAL ITALIC SMALL Z
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Italic/MathItalic.js");
