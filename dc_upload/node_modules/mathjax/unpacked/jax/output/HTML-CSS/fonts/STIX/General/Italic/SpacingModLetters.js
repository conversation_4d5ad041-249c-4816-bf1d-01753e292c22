/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-italic'],
  {
    0x2B0: [838,-326,378,7,391],       // MODIFIER LETTER SMALL H
    0x2B1: [838,-326,378,7,414],       // MODIFIER LETTER SMALL H WITH HOOK
    0x2B2: [851,-199,300,44,350],      // MODIFIER LETTER SMALL J
    0x2B3: [690,-345,320,2,320],       // MODIFIER LETTER SMALL R
    0x2B4: [690,-345,320,0,318],       // MODIFIER LETTER SMALL TURNED R
    0x2B5: [690,-163,320,0,335],       // MODIFIER LETTER SMALL TURNED R WITH HOOK
    0x2B6: [684,-345,390,6,462],       // MODIFIER LETTER SMALL CAPITAL INVERTED R
    0x2B7: [690,-327,500,15,515],      // MODIFIER LETTER SMALL W
    0x2B8: [693,-202,330,16,357],      // MODIFIER LETTER SMALL Y
    0x2BB: [686,-443,333,79,236],      // MODIFIER LETTER TURNED COMMA
    0x2C0: [690,-295,326,30,307],      // MODIFIER LETTER GLOTTAL STOP
    0x2C1: [690,-295,326,23,343],      // MODIFIER LETTER REVERSED GLOTTAL STOP
    0x2C6: [661,-492,333,91,385],      // MODIFIER LETTER CIRCUMFLEX ACCENT
    0x2C7: [661,-492,333,121,426],     // CARON
    0x2D8: [650,-492,333,117,418],     // BREVE
    0x2D9: [606,-508,333,207,305],     // DOT ABOVE
    0x2DA: [707,-508,333,155,355],     // RING ABOVE
    0x2DB: [40,169,333,-20,200],       // OGONEK
    0x2DC: [624,-517,333,100,427],     // SMALL TILDE
    0x2DD: [664,-494,333,93,486],      // DOUBLE ACUTE ACCENT
    0x2E0: [684,-218,315,23,335],      // MODIFIER LETTER SMALL GAMMA
    0x2E1: [837,-333,220,41,214],      // MODIFIER LETTER SMALL L
    0x2E2: [691,-335,300,16,290],      // MODIFIER LETTER SMALL S
    0x2E3: [691,-333,380,4,379],       // MODIFIER LETTER SMALL X
    0x2E4: [847,-333,318,8,345],       // MODIFIER LETTER SMALL REVERSED GLOTTAL STOP
    0x2EC: [70,147,320,15,305],        // MODIFIER LETTER VOICING
    0x2ED: [665,-507,405,10,395],      // MODIFIER LETTER UNASPIRATED
    0x2F7: [-113,220,333,-94,233]      // ??
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Italic/SpacingModLetters.js");
