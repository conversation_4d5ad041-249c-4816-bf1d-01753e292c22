/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/MathOperators.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['MathJax_AMS'],
  {
    0x2201: [846,21,500,56,444],       // COMPLEMENT
    0x2204: [860,166,556,55,497],      // THERE DOES NOT EXIST
    0x2205: [587,3,778,54,720],        // EMPTY SET
    0x220D: [440,1,429,102,456],       // SMALL CONTAINS AS MEMBER
    0x2212: [270,-230,500,84,417],     // MINUS SIGN
    0x2214: [766,93,778,57,722],       // DOT PLUS
    0x2216: [430,23,778,91,685],       // SET MINUS
    0x221D: [472,-28,778,56,722],      // PROPORTIONAL TO
    0x2220: [694,0,722,55,666],        // ANGLE
    0x2221: [714,20,722,55,666],       // MEASURED ANGLE
    0x2222: [551,51,722,55,666],       // SPHERICAL ANGLE
    0x2223: [430,23,222,91,131],       // DIVIDES
    0x2224: [750,252,278,-21,297],     // DOES NOT DIVIDE
    0x2225: [431,23,389,55,331],       // PARALLEL TO
    0x2226: [750,250,500,-20,518],     // NOT PARALLEL TO
    0x2234: [471,82,667,24,643],       // THEREFORE
    0x2235: [471,82,667,23,643],       // BECAUSE
    0x223C: [365,-132,778,55,719],     // TILDE OPERATOR
    0x223D: [367,-133,778,56,722],     // REVERSED TILDE
    0x2241: [467,-32,778,55,719],      // stix-not, vert, similar
    0x2242: [463,-34,778,55,720],      // MINUS TILDE
    0x2246: [652,155,778,54,720],      // APPROXIMATELY BUT NOT ACTUALLY EQUAL TO
    0x2248: [481,-50,778,55,719],      // ALMOST EQUAL TO
    0x224A: [579,39,778,51,725],       // ALMOST EQUAL OR EQUAL TO
    0x224E: [492,-8,778,56,722],       // GEOMETRICALLY EQUIVALENT TO
    0x224F: [492,-133,778,56,722],     // DIFFERENCE BETWEEN
    0x2251: [609,108,778,56,722],      // GEOMETRICALLY EQUAL TO
    0x2252: [601,101,778,15,762],      // APPROXIMATELY EQUAL TO OR THE IMAGE OF
    0x2253: [601,102,778,14,762],      // IMAGE OF OR APPROXIMATELY EQUAL TO
    0x2256: [367,-133,778,56,722],     // RING IN EQUAL TO
    0x2257: [721,-133,778,56,722],     // RING EQUAL TO
    0x225C: [859,-133,778,56,723],     // DELTA EQUAL TO
    0x2266: [753,175,778,83,694],      // LESS-THAN OVER EQUAL TO
    0x2267: [753,175,778,83,694],      // GREATER-THAN OVER EQUAL TO
    0x2268: [752,286,778,82,693],      // stix-less, vert, not double equals
    0x2269: [752,286,778,82,693],      // stix-gt, vert, not double equals
    0x226C: [750,250,500,74,425],      // BETWEEN
    0x226E: [708,209,778,82,693],      // stix-not, vert, less-than
    0x226F: [708,209,778,82,693],      // stix-not, vert, greater-than
    0x2270: [801,303,778,82,694],      // stix-not, vert, less-than-or-equal
    0x2271: [801,303,778,82,694],      // stix-not, vert, greater-than-or-equal
    0x2272: [732,228,778,56,722],      // stix-less-than or (contour) similar
    0x2273: [732,228,778,56,722],      // stix-greater-than or (contour) similar
    0x2276: [681,253,778,44,734],      // LESS-THAN OR GREATER-THAN
    0x2277: [681,253,778,83,694],      // GREATER-THAN OR LESS-THAN
    0x227C: [580,153,778,83,694],      // PRECEDES OR EQUAL TO
    0x227D: [580,154,778,82,694],      // SUCCEEDS OR EQUAL TO
    0x227E: [732,228,778,56,722],      // PRECEDES OR EQUIVALENT TO
    0x227F: [732,228,778,56,722],      // SUCCEEDS OR EQUIVALENT TO
    0x2280: [705,208,778,82,693],      // DOES NOT PRECEDE
    0x2281: [705,208,778,82,693],      // stix-not (vert) succeeds
    0x2288: [801,303,778,83,693],      // stix-/nsubseteq N: not (vert) subset, equals
    0x2289: [801,303,778,82,691],      // stix-/nsupseteq N: not (vert) superset, equals
    0x228A: [635,241,778,84,693],      // stix-subset, not equals, variant
    0x228B: [635,241,778,82,691],      // stix-superset, not equals, variant
    0x228F: [539,41,778,83,694],       // SQUARE IMAGE OF
    0x2290: [539,41,778,64,714],       // SQUARE ORIGINAL OF
    0x229A: [582,82,778,57,721],       // CIRCLED RING OPERATOR
    0x229B: [582,82,778,57,721],       // CIRCLED ASTERISK OPERATOR
    0x229D: [582,82,778,57,721],       // CIRCLED DASH
    0x229E: [689,0,778,55,722],        // SQUARED PLUS
    0x229F: [689,0,778,55,722],        // SQUARED MINUS
    0x22A0: [689,0,778,55,722],        // SQUARED TIMES
    0x22A1: [689,0,778,55,722],        // SQUARED DOT OPERATOR
    0x22A8: [694,0,611,55,555],        // TRUE
    0x22A9: [694,0,722,55,666],        // FORCES
    0x22AA: [694,0,889,55,833],        // TRIPLE VERTICAL BAR RIGHT TURNSTILE
    0x22AC: [695,1,611,-55,554],       // DOES NOT PROVE
    0x22AD: [695,1,611,-55,554],       // NOT TRUE
    0x22AE: [695,1,722,-55,665],       // DOES NOT FORCE
    0x22AF: [695,1,722,-55,665],       // NEGATED DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
    0x22B2: [539,41,778,83,694],       // NORMAL SUBGROUP OF
    0x22B3: [539,41,778,83,694],       // CONTAINS AS NORMAL SUBGROUP
    0x22B4: [636,138,778,83,694],      // NORMAL SUBGROUP OF OR EQUAL TO
    0x22B5: [636,138,778,83,694],      // CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
    0x22B8: [408,-92,1111,55,1055],    // MULTIMAP
    0x22BA: [431,212,556,57,500],      // INTERCALATE
    0x22BB: [716,0,611,55,555],        // XOR
    0x22BC: [716,0,611,55,555],        // NAND
    0x22C5: [189,0,278,55,222],        // DOT OPERATOR
    0x22C7: [545,44,778,55,720],       // DIVISION TIMES
    0x22C9: [492,-8,778,146,628],      // LEFT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22CA: [492,-8,778,146,628],      // RIGHT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22CB: [694,22,778,55,722],       // LEFT SEMIDIRECT PRODUCT
    0x22CC: [694,22,778,55,722],       // RIGHT SEMIDIRECT PRODUCT
    0x22CD: [464,-36,778,56,722],      // REVERSED TILDE EQUALS
    0x22CE: [578,21,760,83,676],       // CURLY LOGICAL OR
    0x22CF: [578,22,760,83,676],       // CURLY LOGICAL AND
    0x22D0: [540,40,778,84,694],       // DOUBLE SUBSET
    0x22D1: [540,40,778,83,693],       // DOUBLE SUPERSET
    0x22D2: [598,22,667,55,611],       // DOUBLE INTERSECTION
    0x22D3: [598,22,667,55,611],       // DOUBLE UNION
    0x22D4: [736,22,667,56,611],       // PITCHFORK
    0x22D6: [541,41,778,82,693],       // LESS-THAN WITH DOT
    0x22D7: [541,41,778,82,693],       // GREATER-THAN WITH DOT
    0x22D8: [568,67,1333,56,1277],     // VERY MUCH LESS-THAN
    0x22D9: [568,67,1333,55,1277],     // VERY MUCH GREATER-THAN
    0x22DA: [886,386,778,83,674],      // stix-less, equal, slanted, greater
    0x22DB: [886,386,778,83,674],      // stix-greater, equal, slanted, less
    0x22DE: [734,0,778,83,694],        // EQUAL TO OR PRECEDES
    0x22DF: [734,0,778,82,694],        // EQUAL TO OR SUCCEEDS
    0x22E0: [801,303,778,82,693],      // stix-not (vert) precedes or contour equals
    0x22E1: [801,303,778,82,694],      // stix-not (vert) succeeds or contour equals
    0x22E6: [730,359,778,55,719],      // LESS-THAN BUT NOT EQUIVALENT TO
    0x22E7: [730,359,778,55,719],      // GREATER-THAN BUT NOT EQUIVALENT TO
    0x22E8: [730,359,778,55,719],      // PRECEDES BUT NOT EQUIVALENT TO
    0x22E9: [730,359,778,55,719],      // SUCCEEDS BUT NOT EQUIVALENT TO
    0x22EA: [706,208,778,82,693],      // NOT NORMAL SUBGROUP OF
    0x22EB: [706,208,778,82,693],      // DOES NOT CONTAIN AS NORMAL SUBGROUP
    0x22EC: [802,303,778,82,693],      // stix-not, vert, left triangle, equals
    0x22ED: [801,303,778,82,693]       // stix-not, vert, right triangle, equals
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/AMS/Regular/MathOperators.js");
