/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MathOperators.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['MathJax_Main-bold'],
  {
    0x2200: [694,16,639,1,640],        // FOR ALL
    0x2202: [710,17,628,60,657],       // PARTIAL DIFFERENTIAL
    0x2203: [694,-1,639,64,574],       // THERE EXISTS
    0x2205: [767,73,575,46,528],       // EMPTY SET
    0x2207: [686,24,958,56,901],       // NABLA
    0x2208: [587,86,767,97,670],       // ELEMENT OF
    0x2209: [711,210,767,97,670],      // stix-negated (vert) set membership, variant
    0x220B: [587,86,767,96,670],       // CONTAINS AS MEMBER
    0x2212: [281,-221,894,96,797],     // MINUS SIGN
    0x2213: [537,227,894,64,829],      // MINUS-OR-PLUS SIGN
    0x2215: [750,250,575,63,511],      // DIVISION SLASH
    0x2216: [750,250,575,63,511],      // SET MINUS
    0x2217: [472,-28,575,73,501],      // ASTERISK OPERATOR
    0x2218: [474,-28,575,64,510],      // RING OPERATOR
    0x2219: [474,-28,575,64,510],      // BULLET OPERATOR
    0x221A: [820,180,958,78,988],      // SQUARE ROOT
    0x221D: [451,8,894,65,830],        // PROPORTIONAL TO
    0x221E: [452,8,1150,65,1084],      // INFINITY
    0x2220: [714,0,722,55,676],        // ANGLE
    0x2223: [750,249,319,129,190],     // DIVIDES
    0x2225: [750,248,575,145,430],     // PARALLEL TO
    0x2227: [604,17,767,64,702],       // LOGICAL AND
    0x2228: [604,16,767,64,702],       // LOGICAL OR
    0x2229: [603,16,767,64,702],       // stix-intersection, serifs
    0x222A: [604,16,767,64,702],       // stix-union, serifs
    0x222B: [711,211,569,64,632],      // INTEGRAL
    0x223C: [391,-109,894,64,828],     // TILDE OPERATOR
    0x2240: [583,82,319,64,254],       // WREATH PRODUCT
    0x2243: [502,3,894,64,829],        // ASYMPTOTICALLY EQUAL TO
    0x2245: [638,27,1000,64,829],      // APPROXIMATELY EQUAL TO
    0x2248: [524,-32,894,64,829],      // ALMOST EQUAL TO
    0x224D: [533,32,894,64,829],       // EQUIVALENT TO
    0x2250: [721,-109,894,64,829],     // APPROACHES THE LIMIT
    0x2260: [711,210,894,64,829],      // stix-not (vert) equals
    0x2261: [505,3,894,64,829],        // IDENTICAL TO
    0x2264: [697,199,894,96,797],      // LESS-THAN OR EQUAL TO
    0x2265: [697,199,894,96,797],      // GREATER-THAN OR EQUAL TO
    0x226A: [617,116,1150,64,1085],    // MUCH LESS-THAN
    0x226B: [618,116,1150,64,1085],    // MUCH GREATER-THAN
    0x227A: [585,86,894,96,797],       // PRECEDES
    0x227B: [586,86,894,96,797],       // SUCCEEDS
    0x2282: [587,85,894,96,797],       // SUBSET OF
    0x2283: [587,86,894,96,796],       // SUPERSET OF
    0x2286: [697,199,894,96,797],      // SUBSET OF OR EQUAL TO
    0x2287: [697,199,894,96,796],      // SUPERSET OF OR EQUAL TO
    0x228E: [604,16,767,64,702],       // MULTISET UNION
    0x2291: [697,199,894,96,828],      // SQUARE IMAGE OF OR EQUAL TO
    0x2292: [697,199,894,66,797],      // SQUARE ORIGINAL OF OR EQUAL TO
    0x2293: [604,-1,767,70,696],       // stix-square intersection, serifs
    0x2294: [604,-1,767,70,696],       // stix-square union, serifs
    0x2295: [632,132,894,64,828],      // stix-circled plus (with rim)
    0x2296: [632,132,894,64,828],      // CIRCLED MINUS
    0x2297: [632,132,894,64,828],      // stix-circled times (with rim)
    0x2298: [632,132,894,64,828],      // CIRCLED DIVISION SLASH
    0x2299: [632,132,894,64,828],      // CIRCLED DOT OPERATOR
    0x22A2: [693,-1,703,65,637],       // RIGHT TACK
    0x22A3: [693,-1,703,64,638],       // LEFT TACK
    0x22A4: [694,-1,894,64,829],       // DOWN TACK
    0x22A5: [693,-1,894,65,829],       // UP TACK
    0x22A8: [750,249,974,129,918],     // TRUE
    0x22C4: [523,21,575,15,560],       // DIAMOND OPERATOR
    0x22C5: [336,-166,319,74,245],     // DOT OPERATOR
    0x22C6: [502,0,575,24,550],        // STAR OPERATOR
    0x22C8: [540,39,1000,33,967],      // BOWTIE
    0x22EE: [951,29,319,74,245],       // VERTICAL ELLIPSIS
    0x22EF: [336,-166,1295,74,1221],   // MIDLINE HORIZONTAL ELLIPSIS
    0x22F1: [871,-101,1323,129,1194]   // DOWN RIGHT DIAGONAL ELLIPSIS
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/Main/Bold/MathOperators.js");
