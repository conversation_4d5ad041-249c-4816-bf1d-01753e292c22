/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2B0: [848,-336,378,7,365],       // MODIFIER LETTER SMALL H
    0x2B1: [848,-336,378,7,365],       // MODIFIER LETTER SMALL H WITH HOOK
    0x2B2: [852,-169,300,44,244],      // MODIFIER LETTER SMALL J
    0x2B3: [681,-336,252,5,252],       // MODIFIER LETTER SMALL R
    0x2B4: [680,-335,277,10,257],      // MODIFIER LETTER SMALL TURNED R
    0x2B5: [680,-168,325,10,338],      // MODIFIER LETTER SMALL TURNED R WITH HOOK
    0x2B6: [680,-335,390,6,379],       // MODIFIER LETTER SMALL CAPITAL INVERTED R
    0x2B7: [680,-331,520,6,512],       // MODIFIER LETTER SMALL W
    0x2B8: [680,-176,370,14,361],      // MODIFIER LETTER SMALL Y
    0x2B9: [684,-421,208,90,257],      // MODIFIER LETTER PRIME
    0x2BA: [684,-421,305,19,324],      // MODIFIER LETTER DOUBLE PRIME
    0x2BB: [686,-443,333,79,218],      // MODIFIER LETTER TURNED COMMA
    0x2BC: [686,-443,333,79,218],      // MODIFIER LETTER APOSTROPHE
    0x2BD: [686,-443,333,79,218],      // MODIFIER LETTER REVERSED COMMA
    0x2BE: [680,-485,198,35,163],      // MODIFIER LETTER RIGHT HALF RING
    0x2BF: [680,-485,198,35,163],      // MODIFIER LETTER LEFT HALF RING
    0x2C0: [690,-295,326,23,303],      // MODIFIER LETTER GLOTTAL STOP
    0x2C1: [690,-295,326,23,303],      // MODIFIER LETTER REVERSED GLOTTAL STOP
    0x2C2: [755,-419,317,33,285],      // MODIFIER LETTER LEFT ARROWHEAD
    0x2C3: [755,-419,317,33,285],      // MODIFIER LETTER RIGHT ARROWHEAD
    0x2C4: [713,-461,317,-9,327],      // MODIFIER LETTER UP ARROWHEAD
    0x2C5: [713,-461,317,-9,327],      // MODIFIER LETTER DOWN ARROWHEAD
    0x2C8: [713,-448,278,119,159],     // MODIFIER LETTER VERTICAL LINE
    0x2CC: [70,195,278,119,159],       // MODIFIER LETTER LOW VERTICAL LINE
    0x2CD: [-104,159,334,11,323],      // MODIFIER LETTER LOW MACRON
    0x2CE: [-21,192,333,25,249],       // MODIFIER LETTER LOW GRAVE ACCENT
    0x2CF: [-21,192,333,84,308],       // MODIFIER LETTER LOW ACUTE ACCENT
    0x2D0: [460,-19,333,89,244],       // MODIFIER LETTER TRIANGULAR COLON
    0x2D1: [460,-299,333,89,244],      // MODIFIER LETTER HALF TRIANGULAR COLON
    0x2D2: [365,-75,333,72,262],       // MODIFIER LETTER CENTRED RIGHT HALF RING
    0x2D3: [365,-75,333,71,261],       // MODIFIER LETTER CENTRED LEFT HALF RING
    0x2D4: [205,-18,333,51,281],       // MODIFIER LETTER UP TACK
    0x2D5: [205,-18,333,51,281],       // MODIFIER LETTER DOWN TACK
    0x2D6: [218,-26,333,71,263],       // MODIFIER LETTER PLUS SIGN
    0x2D7: [144,-100,333,71,263],      // MODIFIER LETTER MINUS SIGN
    0x2DA: [711,-512,333,67,266],      // RING ABOVE
    0x2DB: [0,165,333,64,249],         // OGONEK
    0x2DD: [678,-507,333,-3,376],      // DOUBLE ACUTE ACCENT
    0x2DE: [443,-186,298,0,263],       // MODIFIER LETTER RHOTIC HOOK
    0x2DF: [662,-425,333,48,284],      // MODIFIER LETTER CROSS ACCENT
    0x2E0: [684,-219,378,24,335],      // MODIFIER LETTER SMALL GAMMA
    0x2E1: [848,-336,215,19,197],      // MODIFIER LETTER SMALL L
    0x2E2: [681,-331,291,36,261],      // MODIFIER LETTER SMALL S
    0x2E3: [680,-336,380,5,372],       // MODIFIER LETTER SMALL X
    0x2E4: [850,-336,341,45,319],      // MODIFIER LETTER SMALL REVERSED GLOTTAL STOP
    0x2E5: [662,0,413,48,373],         // MODIFIER LETTER EXTRA-HIGH TONE BAR
    0x2E6: [662,0,405,40,365],         // MODIFIER LETTER HIGH TONE BAR
    0x2E7: [662,0,405,40,365],         // MODIFIER LETTER MID TONE BAR
    0x2E8: [662,0,405,40,365],         // MODIFIER LETTER LOW TONE BAR
    0x2E9: [662,0,405,40,365],         // MODIFIER LETTER EXTRA-LOW TONE BAR
    0x2EC: [70,147,333,21,311],        // MODIFIER LETTER VOICING
    0x2ED: [665,-507,405,10,395],      // MODIFIER LETTER UNASPIRATED
    0x2F7: [-113,219,333,1,331]        // ??
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/SpacingModLetters.js");
