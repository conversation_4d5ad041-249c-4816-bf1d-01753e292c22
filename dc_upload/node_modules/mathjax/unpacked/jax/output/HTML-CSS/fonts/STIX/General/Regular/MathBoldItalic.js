/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathBoldItalic.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x1D468: [685,0,759,39,724],       // MATHEMATICAL BOLD ITALIC CAPITAL A
    0x1D469: [669,0,726,42,715],       // MATHEMATICAL BOLD ITALIC CAPITAL B
    0x1D46A: [685,12,701,55,745],      // MATHEMATICAL BOLD ITALIC CAPITAL C
    0x1D46B: [669,0,818,42,790],       // MATHEMATICAL BOLD ITALIC CAPITAL D
    0x1D46C: [669,0,732,42,754],       // MATHEMATICAL BOLD ITALIC CAPITAL E
    0x1D46D: [669,0,635,44,750],       // MATHEMATICAL BOLD ITALIC CAPITAL F
    0x1D46E: [685,12,768,55,768],      // MATHEMATICAL BOLD ITALIC CAPITAL G
    0x1D46F: [669,0,891,42,946],       // MATHEMATICAL BOLD ITALIC CAPITAL H
    0x1D470: [669,0,502,42,557],       // MATHEMATICAL BOLD ITALIC CAPITAL I
    0x1D471: [669,12,558,66,646],      // MATHEMATICAL BOLD ITALIC CAPITAL J
    0x1D472: [669,0,795,42,839],       // MATHEMATICAL BOLD ITALIC CAPITAL K
    0x1D473: [669,0,744,42,700],       // MATHEMATICAL BOLD ITALIC CAPITAL L
    0x1D474: [669,0,1016,42,1071],     // MATHEMATICAL BOLD ITALIC CAPITAL M
    0x1D475: [669,0,869,42,924],       // MATHEMATICAL BOLD ITALIC CAPITAL N
    0x1D476: [685,16,777,55,755],      // MATHEMATICAL BOLD ITALIC CAPITAL O
    0x1D477: [669,0,612,42,733],       // MATHEMATICAL BOLD ITALIC CAPITAL P
    0x1D478: [685,154,810,55,756],     // MATHEMATICAL BOLD ITALIC CAPITAL Q
    0x1D479: [669,0,801,42,784],       // MATHEMATICAL BOLD ITALIC CAPITAL R
    0x1D47A: [685,10,671,55,704],      // MATHEMATICAL BOLD ITALIC CAPITAL S
    0x1D47B: [669,0,568,28,700],       // MATHEMATICAL BOLD ITALIC CAPITAL T
    0x1D47C: [669,10,733,72,810],      // MATHEMATICAL BOLD ITALIC CAPITAL U
    0x1D47D: [669,15,593,66,797],      // MATHEMATICAL BOLD ITALIC CAPITAL V
    0x1D47E: [669,17,925,66,1129],     // MATHEMATICAL BOLD ITALIC CAPITAL W
    0x1D47F: [669,0,808,28,830],       // MATHEMATICAL BOLD ITALIC CAPITAL X
    0x1D480: [669,0,549,39,725],       // MATHEMATICAL BOLD ITALIC CAPITAL Y
    0x1D481: [669,0,797,66,830],       // MATHEMATICAL BOLD ITALIC CAPITAL Z
    0x1D482: [462,10,581,44,548],      // MATHEMATICAL BOLD ITALIC SMALL A
    0x1D483: [685,8,509,50,487],       // MATHEMATICAL BOLD ITALIC SMALL B
    0x1D484: [462,10,477,44,460],      // MATHEMATICAL BOLD ITALIC SMALL C
    0x1D485: [685,14,595,44,589],      // MATHEMATICAL BOLD ITALIC SMALL D
    0x1D486: [462,10,498,44,459],      // MATHEMATICAL BOLD ITALIC SMALL E
    0x1D487: [685,206,572,44,632],     // MATHEMATICAL BOLD ITALIC SMALL F
    0x1D488: [462,203,527,22,527],     // MATHEMATICAL BOLD ITALIC SMALL G
    0x1D489: [685,10,576,50,543],      // MATHEMATICAL BOLD ITALIC SMALL H
    0x1D48A: [620,9,357,55,300],       // MATHEMATICAL BOLD ITALIC SMALL I
    0x1D48B: [620,207,431,-18,414],    // MATHEMATICAL BOLD ITALIC SMALL J
    0x1D48C: [686,11,580,55,563],      // MATHEMATICAL BOLD ITALIC SMALL K
    0x1D48D: [685,9,346,50,310],       // MATHEMATICAL BOLD ITALIC SMALL L
    0x1D48E: [467,9,760,33,727],       // MATHEMATICAL BOLD ITALIC SMALL M
    0x1D48F: [467,10,559,33,526],      // MATHEMATICAL BOLD ITALIC SMALL N
    0x1D490: [462,10,561,44,539],      // MATHEMATICAL BOLD ITALIC SMALL O
    0x1D491: [469,205,571,-33,554],    // MATHEMATICAL BOLD ITALIC SMALL P
    0x1D492: [462,205,526,44,532],     // MATHEMATICAL BOLD ITALIC SMALL Q
    0x1D493: [467,0,441,33,424],       // MATHEMATICAL BOLD ITALIC SMALL R
    0x1D494: [462,11,474,55,419],      // MATHEMATICAL BOLD ITALIC SMALL S
    0x1D495: [592,10,351,44,318],      // MATHEMATICAL BOLD ITALIC SMALL T
    0x1D496: [463,10,535,33,502],      // MATHEMATICAL BOLD ITALIC SMALL U
    0x1D497: [473,14,554,52,539],      // MATHEMATICAL BOLD ITALIC SMALL V
    0x1D498: [473,14,814,52,799],      // MATHEMATICAL BOLD ITALIC SMALL W
    0x1D499: [462,8,587,33,543],       // MATHEMATICAL BOLD ITALIC SMALL X
    0x1D49A: [462,205,519,35,522],     // MATHEMATICAL BOLD ITALIC SMALL Y
    0x1D49B: [461,19,531,35,499]       // MATHEMATICAL BOLD ITALIC SMALL Z
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MathBoldItalic.js");
