/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscMathSymbolsB.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2980: [695,189,594,85,509],      // TRIPLE VERTICAL BAR DELIMITER
    0x2981: [487,-14,565,46,519],      // Z NOTATION SPOT
    0x2982: [566,59,503,110,393],      // Z NOTATION TYPE COLON
    0x2983: [719,213,596,108,477],     // LEFT WHITE CURLY BRACKET
    0x2984: [719,213,596,119,488],     // RIGHT WHITE CURLY BRACKET
    0x2985: [719,213,463,70,393],      // LEFT WHITE PARENTHESIS
    0x2986: [719,213,463,70,393],      // RIGHT WHITE PARENTHESIS
    0x2987: [719,214,511,115,367],     // Z NOTATION LEFT IMAGE BRACKET
    0x2988: [719,214,511,144,396],     // Z NOTATION RIGHT IMAGE BRACKET
    0x2989: [719,213,511,100,352],     // Z NOTATION LEFT BINDING BRACKET
    0x298A: [719,213,511,159,411],     // Z NOTATION RIGHT BINDING BRACKET
    0x298B: [719,213,469,188,447],     // LEFT SQUARE BRACKET WITH UNDERBAR
    0x298C: [719,213,469,22,281],      // RIGHT SQUARE BRACKET WITH UNDERBAR
    0x298D: [719,213,469,188,447],     // LEFT SQUARE BRACKET WITH TICK IN TOP CORNER
    0x298E: [719,213,469,22,281],      // RIGHT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
    0x298F: [719,213,469,188,447],     // LEFT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
    0x2990: [719,213,469,22,281],      // RIGHT SQUARE BRACKET WITH TICK IN TOP CORNER
    0x2991: [719,213,400,73,357],      // LEFT ANGLE BRACKET WITH DOT
    0x2992: [719,213,400,73,357],      // RIGHT ANGLE BRACKET WITH DOT
    0x2993: [649,143,685,34,591],      // LEFT ARC LESS-THAN BRACKET
    0x2994: [649,143,685,94,651],      // RIGHT ARC GREATER-THAN BRACKET
    0x2995: [649,143,685,86,643],      // DOUBLE LEFT ARC GREATER-THAN BRACKET
    0x2996: [649,143,685,42,599],      // DOUBLE RIGHT ARC LESS-THAN BRACKET
    0x2997: [719,213,488,188,466],     // LEFT BLACK TORTOISE SHELL BRACKET
    0x2998: [719,213,488,22,300],      // RIGHT BLACK TORTOISE SHELL BRACKET
    0x2999: [661,155,211,50,161],      // DOTTED FENCE
    0x299A: [662,156,511,177,334],     // VERTICAL ZIGZAG LINE
    0x299B: [547,72,685,42,662],       // MEASURED ANGLE OPENING LEFT
    0x299C: [584,0,685,50,634],        // RIGHT ANGLE VARIANT WITH SQUARE
    0x299D: [584,0,685,50,634],        // MEASURED RIGHT ANGLE WITH DOT
    0x299E: [547,0,685,11,675],        // ANGLE WITH S INSIDE
    0x299F: [396,0,685,24,643],        // ACUTE ANGLE
    0x29A0: [517,13,685,57,654],       // SPHERICAL ANGLE OPENING LEFT
    0x29A1: [609,-12,685,77,607],      // SPHERICAL ANGLE OPENING UP
    0x29A2: [547,0,685,42,662],        // TURNED ANGLE
    0x29A3: [547,0,685,42,662],        // REVERSED ANGLE
    0x29A4: [547,200,685,23,643],      // ANGLE WITH UNDERBAR
    0x29A5: [547,200,685,42,662],      // REVERSED ANGLE WITH UNDERBAR
    0x29A6: [547,0,900,40,860],        // OBLIQUE ANGLE OPENING UP
    0x29A7: [547,0,900,40,860],        // OBLIQUE ANGLE OPENING DOWN
    0x29A8: [574,72,685,29,649],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING UP AND RIGHT
    0x29A9: [574,72,685,36,656],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING UP AND LEFT
    0x29AA: [578,68,685,29,649],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING DOWN AND RIGHT
    0x29AB: [578,68,685,36,656],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING DOWN AND LEFT
    0x29AC: [562,58,706,34,680],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING RIGHT AND UP
    0x29AD: [562,58,706,26,672],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING LEFT AND UP
    0x29AE: [562,58,706,34,680],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING RIGHT AND DOWN
    0x29AF: [562,58,708,26,672],       // MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING LEFT AND DOWN
    0x29B0: [583,79,762,50,712],       // REVERSED EMPTY SET
    0x29B1: [717,79,762,50,712],       // EMPTY SET WITH OVERBAR
    0x29B2: [819,79,762,50,712],       // EMPTY SET WITH SMALL CIRCLE ABOVE
    0x29B3: [832,79,762,50,712],       // EMPTY SET WITH RIGHT ARROW ABOVE
    0x29B4: [832,79,762,50,712],       // EMPTY SET WITH LEFT ARROW ABOVE
    0x29B5: [623,119,910,24,886],      // CIRCLE WITH HORIZONTAL BAR
    0x29B6: [623,119,842,50,792],      // CIRCLED VERTICAL BAR
    0x29B7: [623,119,842,50,792],      // CIRCLED PARALLEL
    0x29B8: [623,119,842,50,792],      // CIRCLED REVERSE SOLIDUS
    0x29B9: [623,119,842,50,792],      // CIRCLED PERPENDICULAR
    0x29BA: [623,119,842,50,792],      // CIRCLE DIVIDED BY HORIZONTAL BAR AND TOP HALF DIVIDED BY VERTICAL BAR
    0x29BB: [623,119,842,50,792],      // CIRCLE WITH SUPERIMPOSED X
    0x29BC: [623,119,842,50,792],      // CIRCLED ANTICLOCKWISE-ROTATED DIVISION SIGN
    0x29BD: [882,179,842,50,792],      // UP ARROW THROUGH CIRCLE
    0x29BE: [623,119,842,50,792],      // CIRCLED WHITE BULLET
    0x29BF: [623,119,842,50,792],      // CIRCLED BULLET
    0x29C0: [623,119,842,50,792],      // CIRCLED LESS-THAN
    0x29C1: [623,119,842,50,792],      // CIRCLED GREATER-THAN
    0x29C2: [623,119,1091,50,1056],    // CIRCLE WITH SMALL CIRCLE TO THE RIGHT
    0x29C3: [623,119,1091,50,1056],    // CIRCLE WITH TWO HORIZONTAL STROKES TO THE RIGHT
    0x29C4: [662,158,910,45,865],      // SQUARED RISING DIAGONAL SLASH
    0x29C5: [662,158,910,45,865],      // SQUARED FALLING DIAGONAL SLASH
    0x29C6: [662,158,910,45,865],      // SQUARED ASTERISK
    0x29C7: [662,158,910,45,865],      // SQUARED SMALL CIRCLE
    0x29C8: [662,158,910,45,865],      // SQUARED SQUARE
    0x29C9: [712,207,1046,64,982],     // TWO JOINED SQUARES
    0x29CA: [1003,127,1145,35,1110],   // TRIANGLE WITH DOT ABOVE
    0x29CB: [811,259,1145,35,1110],    // TRIANGLE WITH UNDERBAR
    0x29CC: [811,127,1145,35,1110],    // S IN TRIANGLE
    0x29CD: [811,127,1165,15,1150],    // TRIANGLE WITH SERIFS AT BOTTOM
    0x29CE: [698,193,780,70,710],      // RIGHT TRIANGLE ABOVE LEFT TRIANGLE
    0x29CF: [531,25,857,48,777],       // LEFT TRIANGLE BESIDE VERTICAL BAR
    0x29D0: [531,25,857,80,809],       // VERTICAL BAR BESIDE RIGHT TRIANGLE
    0x29D1: [582,80,810,93,716],       // BOWTIE WITH LEFT HALF BLACK
    0x29D2: [582,80,810,93,716],       // BOWTIE WITH RIGHT HALF BLACK
    0x29D3: [582,80,810,93,716],       // BLACK BOWTIE
    0x29D4: [582,80,810,94,717],       // TIMES WITH LEFT HALF BLACK
    0x29D5: [582,80,810,93,716],       // TIMES WITH RIGHT HALF BLACK
    0x29D6: [602,100,810,74,736],      // WHITE HOURGLASS
    0x29D7: [602,100,810,74,736],      // BLACK HOURGLASS
    0x29D8: [620,116,511,177,334],     // LEFT WIGGLY FENCE
    0x29D9: [620,116,511,176,333],     // RIGHT WIGGLY FENCE
    0x29DA: [620,116,688,177,511],     // LEFT DOUBLE WIGGLY FENCE
    0x29DB: [620,116,688,177,511],     // RIGHT DOUBLE WIGGLY FENCE
    0x29DC: [430,0,926,70,854],        // INCOMPLETE INFINITY
    0x29DD: [653,0,926,70,854],        // TIE OVER INFINITY
    0x29DE: [695,189,926,70,854],      // INFINITY NEGATED WITH VERTICAL BAR
    0x29DF: [403,-103,1145,50,1095],   // DOUBLE-ENDED MULTIMAP
    0x29E0: [662,157,910,45,865],      // SQUARE WITH CONTOURED OUTLINE
    0x29E1: [512,8,667,24,613],        // INCREASES AS
    0x29E2: [414,0,790,64,726],        // SHUFFLE PRODUCT
    0x29E3: [662,156,685,47,637],      // EQUALS SIGN AND SLANTED PARALLEL
    0x29E4: [842,156,685,47,637],      // EQUALS SIGN AND SLANTED PARALLEL WITH TILDE ABOVE
    0x29E5: [662,156,685,48,637],      // IDENTICAL TO AND SLANTED PARALLEL
    0x29E6: [584,78,798,60,738],       // GLEICH STARK
    0x29E7: [695,189,628,48,580],      // THERMODYNAMIC
    0x29E8: [811,127,1145,35,1110],    // DOWN-POINTING TRIANGLE WITH LEFT HALF BLACK
    0x29E9: [811,127,1145,35,1110],    // DOWN-POINTING TRIANGLE WITH RIGHT HALF BLACK
    0x29EA: [744,241,762,32,730],      // BLACK DIAMOND WITH DOWN ARROW
    0x29EB: [795,289,790,45,745],      // BLACK LOZENGE
    0x29EC: [743,241,762,50,712],      // WHITE CIRCLE WITH DOWN ARROW
    0x29ED: [743,241,762,50,712],      // BLACK CIRCLE WITH DOWN ARROW
    0x29EE: [747,243,762,97,665],      // ERROR-BARRED WHITE SQUARE
    0x29EF: [747,243,762,97,665],      // ERROR-BARRED BLACK SQUARE
    0x29F0: [747,243,762,32,730],      // ERROR-BARRED WHITE DIAMOND
    0x29F1: [747,243,762,32,730],      // ERROR-BARRED BLACK DIAMOND
    0x29F2: [747,243,762,65,697],      // ERROR-BARRED WHITE CIRCLE
    0x29F3: [747,243,762,65,697],      // ERROR-BARRED BLACK CIRCLE
    0x29F4: [521,13,926,55,871],       // RULE-DELAYED
    0x29F6: [765,80,520,94,426],       // SOLIDUS WITH OVERBAR
    0x29F7: [662,80,520,94,426],       // REVERSE SOLIDUS WITH HORIZONTAL STROKE
    0x29F8: [695,325,602,85,517],      // BIG SOLIDUS
    0x29F9: [695,325,602,85,517],      // BIG REVERSE SOLIDUS
    0x29FA: [532,25,685,64,621],       // DOUBLE PLUS
    0x29FB: [532,25,685,64,621],       // TRIPLE PLUS
    0x29FC: [713,213,459,77,394],      // LEFT-POINTING CURVED ANGLE BRACKET
    0x29FD: [713,213,459,65,382],      // RIGHT-POINTING CURVED ANGLE BRACKET
    0x29FE: [540,36,762,93,669],       // TINY
    0x29FF: [316,-190,762,93,669]      // MINY
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MiscMathSymbolsB.js");
