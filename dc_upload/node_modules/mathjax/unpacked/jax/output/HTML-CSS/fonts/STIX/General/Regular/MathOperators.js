/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathOperators.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2201: [760,15,463,59,404],       // COMPLEMENT
    0x2204: [775,122,560,71,487],      // THERE DOES NOT EXIST
    0x2206: [674,0,731,63,665],        // INCREMENT
    0x220A: [459,-45,486,64,422],      // SMALL ELEMENT OF
    0x220C: [662,157,685,60,625],      // stix-negated (vert) contains
    0x220D: [459,-45,486,64,422],      // SMALL CONTAINS AS MEMBER
    0x220E: [640,0,545,60,485],        // END OF PROOF
    0x2214: [741,41,685,48,636],       // DOT PLUS
    0x221B: [973,259,928,112,963],     // CUBE ROOT
    0x221C: [973,259,928,112,963],     // FOURTH ROOT
    0x221F: [584,0,685,50,634],        // RIGHT ANGLE
    0x2221: [547,72,685,22,642],       // MEASURED ANGLE
    0x2222: [519,11,685,56,653],       // SPHERICAL ANGLE
    0x2224: [690,189,404,23,381],      // DOES NOT DIVIDE
    0x2226: [690,189,609,23,586],      // NOT PARALLEL TO
    0x222C: [824,320,701,32,881],      // DOUBLE INTEGRAL
    0x222D: [824,320,943,32,1123],     // TRIPLE INTEGRAL
    0x222E: [824,320,499,32,639],      // CONTOUR INTEGRAL
    0x222F: [824,320,741,32,881],      // SURFACE INTEGRAL
    0x2230: [824,320,982,32,1122],     // VOLUME INTEGRAL
    0x2231: [824,320,499,32,639],      // CLOCKWISE INTEGRAL
    0x2232: [824,320,499,32,639],      // CLOCKWISE CONTOUR INTEGRAL
    0x2233: [824,320,499,32,639],      // ANTICLOCKWISE CONTOUR INTEGRAL
    0x2234: [521,16,620,38,582],       // THEREFORE
    0x2235: [521,16,620,38,582],       // BECAUSE
    0x2236: [521,13,511,192,319],      // RATIO
    0x2237: [521,13,685,82,602],       // PROPORTION
    0x2238: [511,-220,685,48,637],     // DOT MINUS
    0x2239: [511,5,685,48,637],        // EXCESS
    0x223A: [511,5,685,48,637],        // GEOMETRIC PROPORTION
    0x223B: [521,13,685,48,637],       // HOMOTHETIC
    0x223D: [362,-148,685,48,637],     // REVERSED TILDE
    0x223E: [413,-90,685,48,637],      // stix-most positive
    0x223F: [467,-39,685,49,637],      // stix-reverse sine wave
    0x2241: [424,-88,685,48,637],      // stix-not, vert, similar
    0x2242: [445,-55,685,48,637],      // MINUS TILDE
    0x2244: [519,35,685,48,637],       // stix-not (vert) similar or equal
    0x2246: [604,107,685,47,637],      // APPROXIMATELY BUT NOT ACTUALLY EQUAL TO
    0x2247: [647,202,685,48,637],      // stix-not (vert) similar over two-line equals
    0x2249: [549,49,685,48,637],       // stix-not, vert, approximate
    0x224A: [552,45,685,48,637],       // ALMOST EQUAL OR EQUAL TO
    0x224B: [532,26,685,48,638],       // TRIPLE TILDE
    0x224C: [532,27,685,48,637],       // stix-all equal to (lazy S over equals) (formerly 224C; that shape changed)
    0x224E: [471,-35,685,48,637],      // GEOMETRICALLY EQUIVALENT TO
    0x224F: [471,-120,685,48,637],     // DIFFERENCE BETWEEN
    0x2251: [611,106,685,48,637],      // GEOMETRICALLY EQUAL TO
    0x2252: [611,105,685,48,637],      // APPROXIMATELY EQUAL TO OR THE IMAGE OF
    0x2253: [611,106,685,48,637],      // IMAGE OF OR APPROXIMATELY EQUAL TO
    0x2254: [417,-89,824,48,776],      // COLON EQUALS
    0x2255: [417,-89,824,48,776],      // EQUALS COLON
    0x2256: [416,-90,685,48,637],      // RING IN EQUAL TO
    0x2257: [752,-120,685,48,637],     // RING EQUAL TO
    0x2258: [729,-120,685,48,637],     // CORRESPONDS TO
    0x2259: [853,-120,685,48,637],     // ESTIMATES
    0x225A: [853,-120,685,48,637],     // EQUIANGULAR TO
    0x225B: [756,-120,685,48,637],     // STAR EQUALS
    0x225C: [853,-120,685,48,637],     // DELTA EQUAL TO
    0x225D: [823,-120,685,7,678],      // EQUAL TO BY DEFINITION
    0x225E: [703,-120,685,48,637],     // MEASURED BY
    0x225F: [863,-120,685,48,637],     // QUESTIONED EQUAL TO
    0x2262: [662,156,685,48,637],      // stix-not (vert) three-line equals
    0x2263: [544,38,685,48,637],       // STRICTLY EQUIVALENT TO
    0x2266: [718,211,685,57,622],      // LESS-THAN OVER EQUAL TO
    0x2267: [718,211,685,57,622],      // GREATER-THAN OVER EQUAL TO
    0x2268: [746,260,685,56,621],      // stix-less, vert, not double equals
    0x2269: [746,260,685,56,621],      // stix-gt, vert, not double equals
    0x226C: [730,224,466,85,381],      // BETWEEN
    0x226D: [572,66,685,48,637],       // stix-not (vert) asymptotically equal to
    0x226E: [662,156,685,56,621],      // stix-not, vert, less-than
    0x226F: [662,156,685,56,621],      // stix-not, vert, greater-than
    0x2270: [730,229,685,56,621],      // stix-not, vert, less-than-or-equal
    0x2271: [730,229,685,56,622],      // stix-not, vert, greater-than-or-equal
    0x2272: [664,164,685,48,637],      // stix-less-than or (contour) similar
    0x2273: [664,164,685,48,637],      // stix-greater-than or (contour) similar
    0x2274: [731,228,685,48,637],      // stix-not, vert, less, similar
    0x2275: [730,229,685,48,637],      // stix-not, vert, greater, similar
    0x2276: [705,204,685,56,621],      // LESS-THAN OR GREATER-THAN
    0x2277: [705,204,685,56,621],      // GREATER-THAN OR LESS-THAN
    0x2278: [750,250,685,48,637],      // stix-not, vert, less, greater
    0x2279: [750,250,685,48,637],      // stix-not, vert, greater, less
    0x227E: [664,164,685,48,637],      // PRECEDES OR EQUIVALENT TO
    0x227F: [664,164,685,48,637],      // SUCCEEDS OR EQUIVALENT TO
    0x2280: [662,156,685,64,621],      // DOES NOT PRECEDE
    0x2281: [662,156,685,64,621],      // stix-not (vert) succeeds
    0x2284: [662,156,685,65,623],      // stix-not subset [vertical negation]
    0x2285: [662,156,685,65,623],      // stix-not superset [vertical negation]
    0x2288: [730,229,685,64,621],      // stix-/nsubseteq N: not (vert) subset, equals
    0x2289: [730,229,685,64,621],      // stix-/nsupseteq N: not (vert) superset, equals
    0x228A: [627,216,685,64,621],      // stix-subset, not equals, variant
    0x228B: [627,216,685,64,621],      // stix-superset, not equals, variant
    0x228C: [536,31,620,48,572],       // MULTISET
    0x228D: [536,31,620,48,572],       // MULTISET MULTIPLICATION
    0x228F: [531,25,685,64,621],       // SQUARE IMAGE OF
    0x2290: [531,25,685,64,621],       // SQUARE ORIGINAL OF
    0x229A: [623,119,842,50,792],      // CIRCLED RING OPERATOR
    0x229B: [623,119,842,50,792],      // CIRCLED ASTERISK OPERATOR
    0x229C: [623,119,842,50,792],      // stix-two horizontal bars in circle
    0x229D: [623,119,842,50,792],      // CIRCLED DASH
    0x229E: [662,158,910,45,865],      // SQUARED PLUS
    0x229F: [662,158,910,45,865],      // SQUARED MINUS
    0x22A0: [662,158,910,45,865],      // SQUARED TIMES
    0x22A1: [662,157,910,45,865],      // SQUARED DOT OPERATOR
    0x22A6: [662,0,497,64,433],        // ASSERTION
    0x22A7: [662,0,498,64,434],        // MODELS
    0x22A9: [662,0,860,57,814],        // FORCES
    0x22AA: [662,0,860,45,815],        // TRIPLE VERTICAL BAR RIGHT TURNSTILE
    0x22AB: [662,0,860,57,814],        // DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
    0x22AC: [662,0,786,9,723],         // DOES NOT PROVE
    0x22AD: [662,0,786,9,723],         // NOT TRUE
    0x22AE: [662,0,968,9,922],         // DOES NOT FORCE
    0x22AF: [662,0,968,9,922],         // NEGATED DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
    0x22B0: [551,45,685,64,621],       // PRECEDES UNDER RELATION
    0x22B1: [551,45,685,64,621],       // SUCCEEDS UNDER RELATION
    0x22B2: [531,25,685,24,631],       // NORMAL SUBGROUP OF
    0x22B3: [531,25,685,54,661],       // CONTAINS AS NORMAL SUBGROUP
    0x22B4: [607,103,685,24,631],      // NORMAL SUBGROUP OF OR EQUAL TO
    0x22B5: [607,103,685,54,661],      // CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
    0x22B6: [403,-103,1145,50,1095],   // ORIGINAL OF
    0x22B7: [403,-103,1145,50,1095],   // IMAGE OF
    0x22B8: [403,-103,849,50,799],     // MULTIMAP
    0x22B9: [547,41,685,48,636],       // HERMITIAN CONJUGATE MATRIX
    0x22BA: [450,212,480,74,406],      // INTERCALATE
    0x22BB: [536,139,620,32,590],      // XOR
    0x22BC: [646,29,620,32,590],       // NAND
    0x22BD: [646,29,620,32,590],       // NOR
    0x22BE: [584,0,685,50,634],        // RIGHT ANGLE WITH ARC
    0x22BF: [662,158,911,45,865],      // RIGHT TRIANGLE
    0x22C7: [545,38,685,51,634],       // DIVISION TIMES
    0x22C9: [582,80,810,93,716],       // LEFT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22CA: [582,80,810,93,716],       // RIGHT NORMAL FACTOR SEMIDIRECT PRODUCT
    0x22CB: [582,80,810,74,736],       // LEFT SEMIDIRECT PRODUCT
    0x22CC: [582,80,810,74,736],       // RIGHT SEMIDIRECT PRODUCT
    0x22CD: [445,-55,685,48,637],      // REVERSED TILDE EQUALS
    0x22CE: [532,25,580,31,549],       // CURLY LOGICAL OR
    0x22CF: [532,25,580,31,549],       // CURLY LOGICAL AND
    0x22D0: [531,25,685,64,621],       // DOUBLE SUBSET
    0x22D1: [531,25,685,64,621],       // DOUBLE SUPERSET
    0x22D2: [536,31,620,48,572],       // DOUBLE INTERSECTION
    0x22D3: [536,31,620,48,572],       // DOUBLE UNION
    0x22D4: [631,31,620,48,572],       // PITCHFORK
    0x22D5: [690,189,685,48,637],      // EQUAL AND PARALLEL TO
    0x22D6: [534,24,685,56,621],       // LESS-THAN WITH DOT
    0x22D7: [534,24,685,56,621],       // GREATER-THAN WITH DOT
    0x22D8: [534,24,1274,45,1229],     // VERY MUCH LESS-THAN
    0x22D9: [534,24,1274,45,1229],     // VERY MUCH GREATER-THAN
    0x22DA: [830,324,685,56,621],      // stix-less, equal, slanted, greater
    0x22DB: [830,324,685,56,621],      // stix-greater, equal, slanted, less
    0x22DC: [607,103,685,64,621],      // EQUAL TO OR LESS-THAN
    0x22DD: [607,103,685,64,621],      // EQUAL TO OR GREATER-THAN
    0x22DE: [627,121,685,64,621],      // EQUAL TO OR PRECEDES
    0x22DF: [627,121,685,64,621],      // EQUAL TO OR SUCCEEDS
    0x22E0: [730,229,685,64,621],      // stix-not (vert) precedes or contour equals
    0x22E1: [730,229,685,64,621],      // stix-not (vert) succeeds or contour equals
    0x22E2: [730,229,685,65,622],      // NOT SQUARE IMAGE OF OR EQUAL TO
    0x22E3: [730,229,685,65,622],      // NOT SQUARE ORIGINAL OF OR EQUAL TO
    0x22E4: [627,216,685,64,621],      // SQUARE IMAGE OF OR NOT EQUAL TO
    0x22E5: [627,216,685,64,621],      // SQUARE ORIGINAL OF OR NOT EQUAL TO
    0x22E6: [669,279,685,48,637],      // LESS-THAN BUT NOT EQUIVALENT TO
    0x22E7: [669,279,685,48,637],      // GREATER-THAN BUT NOT EQUIVALENT TO
    0x22E8: [670,279,685,48,637],      // PRECEDES BUT NOT EQUIVALENT TO
    0x22E9: [670,279,685,48,637],      // SUCCEEDS BUT NOT EQUIVALENT TO
    0x22EA: [662,156,635,24,581],      // NOT NORMAL SUBGROUP OF
    0x22EB: [662,156,635,54,611],      // DOES NOT CONTAIN AS NORMAL SUBGROUP
    0x22EC: [730,229,635,24,581],      // stix-not, vert, left triangle, equals
    0x22ED: [730,229,635,54,611],      // stix-not, vert, right triangle, equals
    0x22F0: [520,18,926,194,732],      // UP RIGHT DIAGONAL ELLIPSIS
    0x22F2: [531,27,823,55,763],       // ELEMENT OF WITH LONG HORIZONTAL STROKE
    0x22F3: [531,27,685,60,625],       // ELEMENT OF WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    0x22F4: [459,-45,486,62,420],      // SMALL ELEMENT OF WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    0x22F5: [716,27,685,60,625],       // ELEMENT OF WITH DOT ABOVE
    0x22F6: [685,27,685,60,625],       // ELEMENT OF WITH OVERBAR
    0x22F7: [613,-45,486,62,420],      // SMALL ELEMENT OF WITH OVERBAR
    0x22F8: [532,180,685,60,625],      // ELEMENT OF WITH UNDERBAR
    0x22F9: [531,27,685,61,625],       // ELEMENT OF WITH TWO HORIZONTAL STROKES
    0x22FA: [531,27,823,55,763],       // CONTAINS WITH LONG HORIZONTAL STROKE
    0x22FB: [531,27,685,59,624],       // CONTAINS WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    0x22FC: [459,-45,486,62,420],      // SMALL CONTAINS WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    0x22FD: [685,27,685,61,626],       // CONTAINS WITH OVERBAR
    0x22FE: [613,-45,486,67,425],      // SMALL CONTAINS WITH OVERBAR
    0x22FF: [662,158,910,45,865]       // Z NOTATION BAG MEMBERSHIP
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MathOperators.js");
