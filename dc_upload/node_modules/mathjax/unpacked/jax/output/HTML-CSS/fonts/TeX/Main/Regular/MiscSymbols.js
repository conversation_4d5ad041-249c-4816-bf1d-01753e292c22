/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/MiscSymbols.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['MathJax_Main'],
  {
    0x2660: [727,130,778,55,723],      // BLACK SPADE SUIT
    0x2661: [716,33,778,55,723],       // WHITE HEART SUIT
    0x2662: [727,162,778,55,723],      // WHITE DIAMOND SUIT
    0x2663: [726,130,778,28,750],      // BLACK CLUB SUIT
    0x266D: [750,22,389,55,332],       // MUSIC FLAT SIGN
    0x266E: [734,223,389,65,324],      // MUSIC NATURAL SIGN
    0x266F: [723,223,389,55,333]       // MUSIC SHARP SIGN
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/Main/Regular/MiscSymbols.js");
