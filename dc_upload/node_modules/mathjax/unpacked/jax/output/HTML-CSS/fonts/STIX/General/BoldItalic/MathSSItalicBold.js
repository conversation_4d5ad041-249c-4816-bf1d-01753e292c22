/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/MathSSItalicBold.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold-italic'],
  {
    0x1D63C: [690,0,690,25,665],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL A
    0x1D63D: [676,0,636,80,691],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL B
    0x1D63E: [691,19,723,119,797],     // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL C
    0x1D63F: [676,0,709,80,772],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL D
    0x1D640: [676,0,635,80,728],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL E
    0x1D641: [676,0,582,80,725],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL F
    0x1D642: [691,19,746,107,785],     // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL G
    0x1D643: [676,0,715,80,803],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL H
    0x1D644: [676,0,440,79,534],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL I
    0x1D645: [676,96,481,15,574],      // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL J
    0x1D646: [676,0,712,80,816],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL K
    0x1D647: [676,0,603,80,612],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL L
    0x1D648: [676,0,913,80,1001],      // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL M
    0x1D649: [676,18,724,80,812],      // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL N
    0x1D64A: [692,18,778,106,840],     // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL O
    0x1D64B: [676,0,581,80,695],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL P
    0x1D64C: [691,176,779,105,839],    // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL Q
    0x1D64D: [676,0,670,80,698],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL R
    0x1D64E: [691,19,554,66,637],      // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL S
    0x1D64F: [676,0,641,157,785],      // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL T
    0x1D650: [676,19,699,123,792],     // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL U
    0x1D651: [676,18,690,193,833],     // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL V
    0x1D652: [676,15,997,198,1135],    // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL W
    0x1D653: [676,0,740,40,853],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL X
    0x1D654: [676,0,694,188,842],      // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL Y
    0x1D655: [676,0,653,25,769],       // MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL Z
    0x1D656: [473,14,489,48,507],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL A
    0x1D657: [676,13,512,51,558],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL B
    0x1D658: [473,14,462,71,524],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL C
    0x1D659: [676,14,518,69,625],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL D
    0x1D65A: [473,13,452,71,492],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL E
    0x1D65B: [692,0,340,72,533],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL F
    0x1D65C: [473,206,504,2,599],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL G
    0x1D65D: [676,0,510,55,542],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL H
    0x1D65E: [688,0,245,59,366],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL I
    0x1D65F: [688,202,324,-90,440],    // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL J
    0x1D660: [676,0,519,55,599],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL K
    0x1D661: [676,0,235,55,348],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL L
    0x1D662: [473,0,776,55,809],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL M
    0x1D663: [473,0,510,55,542],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL N
    0x1D664: [473,14,501,72,542],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL O
    0x1D665: [473,205,512,3,559],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL P
    0x1D666: [473,205,512,69,574],     // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL Q
    0x1D667: [473,0,411,55,519],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL R
    0x1D668: [473,13,385,37,442],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL S
    0x1D669: [631,12,386,98,447],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL T
    0x1D66A: [462,15,518,81,569],      // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL U
    0x1D66B: [462,14,462,129,561],     // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL V
    0x1D66C: [462,14,701,131,798],     // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL W
    0x1D66D: [462,0,506,20,582],       // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL X
    0x1D66E: [462,204,472,-27,569],    // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL Y
    0x1D66F: [462,0,441,21,530]        // MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL Z
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/BoldItalic/MathSSItalicBold.js");
