/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SupplementalArrowsA.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x27F0: [662,156,1033,69,965],     // UPWARDS QUADRUPLE ARROW
    0x27F1: [662,156,1033,69,965],     // DOWNWARDS QUADRUPLE ARROW
    0x27F2: [626,116,974,54,882],      // ANTICLOCKWISE GAPPED CIRCLE ARROW
    0x27F3: [626,116,974,92,920],      // CLOCKWISE GAPPED CIRCLE ARROW
    0x27F4: [569,61,1200,52,1147],     // RIGHT ARROW WITH CIRCLED PLUS
    0x27FD: [551,45,1574,55,1519],     // LONG LEFTWARDS DOUBLE ARROW FROM BAR
    0x27FE: [551,45,1574,55,1519],     // LONG RIGHTWARDS DOUBLE ARROW FROM BAR
    0x27FF: [449,-58,1574,55,1519]     // LONG RIGHTWARDS SQUIGGLE ARROW
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/SupplementalArrowsA.js");
