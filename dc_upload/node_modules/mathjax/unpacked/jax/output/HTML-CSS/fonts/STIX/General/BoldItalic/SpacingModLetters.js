/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold-italic'],
  {
    0x2B0: [852,-328,380,7,365],       // MODIFIER LETTER SMALL H
    0x2B1: [841,-329,380,7,365],       // MODIFIER LETTER SMALL H WITH HOOK
    0x2B2: [862,-176,350,24,384],      // MODIFIER LETTER SMALL J
    0x2B3: [690,-344,389,21,384],      // MODIFIER LETTER SMALL R
    0x2B4: [690,-344,389,2,365],       // MODIFIER LETTER SMALL TURNED R
    0x2B5: [690,-171,389,2,371],       // MODIFIER LETTER SMALL TURNED R WITH HOOK
    0x2B6: [684,-345,390,5,466],       // MODIFIER LETTER SMALL CAPITAL INVERTED R
    0x2B7: [690,-331,450,15,467],      // MODIFIER LETTER SMALL W
    0x2B8: [690,-176,350,11,386],      // MODIFIER LETTER SMALL Y
    0x2BB: [685,-369,333,128,332],     // MODIFIER LETTER TURNED COMMA
    0x2C0: [690,-240,343,-3,323],      // MODIFIER LETTER GLOTTAL STOP
    0x2C1: [690,-240,326,20,364],      // MODIFIER LETTER REVERSED GLOTTAL STOP
    0x2C6: [690,-516,333,40,367],      // MODIFIER LETTER CIRCUMFLEX ACCENT
    0x2C7: [690,-516,333,79,411],      // CARON
    0x2D8: [678,-516,333,71,387],      // BREVE
    0x2D9: [655,-525,333,163,293],     // DOT ABOVE
    0x2DA: [754,-541,333,127,340],     // RING ABOVE
    0x2DB: [44,173,333,-40,189],       // OGONEK
    0x2DC: [655,-536,333,48,407],      // SMALL TILDE
    0x2DD: [697,-516,333,69,498],      // DOUBLE ACUTE ACCENT
    0x2E0: [684,-190,379,14,423],      // MODIFIER LETTER SMALL GAMMA
    0x2E1: [857,-329,222,2,217],       // MODIFIER LETTER SMALL L
    0x2E2: [690,-331,280,8,274],       // MODIFIER LETTER SMALL S
    0x2E3: [690,-335,389,3,387],       // MODIFIER LETTER SMALL X
    0x2E4: [849,-329,328,9,364],       // MODIFIER LETTER SMALL REVERSED GLOTTAL STOP
    0x2EC: [70,167,314,5,309],         // MODIFIER LETTER VOICING
    0x2ED: [720,-528,395,5,390],       // MODIFIER LETTER UNASPIRATED
    0x2F7: [-108,227,333,-74,285]      // ??
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/BoldItalic/SpacingModLetters.js");
