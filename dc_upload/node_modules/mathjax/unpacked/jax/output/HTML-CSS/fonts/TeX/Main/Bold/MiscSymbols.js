/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MiscSymbols.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['MathJax_Main-bold'],
  {
    0x2660: [719,129,894,64,829],      // BLACK SPADE SUIT
    0x2661: [711,24,894,65,828],       // WHITE HEART SUIT
    0x2662: [719,154,894,64,828],      // WHITE DIAMOND SUIT
    0x2663: [719,129,894,32,861],      // BLACK CLUB SUIT
    0x266D: [750,17,447,64,381],       // MUSIC FLAT SIGN
    0x266E: [741,223,447,57,389],      // MUSIC NATURAL SIGN
    0x266F: [724,224,447,63,382]       // MUSIC SHARP SIGN
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/Main/Bold/MiscSymbols.js");
