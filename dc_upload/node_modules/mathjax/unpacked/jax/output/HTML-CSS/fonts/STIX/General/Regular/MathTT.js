/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathTT.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x1D670: [673,0,525,26,496],       // MATHEMATICAL MONOSPACE CAPITAL A
    0x1D671: [662,0,525,29,480],       // MATHEMATICAL MONOSPACE CAPITAL B
    0x1D672: [672,11,525,40,482],      // MATHEMATICAL MONOSPACE CAPITAL C
    0x1D673: [662,0,525,25,483],       // MATHEMATICAL MONOSPACE CAPITAL D
    0x1D674: [662,0,525,31,500],       // MATHEMATICAL MONOSPACE CAPITAL E
    0x1D675: [662,0,525,34,488],       // MATHEMATICAL MONOSPACE CAPITAL F
    0x1D676: [672,11,525,37,495],      // MATHEMATICAL MONOSPACE CAPITAL G
    0x1D677: [662,0,525,26,496],       // MATHEMATICAL MONOSPACE CAPITAL H
    0x1D678: [662,0,525,84,438],       // MATHEMATICAL MONOSPACE CAPITAL I
    0x1D679: [662,11,525,85,476],      // MATHEMATICAL MONOSPACE CAPITAL J
    0x1D67A: [662,0,525,30,494],       // MATHEMATICAL MONOSPACE CAPITAL K
    0x1D67B: [662,0,525,37,487],       // MATHEMATICAL MONOSPACE CAPITAL L
    0x1D67C: [662,0,525,21,501],       // MATHEMATICAL MONOSPACE CAPITAL M
    0x1D67D: [662,0,525,31,491],       // MATHEMATICAL MONOSPACE CAPITAL N
    0x1D67E: [672,11,525,56,466],      // MATHEMATICAL MONOSPACE CAPITAL O
    0x1D67F: [662,0,525,31,479],       // MATHEMATICAL MONOSPACE CAPITAL P
    0x1D680: [672,139,525,56,466],     // MATHEMATICAL MONOSPACE CAPITAL Q
    0x1D681: [662,11,525,26,520],      // MATHEMATICAL MONOSPACE CAPITAL R
    0x1D682: [672,11,525,52,470],      // MATHEMATICAL MONOSPACE CAPITAL S
    0x1D683: [662,0,525,26,496],       // MATHEMATICAL MONOSPACE CAPITAL T
    0x1D684: [662,11,525,9,514],       // MATHEMATICAL MONOSPACE CAPITAL U
    0x1D685: [662,8,525,17,506],       // MATHEMATICAL MONOSPACE CAPITAL V
    0x1D686: [662,8,525,11,512],       // MATHEMATICAL MONOSPACE CAPITAL W
    0x1D687: [662,0,525,24,497],       // MATHEMATICAL MONOSPACE CAPITAL X
    0x1D688: [662,0,525,15,507],       // MATHEMATICAL MONOSPACE CAPITAL Y
    0x1D689: [662,0,525,47,479],       // MATHEMATICAL MONOSPACE CAPITAL Z
    0x1D68A: [459,6,525,58,516],       // MATHEMATICAL MONOSPACE SMALL A
    0x1D68B: [609,6,525,17,481],       // MATHEMATICAL MONOSPACE SMALL B
    0x1D68C: [459,6,525,78,464],       // MATHEMATICAL MONOSPACE SMALL C
    0x1D68D: [609,6,525,41,505],       // MATHEMATICAL MONOSPACE SMALL D
    0x1D68E: [459,6,525,60,462],       // MATHEMATICAL MONOSPACE SMALL E
    0x1D68F: [615,0,525,42,437],       // MATHEMATICAL MONOSPACE SMALL F
    0x1D690: [461,228,525,29,508],     // MATHEMATICAL MONOSPACE SMALL G
    0x1D691: [609,0,525,17,505],       // MATHEMATICAL MONOSPACE SMALL H
    0x1D692: [610,0,525,84,448],       // MATHEMATICAL MONOSPACE SMALL I
    0x1D693: [610,227,525,47,362],     // MATHEMATICAL MONOSPACE SMALL J
    0x1D694: [609,0,525,24,505],       // MATHEMATICAL MONOSPACE SMALL K
    0x1D695: [609,0,525,63,459],       // MATHEMATICAL MONOSPACE SMALL L
    0x1D696: [456,0,525,2,520],        // MATHEMATICAL MONOSPACE SMALL M
    0x1D697: [456,0,525,17,505],       // MATHEMATICAL MONOSPACE SMALL N
    0x1D698: [459,6,525,62,460],       // MATHEMATICAL MONOSPACE SMALL O
    0x1D699: [456,221,525,17,481],     // MATHEMATICAL MONOSPACE SMALL P
    0x1D69A: [456,221,525,45,530],     // MATHEMATICAL MONOSPACE SMALL Q
    0x1D69B: [456,0,525,37,485],       // MATHEMATICAL MONOSPACE SMALL R
    0x1D69C: [459,6,525,72,457],       // MATHEMATICAL MONOSPACE SMALL S
    0x1D69D: [580,6,525,25,448],       // MATHEMATICAL MONOSPACE SMALL T
    0x1D69E: [450,6,525,17,505],       // MATHEMATICAL MONOSPACE SMALL U
    0x1D69F: [450,4,525,22,500],       // MATHEMATICAL MONOSPACE SMALL V
    0x1D6A0: [450,4,525,15,508],       // MATHEMATICAL MONOSPACE SMALL W
    0x1D6A1: [450,0,525,23,498],       // MATHEMATICAL MONOSPACE SMALL X
    0x1D6A2: [450,227,525,24,501],     // MATHEMATICAL MONOSPACE SMALL Y
    0x1D6A3: [450,0,525,32,473],       // MATHEMATICAL MONOSPACE SMALL Z
    0x1D7F7: [681,0,525,110,435],      // MATHEMATICAL MONOSPACE DIGIT ONE
    0x1D7F8: [681,0,525,52,470],       // MATHEMATICAL MONOSPACE DIGIT TWO
    0x1D7F9: [681,11,525,43,479],      // MATHEMATICAL MONOSPACE DIGIT THREE
    0x1D7FA: [682,0,525,29,493],       // MATHEMATICAL MONOSPACE DIGIT FOUR
    0x1D7FB: [670,11,525,52,470],      // MATHEMATICAL MONOSPACE DIGIT FIVE
    0x1D7FC: [681,11,525,58,464],      // MATHEMATICAL MONOSPACE DIGIT SIX
    0x1D7FD: [686,11,525,43,479],      // MATHEMATICAL MONOSPACE DIGIT SEVEN
    0x1D7FE: [681,11,525,43,479],      // MATHEMATICAL MONOSPACE DIGIT EIGHT
    0x1D7FF: [681,11,525,58,464]       // MATHEMATICAL MONOSPACE DIGIT NINE
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MathTT.js");
