/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SuppMathOperators.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2A07: [763,259,1180,83,1097],    // TWO LOGICAL AND OPERATOR
    0x2A08: [763,259,1180,83,1097],    // TWO LOGICAL OR OPERATOR
    0x2A09: [763,259,1021,50,971],     // N-ARY TIMES OPERATOR
    0x2A0A: [763,259,914,58,856],      // MODULO TWO SUM
    0x2A0B: [824,320,690,33,659],      // SUMMATION WITH INTEGRAL
    0x2A0C: [824,320,1184,32,1364],    // QUADRUPLE INTEGRAL OPERATOR
    0x2A0D: [824,320,499,32,639],      // FINITE PART INTEGRAL
    0x2A0E: [824,320,499,32,639],      // INTEGRAL WITH DOUBLE STROKE
    0x2A0F: [824,320,499,32,639],      // INTEGRAL AVERAGE WITH SLASH
    0x2A10: [824,320,499,32,639],      // CIRCULATION FUNCTION
    0x2A11: [824,320,499,32,639],      // ANTICLOCKWISE INTEGRATION
    0x2A12: [824,320,519,32,639],      // LINE INTEGRATION WITH RECTANGULAR PATH AROUND POLE
    0x2A13: [824,320,499,32,639],      // LINE INTEGRATION WITH SEMICIRCULAR PATH AROUND POLE
    0x2A14: [824,320,628,32,688],      // LINE INTEGRATION NOT INCLUDING THE POLE
    0x2A15: [824,320,499,32,639],      // INTEGRAL AROUND A POINT OPERATOR
    0x2A16: [824,320,529,32,639],      // QUATERNION INTEGRAL OPERATOR
    0x2A17: [824,320,738,32,818],      // INTEGRAL WITH LEFTWARDS ARROW WITH HOOK
    0x2A18: [824,320,539,32,639],      // INTEGRAL WITH TIMES SIGN
    0x2A19: [824,320,559,32,639],      // INTEGRAL WITH INTERSECTION
    0x2A1A: [824,320,559,32,639],      // INTEGRAL WITH UNION
    0x2A1B: [947,320,459,32,639],      // INTEGRAL WITH OVERBAR
    0x2A1C: [824,443,459,32,639],      // INTEGRAL WITH UNDERBAR
    0x2A1D: [770,252,1270,93,1177],    // JOIN
    0x2A1E: [764,258,1018,45,924],     // LARGE LEFT TRIANGLE OPERATOR
    0x2A1F: [566,291,503,110,410],     // Z NOTATION SCHEMA COMPOSITION
    0x2A20: [633,127,1177,98,1079],    // Z NOTATION SCHEMA PIPING
    0x2A21: [805,300,547,215,472],     // Z NOTATION SCHEMA PROJECTION
    0x2A22: [819,41,685,48,636],       // PLUS SIGN WITH SMALL CIRCLE ABOVE
    0x2A23: [707,41,685,48,636],       // PLUS SIGN WITH CIRCUMFLEX ACCENT ABOVE
    0x2A24: [704,41,685,48,636],       // PLUS SIGN WITH TILDE ABOVE
    0x2A25: [547,235,685,48,636],      // PLUS SIGN WITH DOT BELOW
    0x2A26: [547,198,685,48,636],      // PLUS SIGN WITH TILDE BELOW
    0x2A27: [547,210,685,41,673],      // PLUS SIGN WITH SUBSCRIPT TWO
    0x2A28: [547,41,685,48,636],       // PLUS SIGN WITH BLACK TRIANGLE
    0x2A29: [556,-220,685,48,637],     // MINUS SIGN WITH COMMA ABOVE
    0x2A2A: [286,5,685,48,637],        // MINUS SIGN WITH DOT BELOW
    0x2A2B: [511,5,685,48,637],        // MINUS SIGN WITH FALLING DOTS
    0x2A2C: [511,5,685,48,637],        // MINUS SIGN WITH RISING DOTS
    0x2A2D: [623,119,724,50,674],      // PLUS SIGN IN LEFT HALF CIRCLE
    0x2A2E: [623,119,724,50,674],      // PLUS SIGN IN RIGHT HALF CIRCLE
    0x2A2F: [447,-59,490,50,439],      // VECTOR OR CROSS PRODUCT
    0x2A30: [686,25,640,43,597],       // MULTIPLICATION SIGN WITH DOT ABOVE
    0x2A31: [529,130,640,43,597],      // MULTIPLICATION SIGN WITH UNDERBAR
    0x2A32: [529,45,640,43,597],       // SEMIDIRECT PRODUCT WITH BOTTOM CLOSED
    0x2A33: [538,32,685,57,627],       // SMASH PRODUCT
    0x2A34: [623,119,674,50,624],      // MULTIPLICATION SIGN IN LEFT HALF CIRCLE
    0x2A35: [623,119,674,50,624],      // MULTIPLICATION SIGN IN RIGHT HALF CIRCLE
    0x2A36: [810,119,842,50,792],      // CIRCLED MULTIPLICATION SIGN WITH CIRCUMFLEX ACCENT
    0x2A37: [752,248,1100,50,1050],    // MULTIPLICATION SIGN IN DOUBLE CIRCLE
    0x2A38: [623,119,842,50,792],      // CIRCLED DIVISION SIGN
    0x2A39: [811,127,1145,35,1110],    // PLUS SIGN IN TRIANGLE
    0x2A3A: [811,127,1145,35,1110],    // MINUS SIGN IN TRIANGLE
    0x2A3B: [811,127,1145,35,1110],    // MULTIPLICATION SIGN IN TRIANGLE
    0x2A3C: [393,-115,600,48,552],     // stix-vert, low bar to left from base
    0x2A3D: [393,-115,600,48,552],     // stix-vert, low bar to right from base
    0x2A3E: [488,170,300,60,230],      // Z NOTATION RELATIONAL COMPOSITION
    0x2A40: [536,31,620,48,572],       // INTERSECTION WITH DOT
    0x2A41: [536,31,620,48,572],       // UNION WITH MINUS SIGN
    0x2A42: [668,31,620,48,572],       // UNION WITH OVERBAR
    0x2A43: [668,31,620,48,572],       // INTERSECTION WITH OVERBAR
    0x2A44: [536,31,620,48,572],       // INTERSECTION WITH LOGICAL AND
    0x2A45: [536,31,620,48,572],       // UNION WITH LOGICAL OR
    0x2A46: [914,406,620,48,572],      // UNION ABOVE INTERSECTION
    0x2A47: [914,406,620,48,572],      // INTERSECTION ABOVE UNION
    0x2A48: [914,406,620,48,572],      // UNION ABOVE BAR ABOVE INTERSECTION
    0x2A49: [914,406,620,48,572],      // INTERSECTION ABOVE BAR ABOVE UNION
    0x2A4A: [528,39,1078,48,1030],     // UNION BESIDE AND JOINED WITH UNION
    0x2A4B: [527,40,1078,48,1030],     // INTERSECTION BESIDE AND JOINED WITH INTERSECTION
    0x2A4C: [602,31,620,10,610],       // CLOSED UNION WITH SERIFS
    0x2A4D: [536,97,620,10,610],       // CLOSED INTERSECTION WITH SERIFS
    0x2A4E: [536,31,620,48,572],       // DOUBLE SQUARE INTERSECTION
    0x2A4F: [536,31,620,48,572],       // DOUBLE SQUARE UNION
    0x2A50: [602,31,620,10,610],       // CLOSED UNION WITH SERIFS AND SMASH PRODUCT
    0x2A51: [710,29,620,31,589],       // LOGICAL AND WITH DOT ABOVE
    0x2A52: [710,29,620,31,589],       // LOGICAL OR WITH DOT ABOVE
    0x2A53: [536,29,620,31,589],       // DOUBLE LOGICAL AND
    0x2A54: [536,29,620,31,589],       // DOUBLE LOGICAL OR
    0x2A55: [536,29,780,32,748],       // TWO INTERSECTING LOGICAL AND
    0x2A56: [536,29,780,32,748],       // TWO INTERSECTING LOGICAL OR
    0x2A57: [536,29,706,106,683],      // SLOPING LARGE OR
    0x2A58: [536,29,706,23,600],       // SLOPING LARGE AND
    0x2A59: [585,77,620,31,589],       // LOGICAL OR OVERLAPPING LOGICAL AND
    0x2A5A: [536,29,620,31,589],       // LOGICAL AND WITH MIDDLE STEM
    0x2A5B: [536,29,620,31,589],       // LOGICAL OR WITH MIDDLE STEM
    0x2A5C: [536,29,620,31,589],       // LOGICAL AND WITH HORIZONTAL DASH
    0x2A5D: [536,29,620,31,589],       // LOGICAL OR WITH HORIZONTAL DASH
    0x2A5E: [796,29,620,31,589],       // LOGICAL AND WITH DOUBLE OVERBAR
    0x2A5F: [536,139,620,30,590],      // LOGICAL AND WITH UNDERBAR
    0x2A60: [536,289,620,30,590],      // LOGICAL AND WITH DOUBLE UNDERBAR
    0x2A61: [479,0,620,45,575],        // SMALL VEE WITH UNDERBAR
    0x2A62: [806,29,620,30,590],       // LOGICAL OR WITH DOUBLE OVERBAR
    0x2A63: [536,289,620,30,590],      // LOGICAL OR WITH DOUBLE UNDERBAR
    0x2A64: [791,284,1043,70,1008],    // Z NOTATION DOMAIN ANTIRESTRICTION
    0x2A65: [791,284,1043,70,1008],    // Z NOTATION RANGE ANTIRESTRICTION
    0x2A66: [386,105,685,48,637],      // EQUALS SIGN WITH DOT BELOW
    0x2A67: [703,-28,685,48,637],      // IDENTICAL WITH DOT ABOVE
    0x2A68: [695,189,685,48,637],      // TRIPLE HORIZONTAL BAR WITH DOUBLE VERTICAL STROKE
    0x2A69: [662,156,685,48,637],      // TRIPLE HORIZONTAL BAR WITH TRIPLE VERTICAL STROKE
    0x2A6A: [521,-148,685,48,637],     // TILDE OPERATOR WITH DOT ABOVE
    0x2A6B: [521,13,685,48,637],       // TILDE OPERATOR WITH RISING DOTS
    0x2A6C: [543,38,685,48,637],       // SIMILAR MINUS SIMILAR
    0x2A6D: [703,27,685,48,637],       // CONGRUENT WITH DOT ABOVE
    0x2A6E: [847,-120,685,48,637],     // EQUALS WITH ASTERISK
    0x2A6F: [707,-25,685,48,637],      // ALMOST EQUAL TO WITH CIRCUMFLEX ACCENT
    0x2A70: [650,146,685,48,637],      // APPROXIMATELY EQUAL OR EQUAL TO
    0x2A71: [648,141,685,48,637],      // EQUALS SIGN ABOVE PLUS SIGN
    0x2A72: [648,141,685,48,637],      // PLUS SIGN ABOVE EQUALS SIGN
    0x2A73: [532,27,685,48,637],       // EQUALS SIGN ABOVE TILDE OPERATOR
    0x2A74: [417,-89,1015,48,967],     // DOUBLE COLON EQUAL
    0x2A75: [386,-120,997,48,949],     // TWO CONSECUTIVE EQUALS SIGNS
    0x2A76: [386,-120,1436,48,1388],   // THREE CONSECUTIVE EQUALS SIGNS
    0x2A77: [611,106,685,48,637],      // EQUALS SIGN WITH TWO DOTS ABOVE AND TWO DOTS BELOW
    0x2A78: [703,-28,685,38,647],      // EQUIVALENT WITH FOUR DOTS ABOVE
    0x2A79: [532,26,685,44,609],       // LESS-THAN WITH CIRCLE INSIDE
    0x2A7A: [532,26,685,76,641],       // GREATER-THAN WITH CIRCLE INSIDE
    0x2A7B: [806,26,685,44,609],       // LESS-THAN WITH QUESTION MARK ABOVE
    0x2A7C: [806,26,685,76,641],       // GREATER-THAN WITH QUESTION MARK ABOVE
    0x2A7D: [625,137,685,56,621],      // LESS-THAN OR SLANTED EQUAL TO
    0x2A7E: [625,137,685,56,621],      // GREATER-THAN OR SLANTED EQUAL TO
    0x2A7F: [625,137,685,60,625],      // LESS-THAN OR SLANTED EQUAL TO WITH DOT INSIDE
    0x2A80: [625,137,685,60,625],      // GREATER-THAN OR SLANTED EQUAL TO WITH DOT INSIDE
    0x2A81: [625,137,685,60,625],      // LESS-THAN OR SLANTED EQUAL TO WITH DOT ABOVE
    0x2A82: [625,137,685,60,625],      // GREATER-THAN OR SLANTED EQUAL TO WITH DOT ABOVE
    0x2A83: [777,137,685,60,625],      // LESS-THAN OR SLANTED EQUAL TO WITH DOT ABOVE RIGHT
    0x2A84: [777,137,685,60,625],      // GREATER-THAN OR SLANTED EQUAL TO WITH DOT ABOVE LEFT
    0x2A85: [746,275,685,48,637],      // LESS-THAN OR APPROXIMATE
    0x2A86: [746,275,685,48,637],      // GREATER-THAN OR APPROXIMATE
    0x2A87: [628,216,685,60,625],      // LESS-THAN AND SINGLE-LINE NOT EQUAL TO
    0x2A88: [628,216,687,56,621],      // GREATER-THAN AND SINGLE-LINE NOT EQUAL TO
    0x2A89: [746,309,685,48,637],      // LESS-THAN AND NOT APPROXIMATE
    0x2A8A: [746,309,685,48,637],      // GREATER-THAN AND NOT APPROXIMATE
    0x2A8B: [930,424,685,56,621],      // LESS-THAN ABOVE DOUBLE-LINE EQUAL ABOVE GREATER-THAN
    0x2A8C: [930,424,685,56,621],      // GREATER-THAN ABOVE DOUBLE-LINE EQUAL ABOVE LESS-THAN
    0x2A8D: [746,176,685,48,637],      // LESS-THAN ABOVE SIMILAR OR EQUAL
    0x2A8E: [746,176,685,48,637],      // GREATER-THAN ABOVE SIMILAR OR EQUAL
    0x2A8F: [867,361,685,60,649],      // LESS-THAN ABOVE SIMILAR ABOVE GREATER-THAN
    0x2A90: [867,361,685,60,649],      // GREATER-THAN ABOVE SIMILAR ABOVE LESS-THAN
    0x2A91: [844,338,685,55,630],      // LESS-THAN ABOVE GREATER-THAN ABOVE DOUBLE-LINE EQUAL
    0x2A92: [844,338,685,55,630],      // GREATER-THAN ABOVE LESS-THAN ABOVE DOUBLE-LINE EQUAL
    0x2A93: [866,361,685,60,625],      // LESS-THAN ABOVE SLANTED EQUAL ABOVE GREATER-THAN ABOVE SLANTED EQUAL
    0x2A94: [866,361,685,60,625],      // GREATER-THAN ABOVE SLANTED EQUAL ABOVE LESS-THAN ABOVE SLANTED EQUAL
    0x2A95: [640,122,685,56,621],      // SLANTED EQUAL TO OR LESS-THAN
    0x2A96: [640,122,685,56,621],      // SLANTED EQUAL TO OR GREATER-THAN
    0x2A97: [640,122,685,56,621],      // SLANTED EQUAL TO OR LESS-THAN WITH DOT INSIDE
    0x2A98: [640,122,685,56,621],      // SLANTED EQUAL TO OR GREATER-THAN WITH DOT INSIDE
    0x2A99: [718,211,685,60,625],      // DOUBLE-LINE EQUAL TO OR LESS-THAN
    0x2A9A: [718,211,685,60,625],      // DOUBLE-LINE EQUAL TO OR GREATER-THAN
    0x2A9B: [726,220,685,60,625],      // DOUBLE-LINE SLANTED EQUAL TO OR LESS-THAN
    0x2A9C: [726,220,685,60,625],      // DOUBLE-LINE SLANTED EQUAL TO OR GREATER-THAN
    0x2A9D: [664,164,685,53,642],      // stix-similar (conforming) or less-than
    0x2A9E: [664,164,685,43,632],      // SIMILAR OR GREATER-THAN
    0x2A9F: [774,267,685,48,637],      // SIMILAR ABOVE LESS-THAN ABOVE EQUALS SIGN
    0x2AA0: [774,267,685,48,637],      // SIMILAR ABOVE GREATER-THAN ABOVE EQUALS SIGN
    0x2AA1: [532,26,685,44,609],       // DOUBLE NESTED LESS-THAN
    0x2AA2: [532,26,685,76,641],       // DOUBLE NESTED GREATER-THAN
    0x2AA3: [609,103,933,25,908],      // DOUBLE NESTED LESS-THAN WITH UNDERBAR
    0x2AA4: [532,26,782,60,722],       // GREATER-THAN OVERLAPPING LESS-THAN
    0x2AA5: [532,26,855,60,795],       // GREATER-THAN BESIDE LESS-THAN
    0x2AA6: [532,26,685,35,625],       // LESS-THAN CLOSED BY CURVE
    0x2AA7: [532,26,685,60,650],       // GREATER-THAN CLOSED BY CURVE
    0x2AA8: [625,137,685,50,640],      // LESS-THAN CLOSED BY CURVE ABOVE SLANTED EQUAL
    0x2AA9: [626,137,685,45,635],      // GREATER-THAN CLOSED BY CURVE ABOVE SLANTED EQUAL
    0x2AAA: [537,31,685,45,609],       // SMALLER THAN
    0x2AAB: [537,31,685,76,640],       // LARGER THAN
    0x2AAC: [613,103,685,60,625],      // stix-smaller than or equal, slanted
    0x2AAD: [613,103,685,60,625],      // stix-larger than or equal, slanted
    0x2AAE: [563,-28,685,48,637],      // EQUALS SIGN WITH BUMPY ABOVE
    0x2AB1: [628,216,685,60,625],      // PRECEDES ABOVE SINGLE-LINE NOT EQUAL TO
    0x2AB2: [628,216,685,60,625],      // SUCCEEDS ABOVE SINGLE-LINE NOT EQUAL TO
    0x2AB3: [717,211,685,60,625],      // PRECEDES ABOVE EQUALS SIGN
    0x2AB4: [717,211,685,60,625],      // SUCCEEDS ABOVE EQUALS SIGN
    0x2AB5: [747,260,685,65,622],      // PRECEDES ABOVE NOT EQUAL TO
    0x2AB6: [747,260,685,65,622],      // SUCCEEDS ABOVE NOT EQUAL TO
    0x2AB7: [747,275,685,48,637],      // PRECEDES ABOVE ALMOST EQUAL TO
    0x2AB8: [747,275,685,48,637],      // SUCCEEDS ABOVE ALMOST EQUAL TO
    0x2AB9: [747,309,685,48,637],      // PRECEDES ABOVE NOT ALMOST EQUAL TO
    0x2ABA: [747,309,685,48,637],      // SUCCEEDS ABOVE NOT ALMOST EQUAL TO
    0x2ABB: [532,26,933,25,908],       // DOUBLE PRECEDES
    0x2ABC: [532,26,933,25,908],       // DOUBLE SUCCEEDS
    0x2ABD: [532,26,685,60,625],       // SUBSET WITH DOT
    0x2ABE: [532,26,685,60,625],       // SUPERSET WITH DOT
    0x2ABF: [607,103,685,60,625],      // SUBSET WITH PLUS SIGN BELOW
    0x2AC0: [607,103,685,60,625],      // SUPERSET WITH PLUS SIGN BELOW
    0x2AC1: [607,103,685,60,625],      // SUBSET WITH MULTIPLICATION SIGN BELOW
    0x2AC2: [607,103,685,60,625],      // SUPERSET WITH MULTIPLICATION SIGN BELOW
    0x2AC3: [709,103,685,60,625],      // SUBSET OF OR EQUAL TO WITH DOT ABOVE
    0x2AC4: [709,103,685,60,625],      // SUPERSET OF OR EQUAL TO WITH DOT ABOVE
    0x2AC5: [717,211,685,64,622],      // SUBSET OF ABOVE EQUALS SIGN
    0x2AC6: [717,211,685,65,623],      // SUPERSET OF ABOVE EQUALS SIGN
    0x2AC7: [665,164,685,60,625],      // SUBSET OF ABOVE TILDE OPERATOR
    0x2AC8: [665,164,685,60,625],      // SUPERSET OF ABOVE TILDE OPERATOR
    0x2AC9: [746,274,685,60,625],      // SUBSET OF ABOVE ALMOST EQUAL TO
    0x2ACA: [746,274,685,60,625],      // SUPERSET OF ABOVE ALMOST EQUAL TO
    0x2ACB: [717,319,685,61,619],      // stix-subset not double equals, variant
    0x2ACC: [717,319,685,66,624],      // SUPERSET OF ABOVE NOT EQUAL TO
    0x2ACD: [558,53,1352,64,1288],     // SQUARE LEFT OPEN BOX OPERATOR
    0x2ACE: [558,53,1352,64,1288],     // SQUARE RIGHT OPEN BOX OPERATOR
    0x2ACF: [532,26,685,50,615],       // CLOSED SUBSET
    0x2AD0: [532,26,685,70,635],       // CLOSED SUPERSET
    0x2AD1: [609,103,685,60,626],      // CLOSED SUBSET OR EQUAL TO
    0x2AD2: [609,103,685,60,625],      // CLOSED SUPERSET OR EQUAL TO
    0x2AD3: [715,209,685,60,625],      // SUBSET ABOVE SUPERSET
    0x2AD4: [715,209,685,60,625],      // SUPERSET ABOVE SUBSET
    0x2AD5: [715,209,685,60,625],      // SUBSET ABOVE SUBSET
    0x2AD6: [715,209,685,60,625],      // SUPERSET ABOVE SUPERSET
    0x2AD7: [532,26,1250,60,1190],     // SUPERSET BESIDE SUBSET
    0x2AD8: [532,26,1250,60,1190],     // SUPERSET BESIDE AND JOINED BY DASH WITH SUBSET
    0x2AD9: [536,31,620,48,572],       // ELEMENT OF OPENING DOWNWARDS
    0x2ADA: [697,128,620,48,572],      // PITCHFORK WITH TEE TOP
    0x2ADB: [695,97,620,48,572],       // TRANSVERSAL INTERSECTION
    0x2ADC: [557,10,620,11,572],       // FORKING
    0x2ADD: [557,10,620,48,572],       // NONFORKING
    0x2ADE: [662,0,497,64,433],        // SHORT LEFT TACK
    0x2ADF: [371,0,685,48,637],        // SHORT DOWN TACK
    0x2AE0: [371,0,685,48,637],        // SHORT UP TACK
    0x2AE1: [662,0,685,48,637],        // PERPENDICULAR WITH S
    0x2AE2: [662,0,685,60,625],        // VERTICAL BAR TRIPLE RIGHT TURNSTILE
    0x2AE3: [662,0,860,46,803],        // DOUBLE VERTICAL BAR LEFT TURNSTILE
    0x2AE4: [662,0,685,60,625],        // VERTICAL BAR DOUBLE LEFT TURNSTILE
    0x2AE5: [662,0,860,46,803],        // DOUBLE VERTICAL BAR DOUBLE LEFT TURNSTILE
    0x2AE6: [662,0,685,57,626],        // LONG DASH FROM LEFT MEMBER OF DOUBLE VERTICAL
    0x2AE7: [571,0,685,48,637],        // SHORT DOWN TACK WITH OVERBAR
    0x2AE8: [571,0,685,48,637],        // SHORT UP TACK WITH UNDERBAR
    0x2AE9: [691,185,685,48,637],      // SHORT UP TACK ABOVE SHORT DOWN TACK
    0x2AEA: [662,0,685,48,637],        // DOUBLE DOWN TACK
    0x2AEB: [662,0,685,48,637],        // DOUBLE UP TACK
    0x2AEC: [489,-18,600,48,552],      // DOUBLE STROKE NOT SIGN
    0x2AED: [489,-18,600,48,552],      // REVERSED DOUBLE STROKE NOT SIGN
    0x2AEE: [690,189,404,23,381],      // stix-short mid negated by backslash
    0x2AEF: [660,154,502,101,401],     // VERTICAL LINE WITH CIRCLE ABOVE
    0x2AF0: [660,154,502,101,401],     // VERTICAL LINE WITH CIRCLE BELOW
    0x2AF1: [693,187,502,101,401],     // DOWN TACK WITH CIRCLE BELOW
    0x2AF2: [695,189,523,10,513],      // PARALLEL WITH HORIZONTAL STROKE
    0x2AF3: [695,189,685,48,637],      // PARALLEL WITH TILDE OPERATOR
    0x2AF4: [695,189,685,131,555],     // TRIPLE VERTICAL BAR BINARY RELATION
    0x2AF5: [695,189,685,12,674],      // TRIPLE VERTICAL BAR WITH HORIZONTAL STROKE
    0x2AF6: [608,102,685,279,406],     // TRIPLE COLON OPERATOR
    0x2AF7: [661,155,1170,58,1080],    // TRIPLE NESTED LESS-THAN
    0x2AF8: [661,155,1170,90,1112],    // TRIPLE NESTED GREATER-THAN
    0x2AF9: [726,220,685,60,625],      // DOUBLE-LINE SLANTED LESS-THAN OR EQUAL TO
    0x2AFA: [726,220,685,60,625],      // DOUBLE-LINE SLANTED GREATER-THAN OR EQUAL TO
    0x2AFB: [710,222,894,46,848],      // TRIPLE SOLIDUS BINARY RELATION
    0x2AFC: [763,259,654,94,560],      // LARGE TRIPLE VERTICAL BAR OPERATOR
    0x2AFD: [710,222,709,46,663],      // DOUBLE SOLIDUS OPERATOR
    0x2AFE: [690,189,410,100,310],     // WHITE VERTICAL BAR
    0x2AFF: [763,259,478,94,384]       // N-ARY WHITE VERTICAL BAR
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/SuppMathOperators.js");
