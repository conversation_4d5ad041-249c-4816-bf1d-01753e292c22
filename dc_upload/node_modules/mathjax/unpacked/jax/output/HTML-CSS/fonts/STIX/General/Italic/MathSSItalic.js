/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathSSItalic.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-italic'],
  {
    0x1D608: [674,0,666,31,635],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL A
    0x1D609: [662,0,604,74,641],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL B
    0x1D60A: [676,14,671,96,755],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL C
    0x1D60B: [662,0,692,74,751],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL D
    0x1D60C: [662,0,583,74,678],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL E
    0x1D60D: [662,0,535,74,679],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL F
    0x1D60E: [676,14,695,97,755],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL G
    0x1D60F: [662,0,658,74,749],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL H
    0x1D610: [662,0,401,59,512],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL I
    0x1D611: [662,14,398,22,470],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL J
    0x1D612: [662,0,634,74,729],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL K
    0x1D613: [662,0,559,74,564],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL L
    0x1D614: [662,0,843,75,933],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL M
    0x1D615: [662,14,675,74,766],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL N
    0x1D616: [676,14,714,99,779],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL O
    0x1D617: [662,0,525,74,638],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL P
    0x1D618: [676,175,716,99,779],     // MATHEMATICAL SANS-SERIF ITALIC CAPITAL Q
    0x1D619: [662,0,589,74,639],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL R
    0x1D61A: [676,14,541,62,597],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL S
    0x1D61B: [662,0,608,161,748],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL T
    0x1D61C: [662,14,661,117,757],     // MATHEMATICAL SANS-SERIF ITALIC CAPITAL U
    0x1D61D: [662,11,654,196,788],     // MATHEMATICAL SANS-SERIF ITALIC CAPITAL V
    0x1D61E: [662,11,921,194,1057],    // MATHEMATICAL SANS-SERIF ITALIC CAPITAL W
    0x1D61F: [662,0,700,31,806],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL X
    0x1D620: [662,0,630,186,774],      // MATHEMATICAL SANS-SERIF ITALIC CAPITAL Y
    0x1D621: [662,0,637,28,763],       // MATHEMATICAL SANS-SERIF ITALIC CAPITAL Z
    0x1D622: [463,10,448,55,467],      // MATHEMATICAL SANS-SERIF ITALIC SMALL A
    0x1D623: [684,10,496,74,535],      // MATHEMATICAL SANS-SERIF ITALIC SMALL B
    0x1D624: [463,10,456,67,503],      // MATHEMATICAL SANS-SERIF ITALIC SMALL C
    0x1D625: [684,11,494,72,600],      // MATHEMATICAL SANS-SERIF ITALIC SMALL D
    0x1D626: [463,10,444,69,487],      // MATHEMATICAL SANS-SERIF ITALIC SMALL E
    0x1D627: [683,0,336,101,526],      // MATHEMATICAL SANS-SERIF ITALIC SMALL F
    0x1D628: [463,216,496,-7,575],     // MATHEMATICAL SANS-SERIF ITALIC SMALL G
    0x1D629: [684,0,487,63,510],       // MATHEMATICAL SANS-SERIF ITALIC SMALL H
    0x1D62A: [679,0,220,69,325],       // MATHEMATICAL SANS-SERIF ITALIC SMALL I
    0x1D62B: [679,216,254,-118,354],   // MATHEMATICAL SANS-SERIF ITALIC SMALL J
    0x1D62C: [684,0,453,63,556],       // MATHEMATICAL SANS-SERIF ITALIC SMALL K
    0x1D62D: [684,0,205,61,313],       // MATHEMATICAL SANS-SERIF ITALIC SMALL L
    0x1D62E: [464,0,756,65,775],       // MATHEMATICAL SANS-SERIF ITALIC SMALL M
    0x1D62F: [464,0,487,63,510],       // MATHEMATICAL SANS-SERIF ITALIC SMALL N
    0x1D630: [463,10,499,76,536],      // MATHEMATICAL SANS-SERIF ITALIC SMALL O
    0x1D631: [464,216,498,14,538],     // MATHEMATICAL SANS-SERIF ITALIC SMALL P
    0x1D632: [464,216,498,72,549],     // MATHEMATICAL SANS-SERIF ITALIC SMALL Q
    0x1D633: [464,0,336,63,439],       // MATHEMATICAL SANS-SERIF ITALIC SMALL R
    0x1D634: [463,10,389,61,432],      // MATHEMATICAL SANS-SERIF ITALIC SMALL S
    0x1D635: [580,10,291,96,376],      // MATHEMATICAL SANS-SERIF ITALIC SMALL T
    0x1D636: [453,11,491,89,536],      // MATHEMATICAL SANS-SERIF ITALIC SMALL U
    0x1D637: [453,14,474,143,555],     // MATHEMATICAL SANS-SERIF ITALIC SMALL V
    0x1D638: [453,14,702,140,787],     // MATHEMATICAL SANS-SERIF ITALIC SMALL W
    0x1D639: [453,0,482,30,544],       // MATHEMATICAL SANS-SERIF ITALIC SMALL X
    0x1D63A: [453,216,484,-19,565],    // MATHEMATICAL SANS-SERIF ITALIC SMALL Y
    0x1D63B: [453,0,447,25,517]        // MATHEMATICAL SANS-SERIF ITALIC SMALL Z
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Italic/MathSSItalic.js");
