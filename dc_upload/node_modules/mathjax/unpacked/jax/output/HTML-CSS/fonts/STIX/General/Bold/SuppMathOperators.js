/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/SuppMathOperators.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold'],
  {
    0x2A0C: [824,320,1484,32,1664],    // QUADRUPLE INTEGRAL OPERATOR
    0x2A0D: [824,320,593,32,733],      // FINITE PART INTEGRAL
    0x2A0E: [824,320,593,32,733],      // INTEGRAL WITH DOUBLE STROKE
    0x2A0F: [824,320,593,32,733],      // INTEGRAL AVERAGE WITH SLASH
    0x2A10: [824,320,593,32,733],      // CIRCULATION FUNCTION
    0x2A11: [824,320,593,32,733],      // ANTICLOCKWISE INTEGRATION
    0x2A12: [824,320,613,32,733],      // LINE INTEGRATION WITH RECTANGULAR PATH AROUND POLE
    0x2A13: [824,320,593,32,733],      // LINE INTEGRATION WITH SEMICIRCULAR PATH AROUND POLE
    0x2A14: [824,320,675,32,735],      // LINE INTEGRATION NOT INCLUDING THE POLE
    0x2A15: [824,320,593,32,733],      // INTEGRAL AROUND A POINT OPERATOR
    0x2A16: [824,320,623,32,733],      // QUATERNION INTEGRAL OPERATOR
    0x2A17: [824,320,791,32,871],      // INTEGRAL WITH LEFTWARDS ARROW WITH HOOK
    0x2A18: [824,320,633,32,733],      // INTEGRAL WITH TIMES SIGN
    0x2A19: [824,320,653,32,733],      // INTEGRAL WITH INTERSECTION
    0x2A1A: [824,320,653,32,733],      // INTEGRAL WITH UNION
    0x2A1B: [959,320,557,32,737],      // INTEGRAL WITH OVERBAR
    0x2A1C: [824,455,557,32,737],      // INTEGRAL WITH UNDERBAR
    0x2A22: [894,57,750,65,685],       // PLUS SIGN WITH SMALL CIRCLE ABOVE
    0x2A23: [736,57,750,65,685],       // PLUS SIGN WITH CIRCUMFLEX ACCENT ABOVE
    0x2A24: [746,57,750,65,685],       // PLUS SIGN WITH TILDE ABOVE
    0x2A25: [563,287,750,65,685],      // PLUS SIGN WITH DOT BELOW
    0x2A26: [563,240,750,65,685],      // PLUS SIGN WITH TILDE BELOW
    0x2A27: [563,247,780,65,778],      // PLUS SIGN WITH SUBSCRIPT TWO
    0x2A2A: [297,37,750,66,685],       // MINUS SIGN WITH DOT BELOW
    0x2A2B: [543,37,750,66,685],       // MINUS SIGN WITH FALLING DOTS
    0x2A2C: [543,37,750,66,685],       // MINUS SIGN WITH RISING DOTS
    0x2A30: [745,33,702,66,636],       // MULTIPLICATION SIGN WITH DOT ABOVE
    0x2A31: [538,191,702,66,636],      // MULTIPLICATION SIGN WITH UNDERBAR
    0x2A32: [538,59,702,66,636],       // SEMIDIRECT PRODUCT WITH BOTTOM CLOSED
    0x2A3F: [676,0,734,27,707],        // AMALGAMATION OR COPRODUCT
    0x2A5E: [887,28,640,52,588],       // LOGICAL AND WITH DOUBLE OVERBAR
    0x2A63: [536,379,640,52,588],      // LOGICAL OR WITH DOUBLE UNDERBAR
    0x2A66: [399,161,750,68,682],      // EQUALS SIGN WITH DOT BELOW
    0x2A67: [775,-27,750,68,682],      // IDENTICAL WITH DOT ABOVE
    0x2A6A: [565,-132,750,67,682],     // TILDE OPERATOR WITH DOT ABOVE
    0x2A6D: [759,60,750,68,683],       // CONGRUENT WITH DOT ABOVE
    0x2A6E: [884,-107,750,68,682],     // EQUALS WITH ASTERISK
    0x2A6F: [752,-26,750,68,683],      // ALMOST EQUAL TO WITH CIRCUMFLEX ACCENT
    0x2A70: [680,176,750,68,683],      // APPROXIMATELY EQUAL OR EQUAL TO
    0x2A71: [665,159,750,65,685],      // EQUALS SIGN ABOVE PLUS SIGN
    0x2A72: [665,159,750,65,685],      // PLUS SIGN ABOVE EQUALS SIGN
    0x2A73: [568,60,750,67,682],       // EQUALS SIGN ABOVE TILDE OPERATOR
    0x2A7D: [648,140,750,80,670],      // LESS-THAN OR SLANTED EQUAL TO
    0x2A7E: [648,140,750,80,670],      // GREATER-THAN OR SLANTED EQUAL TO
    0x2A87: [646,213,750,80,670],      // LESS-THAN AND SINGLE-LINE NOT EQUAL TO
    0x2A88: [646,213,750,80,670],      // GREATER-THAN AND SINGLE-LINE NOT EQUAL TO
    0x2A89: [792,305,750,67,682],      // LESS-THAN AND NOT APPROXIMATE
    0x2A8A: [792,305,750,67,682],      // GREATER-THAN AND NOT APPROXIMATE
    0x2A95: [648,140,750,80,670],      // SLANTED EQUAL TO OR LESS-THAN
    0x2A96: [648,140,750,80,670],      // SLANTED EQUAL TO OR GREATER-THAN
    0x2A9D: [689,183,750,67,682],      // stix-similar (conforming) or less-than
    0x2A9E: [689,183,750,67,682],      // SIMILAR OR GREATER-THAN
    0x2AAF: [619,111,750,80,670],      // PRECEDES ABOVE SINGLE-LINE EQUALS SIGN
    0x2AB0: [619,111,750,80,670],      // SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN
    0x2ABD: [547,13,750,82,668],       // SUBSET WITH DOT
    0x2ABE: [547,13,750,82,668],       // SUPERSET WITH DOT
    0x2AC5: [730,222,750,80,670],      // SUBSET OF ABOVE EQUALS SIGN
    0x2AC6: [730,222,750,80,670]       // SUPERSET OF ABOVE EQUALS SIGN
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Bold/SuppMathOperators.js");
