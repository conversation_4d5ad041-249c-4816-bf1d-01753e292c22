/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Bold/PrivateUse.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXNonUnicode-bold'],
  {
    0xE000: [610,25,1184,808,912],     // stix-radical symbol vertical extender
    0xE001: [704,-75,1198,808,1224],   // stix-radical symbol top corner piece
    0xE00E: [819,339,750,80,670],      // stix-not greater, double equals
    0xE00F: [742,235,750,80,670],      // stix-not greater-or-equal, slanted
    0xE010: [742,235,750,80,670],      // stix-not less-or-equal, slanted
    0xE011: [819,339,750,80,670],      // stix-not less, double equals
    0xE023: [742,235,750,68,683],      // stix-not congruent, dot
    0xE025: [852,345,750,67,683],      // stix-not approximately equal or equal to
    0xE028: [672,166,1000,38,961],     // stix-not much less than
    0xE029: [672,166,1000,38,961],     // stix-not much greater than
    0xE037: [672,166,750,67,682],      // stix-reverse not equal
    0xE04D: [553,47,750,68,683],       // stix-not, vert, equal or similar
    0xE050: [672,166,750,87,663],      // stix-not, square subset
    0xE051: [672,166,750,87,663],      // stix-not, square superset
    0xE052: [574,69,750,68,683],       // stix-not bumpy equals
    0xE05B: [574,-16,750,68,683],      // stix-not bumpy single equals
    0xE05C: [553,31,750,68,683],       // stix-not equal or similar
    0xE05E: [762,-565,0,95,425],       // stix-double macron
    0xE061: [-137,437,0,0,330],        // stix-triple underbar
    0xE062: [-137,552,0,0,330],        // stix-quadruple underbar
    0xE064: [837,-565,333,-16,349],    // stix-tilde over bar over
    0xE065: [-137,409,0,-16,349],      // stix-straight over wavy underline
    0xE066: [801,-565,0,91,430],       // stix-double dot over bar over
    0xE067: [-137,409,0,-16,349],      // stix-wavy over straight underline
    0xE06D: [835,113,750,82,668],      // stix-not equal to or member
    0xE06E: [835,113,750,82,668],      // stix-not equal to or contains
    0xE06F: [835,113,750,82,668],      // stix-Not (vert) equals or member
    0xE070: [835,113,750,82,668],      // stix-not (vert) equals or contains
    0xE07E: [738,230,750,80,670],      // stix-not (vert) less-than slanted equal
    0xE07F: [742,234,750,80,670],      // stix-not (vert) greater-than slanted equal
    0xE080: [819,337,750,80,670],      // stix-not (vert) less-than or two-line equal
    0xE081: [820,342,750,91,681],      // stix-not (vert) greater-than or two-line equal
    0xE082: [742,235,750,80,670],      // stix-not (slash) equal (slant) or less-than
    0xE083: [742,234,750,80,670],      // stix-not (slash) equal (slant) or greater-than
    0xE084: [738,230,750,80,670],      // stix-not (vert) equals (slant) or less-than
    0xE085: [742,234,750,80,670],      // stix-not (vert) equals (slant) or greater-than
    0xE0B0: [752,-531,0,100,417],      // stix-left overangle (combining)
    0xE0B1: [-50,271,0,100,417],       // stix-left underangle (combining)
    0xE0B2: [-50,271,0,99,416],        // stix-right underangle (combining)
    0xE0B3: [691,203,556,14,487],      // stix-small fj ligature
    0xE0B4: [555,-209,282,42,239],     // stix-arrow hookleft
    0xE0B5: [555,-209,282,43,240],     // stix-arrow hookright
    0xE0B6: [478,-56,0,15,142],        // stix-maps-to relation tail
    0xE0D8: [688,13,400,57,343],       // stix-arc-degrees (degree with dot below)
    0xE0D9: [663,0,314,54,260],        // stix-arc-minutes (prime with dot below)
    0xE0DA: [663,0,425,54,371],        // stix-arc-seconds (double prime with dot below)
    0xE0DD: [930,0,553,76,483],        // stix-days (roman d with dot below)
    0xE0DE: [926,0,549,67,482],        // stix-hours (roman h with dot below)
    0xE0DF: [765,0,773,67,706],        // stix-minutes (roman m with dot below)
    0xE0E0: [920,0,552,42,510],        // stix-period (roman p with dot below)
    0xE0E1: [765,0,378,55,323],        // stix-seconds (roman s with dot below)
    0xE0E2: [754,0,481,63,435],        // stix-years (roman y with dot below)
    0xE10B: [297,-209,315,0,315],      // stix-stix-extender for horizontal solid (normal) arrow
    0xE10E: [405,-101,714,211,503],    // stix-extender for vertical double arrow
    0xE10F: [399,-107,315,0,315],      // stix-extender for horizontal double arrow
    0xE150: [175,302,735,-40,756],     // stix-horizontal brace, down left piece
    0xE151: [175,302,735,-21,775],     // stix-horizontal brace, down right piece
    0xE152: [477,0,735,-40,756],       // stix-horizontal brace, upper left piece
    0xE153: [477,0,735,-21,775],       // stix-horizontal brace, upper right piece
    0xE263: [422,10,523,26,496],       // stix-old style digit 0
    0xE267: [425,0,523,111,420],       // stix-old style digit 1
    0xE26B: [421,0,523,53,470],        // stix-old style digit 2
    0xE26F: [424,198,523,31,478],      // stix-old style digit 3
    0xE273: [420,198,523,42,496],      // stix-old style digit 4
    0xE277: [421,198,523,49,474],      // stix-old style digit 5
    0xE27B: [614,8,523,21,502],        // stix-old style digit 6
    0xE27F: [421,198,523,8,507],       // stix-old style digit 7
    0xE283: [606,12,523,31,493],       // stix-old style digit 8
    0xE287: [421,202,523,25,499],      // stix-old style digit 9
    0xE28D: [734,-484,0,92,498],       // stix-double circumflex
    0xE28F: [175,0,325,-1,326],        // stix-short horizontal extender at baseline
    0xE290: [175,0,633,-1,634],        // stix-long horizontal extender at baseline
    0xE2FD: [775,235,722,9,689],       // stix-MATHEMATICAL BOLD CAPITAL ALPHA SLASHED
    0xE2FF: [775,235,667,16,619],      // stix-MATHEMATICAL BOLD CAPITAL BETA SLASHED
    0xE301: [775,207,620,16,593],      // stix-MATHEMATICAL BOLD CAPITAL GAMMA SLASHED
    0xE303: [775,207,722,33,673],      // stix-MATHEMATICAL BOLD CAPITAL DELTA SLASHED
    0xE305: [775,235,667,16,641],      // stix-MATHEMATICAL BOLD CAPITAL EPSILON SLASHED
    0xE307: [775,235,667,28,634],      // stix-MATHEMATICAL BOLD CAPITAL ZETA SLASHED
    0xE309: [775,235,778,21,759],      // stix-MATHEMATICAL BOLD CAPITAL ETA SLASHED
    0xE30B: [775,207,778,35,743],      // stix-MATHEMATICAL BOLD CAPITAL THETA SLASHED
    0xE30D: [775,235,389,-36,436],     // stix-MATHEMATICAL BOLD CAPITAL IOTA SLASHED
    0xE30F: [775,235,778,30,769],      // stix-MATHEMATICAL BOLD CAPITAL KAPPA SLASHED
    0xE311: [775,207,707,9,674],       // stix-MATHEMATICAL BOLD CAPITAL LAMBDA SLASHED
    0xE313: [775,235,944,14,921],      // stix-MATHEMATICAL BOLD CAPITAL MU SLASHED
    0xE315: [775,235,722,16,701],      // stix-MATHEMATICAL BOLD CAPITAL NU SLASHED
    0xE317: [775,207,647,40,607],      // stix-MATHEMATICAL BOLD CAPITAL XI SLASHED
    0xE319: [775,235,778,35,743],      // stix-MATHEMATICAL BOLD CAPITAL OMICRON SLASHED
    0xE31B: [775,207,778,21,759],      // stix-MATHEMATICAL BOLD CAPITAL PI SLASHED
    0xE31D: [775,235,611,16,600],      // stix-MATHEMATICAL BOLD CAPITAL RHO SLASHED
    0xE31F: [775,207,671,28,641],      // stix-MATHEMATICAL BOLD CAPITAL SIGMA SLASHED
    0xE321: [775,235,667,31,636],      // stix-MATHEMATICAL BOLD CAPITAL TAU SLASHED
    0xE323: [775,207,723,14,700],      // stix-MATHEMATICAL BOLD CAPITAL UPSILON SLASHED
    0xE325: [775,207,836,18,818],      // stix-MATHEMATICAL BOLD CAPITAL PHI SLASHED
    0xE327: [775,235,722,16,699],      // stix-MATHEMATICAL BOLD CAPITAL CHI SLASHED
    0xE329: [775,207,804,11,793],      // stix-MATHEMATICAL BOLD CAPITAL PSI SLASHED
    0xE32B: [775,207,768,28,740],      // stix-MATHEMATICAL BOLD CAPITAL OMEGA SLASHED
    0xE365: [775,235,669,32,665],      // stix-capital stigma, Greek slashed
    0xE369: [775,235,667,-13,670],     // stix-capital digamma, Greek slashed
    0xE36D: [793,235,757,-49,758],     // stix-capital koppa, Greek slashed
    0xE371: [775,235,734,27,710],      // stix-capital sampi, Greek slashed
    0xE37C: [775,235,667,16,641],      // stix-capital E roman bold slashed
    0xE3B7: [681,11,525,40,482],       // MATHEMATICAL MONOSPACE DIGIT ZERO
    0xE3B8: [681,0,525,90,450],        // MATHEMATICAL MONOSPACE DIGIT ONE
    0xE3B9: [681,0,525,52,470],        // MATHEMATICAL MONOSPACE DIGIT TWO
    0xE3BA: [681,11,525,43,479],       // MATHEMATICAL MONOSPACE DIGIT THREE
    0xE3BB: [682,0,525,29,493],        // MATHEMATICAL MONOSPACE DIGIT FOUR
    0xE3BC: [670,11,525,52,470],       // MATHEMATICAL MONOSPACE DIGIT FIVE
    0xE3BD: [681,11,525,43,479],       // MATHEMATICAL MONOSPACE DIGIT SIX
    0xE3BE: [686,11,525,43,479],       // MATHEMATICAL MONOSPACE DIGIT SEVEN
    0xE3BF: [681,11,525,43,479],       // MATHEMATICAL MONOSPACE DIGIT EIGHT
    0xE3C0: [681,11,525,43,479],       // MATHEMATICAL MONOSPACE DIGIT NINE
    0xE3C3: [747,243,750,68,683],      // stix-not (vert) almost equal or equal to
    0xE3C4: [747,243,750,68,683]       // stix-not almost equal or equal to
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/NonUnicode/Bold/PrivateUse.js");
