/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathSS.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x1D5A0: [674,0,666,31,635],       // MATHEMATICAL SANS-SERIF CAPITAL A
    0x1D5A1: [662,0,604,74,547],       // MATHEMATICAL SANS-SERIF CAPITAL B
    0x1D5A2: [676,14,671,27,637],      // MATHEMATICAL SANS-SERIF CAPITAL C
    0x1D5A3: [662,0,692,74,656],       // MATHEMATICAL SANS-SERIF CAPITAL D
    0x1D5A4: [662,0,583,74,540],       // MATHEMATICAL SANS-SERIF CAPITAL E
    0x1D5A5: [662,0,535,74,523],       // MATHEMATICAL SANS-SERIF CAPITAL F
    0x1D5A6: [676,14,695,27,627],      // MATHEMATICAL SANS-SERIF CAPITAL G
    0x1D5A7: [662,0,658,74,584],       // MATHEMATICAL SANS-SERIF CAPITAL H
    0x1D5A8: [662,0,401,45,356],       // MATHEMATICAL SANS-SERIF CAPITAL I
    0x1D5A9: [662,14,398,12,305],      // MATHEMATICAL SANS-SERIF CAPITAL J
    0x1D5AA: [662,0,634,74,630],       // MATHEMATICAL SANS-SERIF CAPITAL K
    0x1D5AB: [662,0,559,74,546],       // MATHEMATICAL SANS-SERIF CAPITAL L
    0x1D5AC: [662,0,843,75,768],       // MATHEMATICAL SANS-SERIF CAPITAL M
    0x1D5AD: [662,14,675,74,601],      // MATHEMATICAL SANS-SERIF CAPITAL N
    0x1D5AE: [676,14,714,30,684],      // MATHEMATICAL SANS-SERIF CAPITAL O
    0x1D5AF: [662,0,525,74,512],       // MATHEMATICAL SANS-SERIF CAPITAL P
    0x1D5B0: [676,175,716,30,691],     // MATHEMATICAL SANS-SERIF CAPITAL Q
    0x1D5B1: [662,0,589,74,581],       // MATHEMATICAL SANS-SERIF CAPITAL R
    0x1D5B2: [676,14,541,32,481],      // MATHEMATICAL SANS-SERIF CAPITAL S
    0x1D5B3: [662,0,608,15,593],       // MATHEMATICAL SANS-SERIF CAPITAL T
    0x1D5B4: [662,14,661,69,592],      // MATHEMATICAL SANS-SERIF CAPITAL U
    0x1D5B5: [662,11,654,31,623],      // MATHEMATICAL SANS-SERIF CAPITAL V
    0x1D5B6: [662,11,921,29,892],      // MATHEMATICAL SANS-SERIF CAPITAL W
    0x1D5B7: [662,0,700,31,669],       // MATHEMATICAL SANS-SERIF CAPITAL X
    0x1D5B8: [662,0,630,21,609],       // MATHEMATICAL SANS-SERIF CAPITAL Y
    0x1D5B9: [662,0,637,28,603],       // MATHEMATICAL SANS-SERIF CAPITAL Z
    0x1D5BA: [463,10,448,35,391],      // MATHEMATICAL SANS-SERIF SMALL A
    0x1D5BB: [684,10,496,63,466],      // MATHEMATICAL SANS-SERIF SMALL B
    0x1D5BC: [463,10,456,23,432],      // MATHEMATICAL SANS-SERIF SMALL C
    0x1D5BD: [684,11,494,28,437],      // MATHEMATICAL SANS-SERIF SMALL D
    0x1D5BE: [463,10,444,23,428],      // MATHEMATICAL SANS-SERIF SMALL E
    0x1D5BF: [683,0,336,20,369],       // MATHEMATICAL SANS-SERIF SMALL F
    0x1D5C0: [463,216,496,21,467],     // MATHEMATICAL SANS-SERIF SMALL G
    0x1D5C1: [684,0,487,63,424],       // MATHEMATICAL SANS-SERIF SMALL H
    0x1D5C2: [679,0,220,64,156],       // MATHEMATICAL SANS-SERIF SMALL I
    0x1D5C3: [679,216,254,-74,185],    // MATHEMATICAL SANS-SERIF SMALL J
    0x1D5C4: [684,0,453,63,452],       // MATHEMATICAL SANS-SERIF SMALL K
    0x1D5C5: [684,0,205,61,144],       // MATHEMATICAL SANS-SERIF SMALL L
    0x1D5C6: [464,0,756,65,691],       // MATHEMATICAL SANS-SERIF SMALL M
    0x1D5C7: [464,0,487,63,424],       // MATHEMATICAL SANS-SERIF SMALL N
    0x1D5C8: [463,10,499,28,471],      // MATHEMATICAL SANS-SERIF SMALL O
    0x1D5C9: [464,216,498,67,470],     // MATHEMATICAL SANS-SERIF SMALL P
    0x1D5CA: [464,216,498,28,435],     // MATHEMATICAL SANS-SERIF SMALL Q
    0x1D5CB: [464,0,336,63,328],       // MATHEMATICAL SANS-SERIF SMALL R
    0x1D5CC: [463,10,389,49,350],      // MATHEMATICAL SANS-SERIF SMALL S
    0x1D5CD: [580,10,291,1,287],       // MATHEMATICAL SANS-SERIF SMALL T
    0x1D5CE: [453,11,491,63,430],      // MATHEMATICAL SANS-SERIF SMALL U
    0x1D5CF: [453,14,474,31,443],      // MATHEMATICAL SANS-SERIF SMALL V
    0x1D5D0: [453,14,702,28,675],      // MATHEMATICAL SANS-SERIF SMALL W
    0x1D5D1: [453,0,482,30,452],       // MATHEMATICAL SANS-SERIF SMALL X
    0x1D5D2: [453,216,484,28,453],     // MATHEMATICAL SANS-SERIF SMALL Y
    0x1D5D3: [453,0,447,25,417],       // MATHEMATICAL SANS-SERIF SMALL Z
    0x1D7E2: [676,14,500,23,477],      // MATHEMATICAL SANS-SERIF DIGIT ZERO
    0x1D7E3: [677,0,500,108,302],      // MATHEMATICAL SANS-SERIF DIGIT ONE
    0x1D7E4: [676,0,500,35,469],       // MATHEMATICAL SANS-SERIF DIGIT TWO
    0x1D7E5: [676,14,500,31,441],      // MATHEMATICAL SANS-SERIF DIGIT THREE
    0x1D7E6: [676,0,500,11,489],       // MATHEMATICAL SANS-SERIF DIGIT FOUR
    0x1D7E7: [676,14,500,36,458],      // MATHEMATICAL SANS-SERIF DIGIT FIVE
    0x1D7E8: [684,14,500,32,470],      // MATHEMATICAL SANS-SERIF DIGIT SIX
    0x1D7E9: [662,8,500,38,451],       // MATHEMATICAL SANS-SERIF DIGIT SEVEN
    0x1D7EA: [676,14,500,49,447],      // MATHEMATICAL SANS-SERIF DIGIT EIGHT
    0x1D7EB: [676,21,500,28,466]       // MATHEMATICAL SANS-SERIF DIGIT NINE
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MathSS.js");
