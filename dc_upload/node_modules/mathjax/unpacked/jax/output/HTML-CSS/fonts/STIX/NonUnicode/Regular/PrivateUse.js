/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Regular/PrivateUse.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXNonUnicode'],
  {
    0xE002: [1022,0,1192,30,1162],     // stix-"shaw": large operator with three parallel vertical lines topped by a horizontal
    0xE003: [1450,0,1311,55,1256],     // stix-"shaw": large operator with three parallel vertical lines topped by a horizontal
    0xE004: [450,-59,926,55,871],      // stix-not right arrow-wavy
    0xE005: [530,-57,926,55,871],      // stix-not right arrow-curved
    0xE00A: [538,-55,685,48,637],      // stix-reverse most positive, line below
    0xE00B: [543,37,685,48,637],       // stix-most positive, two lines below
    0xE00E: [846,340,685,60,626],      // stix-not greater, double equals
    0xE00F: [730,229,685,56,621],      // stix-not greater-or-equal, slanted
    0xE010: [730,229,685,56,621],      // stix-not less-or-equal, slanted
    0xE011: [846,340,685,61,626],      // stix-not less, double equals
    0xE016: [818,311,685,53,618],      // stix-not subset, double equals
    0xE018: [818,311,685,67,632],      // stix-not superset, double equals
    0xE01E: [607,110,685,48,638],      // stix-not approximately identical to
    0xE023: [695,189,685,48,637],      // stix-not congruent, dot
    0xE025: [724,236,685,48,637],      // stix-not approximately equal or equal to
    0xE026: [662,156,685,47,612],      // stix-not, vert, double nested less than
    0xE027: [662,156,685,73,638],      // stix-not, vert, double nested greater than
    0xE028: [663,155,933,25,908],      // stix-not much less than
    0xE029: [662,156,933,25,908],      // stix-not much greater than
    0xE02A: [662,156,1240,43,1184],    // stix-not triple less than
    0xE02B: [662,156,1240,56,1197],    // stix-not triple greater than
    0xE02F: [662,156,685,23,662],      // stix-not, vert, angle
    0xE035: [602,98,486,64,422],       // stix-negated contains
    0xE037: [662,156,685,48,637],      // stix-reverse not equal
    0xE038: [690,189,732,50,682],      // stix-not parallel, slanted
    0xE039: [662,156,685,47,636],      // stix-not equal, dot
    0xE03A: [811,156,471,40,525],      // stix-not partial differential
    0xE03B: [736,156,685,60,625],      // stix-negated set membership, dot above
    0xE03C: [662,156,685,60,625],      // stix-negated set membership, two horizontal strokes
    0xE03D: [602,98,486,64,422],       // stix-negated (slash) set membership
    0xE040: [415,-92,685,48,637],      // stix-congruence sign (lazy S)
    0xE043: [818,311,685,60,625],      // stix-two-line slanted equal to or less-than - with vertical stroke
    0xE044: [818,311,685,60,625],      // stix-two-line slanted equal to or greater-than with vertical stroke
    0xE045: [818,311,685,60,625],      // stix-two-line slanted equal to or less-than - with slash
    0xE046: [818,311,685,60,625],      // stix-two-line slanted equal to or greater-than with slash
    0xE04B: [745,242,685,60,625],      // stix-not precedes, single equals
    0xE04C: [845,341,685,60,625],      // stix-not precedes, double equals
    0xE04D: [534,19,685,48,637],       // stix-not, vert, equal or similar
    0xE04E: [845,341,685,60,625],      // stix-not succeeds, double equals
    0xE04F: [745,242,685,60,625],      // stix-not succeeds, single equals
    0xE050: [662,156,685,65,620],      // stix-not, square subset
    0xE051: [662,156,685,65,620],      // stix-not, square superset
    0xE052: [561,55,685,48,637],       // stix-not bumpy equals
    0xE053: [450,0,632,26,604],        // stix-Latin letter small-cap K, reversed
    0xE054: [516,10,688,37,679],       // stix-Latin letter small a (one-story) with rhotic hook
    0xE055: [475,14,571,20,563],       // stix-Latin letter small open e with rhotic hook
    0xE056: [459,11,632,10,624],       // stix-Latin letter small open o with rhotic hook
    0xE057: [459,12,624,29,595],       // stix-Latin letter small omega, inverted
    0xE059: [730,224,685,48,637],      // stix-not precedes, similar
    0xE05A: [730,224,685,48,637],      // stix-not succeeds, similar
    0xE05B: [561,-30,685,48,637],      // stix-not bumpy single equals
    0xE05C: [534,19,685,48,637],       // stix-not equal or similar
    0xE05D: [459,10,452,16,436],       // stix-barred ess
    0xE05E: [698,-547,0,95,406],       // stix-double macron
    0xE061: [-141,390,0,11,322],       // stix-triple underbar
    0xE062: [-141,486,0,11,322],       // stix-quadruple underbar
    0xE063: [734,-508,0,94,485],       // stix-accent caret over dot
    0xE064: [777,-547,0,95,425],       // stix-tilde over bar over
    0xE065: [-141,371,0,1,331],        // stix-straight over wavy underline
    0xE066: [770,-547,0,101,412],      // stix-double dot over bar over
    0xE067: [-141,371,0,1,331],        // stix-wavy over straight underline
    0xE068: [683,287,524,9,487],       // stix-hooked h
    0xE06B: [602,98,486,64,422],       // stix-small not (vert) member
    0xE06C: [602,98,486,64,422],       // stix-small not (vert) contains
    0xE06D: [785,157,685,60,625],      // stix-not equal to or member
    0xE06E: [785,157,685,60,625],      // stix-not equal to or contains
    0xE06F: [785,129,685,60,625],      // stix-Not (vert) equals or member
    0xE070: [785,129,685,60,625],      // stix-not (vert) equals or contains
    0xE075: [626,119,685,48,637],      // stix-not (slash) similar minus similar
    0xE076: [626,119,685,48,637],      // stix-not (vert) similar minus similar
    0xE077: [725,236,685,48,637],      // stix-not (vert) double similar over two-line equals
    0xE07C: [758,252,685,48,637],      // stix-not (slash) four-line equals (not strictly equivalent to)
    0xE07D: [758,252,685,48,637],      // stix-not (vert) four-line equals
    0xE07E: [732,227,685,56,621],      // stix-not (vert) less-than slanted equal
    0xE07F: [732,227,685,56,621],      // stix-not (vert) greater-than slanted equal
    0xE080: [818,311,685,57,622],      // stix-not (vert) less-than or two-line equal
    0xE081: [818,311,685,57,622],      // stix-not (vert) greater-than or two-line equal
    0xE082: [730,229,685,56,621],      // stix-not (slash) equal (slant) or less-than
    0xE083: [730,229,685,56,621],      // stix-not (slash) equal (slant) or greater-than
    0xE084: [709,201,685,56,621],      // stix-not (vert) equals (slant) or less-than
    0xE085: [709,201,685,56,621],      // stix-not (vert) equals (slant) or greater-than
    0xE086: [818,311,685,56,621],      // stix-not (slash) two-line equal or less-than
    0xE087: [818,311,685,55,621],      // stix-not (slash) two-line equal or greater-than
    0xE088: [818,311,685,56,621],      // stix-not (vert) two-line equals or less-than
    0xE089: [818,311,685,55,620],      // stix-not (vert) two-line equals or greater-than
    0xE092: [719,296,685,62,627],      // stix-not (vert) equals (contour) or precedes
    0xE093: [719,296,685,61,626],      // stix-not (vert) equals (contour) or succeeds
    0xE094: [719,240,685,62,627],      // stix-not (slash) equals (contour) or precedes
    0xE095: [719,240,685,61,626],      // stix-not (slash) equals (contour) or succeeds
    0xE096: [818,311,685,55,620],      // stix-not (vert) subset or two-line equals
    0xE097: [818,311,685,65,630],      // stix-not (vert) superset or two-line equals
    0xE098: [690,189,685,23,662],      // stix-triple vertical, slash cancellation
    0xE099: [567,183,612,25,587],      // stix-narrow sloped nabla
    0xE09A: [719,213,708,18,690],      // stix-parentheses around thin space
    0xE09B: [719,213,708,18,690],      // stix-center dot in parentheses
    0xE0A2: [460,218,561,24,539],      // stix-barred open gee
    0xE0A4: [470,233,378,10,358],      // stix-Latin letter small r-fishhook, reversed, with descender stem
    0xE0A5: [669,-426,397,75,338],     // stix-hooktop (phonetic symbol)
    0xE0A6: [216,144,444,38,429],      // stix-curly tail (phonetic symbol)
    0xE0A7: [702,-506,376,30,346],     // stix-modifier letter level-rise contour tone bar
    0xE0A8: [662,156,496,114,371],     // stix-modifier letter high-rise tone bar
    0xE0A9: [497,-167,647,49,619],     // stix-retracted (in-line diacritic)
    0xE0AA: [702,-506,376,30,346],     // stix-modifier letter fall-level contour tone bar
    0xE0AB: [662,156,496,114,371],     // stix-modifier letter low-rise tone bar
    0xE0AC: [702,-506,470,30,440],     // stix-modifier letter fall-rise-fall contour tone bar
    0xE0AD: [662,156,638,35,513],      // stix-modifier letter rise-fall tone bar
    0xE0AE: [662,0,423,55,345],        // stix-modifier letter rise tone bar
    0xE0AF: [662,0,423,55,345],        // stix-modifier letter fall tone bar
    0xE0B0: [735,-531,0,100,400],      // stix-left overangle (combining)
    0xE0B1: [-50,254,0,0,300],         // stix-left underangle (combining)
    0xE0B2: [-50,254,0,0,300],         // stix-right underangle (combining)
    0xE0B3: [683,218,541,32,457],      // stix-small fj ligature
    0xE0B4: [556,-220,313,55,258],     // stix-arrow hookleft
    0xE0B5: [556,-220,313,55,258],     // stix-arrow hookright
    0xE0B6: [449,-57,0,30,124],        // stix-maps-to relation tail
    0xE0B7: [324,-183,281,70,211],     // stix-bold center dot (very small filled square)
    0xE0B8: [943,11,1344,67,1302],     // stix-square root of 2
    0xE0B9: [943,11,1344,67,1302],     // stix-square root of 3
    0xE0BB: [622,101,685,48,637],      // stix-equal sign above tilde operator, vertical negation
    0xE0BC: [547,41,685,47,635],       // stix-times sign with dash through it
    0xE0BD: [662,218,710,15,660],      // stix-lowercase italic f with horizontal bar touching its upper edge
    0xE0BE: [757,218,1102,15,1073],    // stix-lowercase italic f with horizontal bar touching its upper edge and superscr u/c italic T
    0xE0BF: [836,236,636,50,586],      // stix-2 lines falling over 1 line rising
    0xE0C0: [836,236,636,50,586],      // stix-1 line falling over 2 lines rising
    0xE0C1: [836,236,636,50,586],      // stix-2 lines rising over 1 line falling
    0xE0C2: [836,236,636,50,586],      // stix-1 line rising over 2 lines falling
    0xE0C3: [386,-120,750,50,700],     // stix-dashed line over line
    0xE0C4: [478,-28,750,50,700],      // stix-dashed line over two lines
    0xE0C5: [478,-28,750,50,700],      // stix-two lines over dashed line
    0xE0C6: [286,-220,750,50,700],     // stix-single line, medium length
    0xE0C7: [402,-120,750,50,700],     // stix-dotted (3 dots) line over line
    0xE0C8: [386,-120,1000,50,950],    // stix-two long horizontal lines
    0xE0C9: [478,-28,1000,50,950],     // stix-three long horizontal lines
    0xE0CA: [544,38,1000,50,950],      // stix-four long horizontal lines
    0xE0CB: [386,-120,750,50,700],     // stix-two medium horizontal lines
    0xE0CC: [478,-28,750,50,700],      // stix-three medium horizontal lines
    0xE0CD: [544,38,750,50,700],       // stix-four medium horizontal lines
    0xE0CE: [836,236,636,50,586],      // stix-rising line, horizontal line, falling line
    0xE0CF: [836,236,636,50,586],      // stix-falling line, horizontal line, rising line
    0xE0D0: [836,236,636,50,586],      // stix-rising line, falling line
    0xE0D1: [836,236,636,50,586],      // stix-falling line, rising line
    0xE0D2: [692,186,926,83,843],      // stix-six carbon ring, corner down, double bonds lower left etc
    0xE0D3: [633,127,926,24,902],      // stix-six carbon ring, edge down, double bonds bottom edge etc
    0xE0D4: [633,127,926,24,902],      // stix-six carbon ring, edge down, double bonds top edge etc
    0xE0D5: [286,-220,1000,50,950],    // stix-single long chemical bond
    0xE0D6: [386,-120,750,50,700],     // stix chemical bond, line over dashed line
    0xE0D7: [583,79,762,50,712],       // stix-donut
    0xE0D8: [584,0,400,57,343],        // stix-arc-degrees (degree with dot below)
    0xE0D9: [665,0,255,56,199],        // stix-arc-minutes (prime with dot below)
    0xE0DA: [665,0,388,56,332],        // stix-arc-seconds (double prime with dot below)
    0xE0DB: [610,104,1472,86,1402],    // stix-boxed communication link
    0xE0DC: [354,-152,1134,65,1069],   // stix-communication link
    0xE0DD: [933,0,516,73,445],        // stix-days (roman d with dot below)
    0xE0DE: [933,0,500,57,439],        // stix-hours (roman h with dot below)
    0xE0DF: [754,0,778,92,699],        // stix-minutes (roman m with dot below)
    0xE0E0: [920,0,500,40,444],        // stix-period (roman p with dot below)
    0xE0E1: [757,0,389,81,318],        // stix-seconds (roman s with dot below)
    0xE0E2: [754,0,500,60,429],        // stix-years (roman y with dot below)
    0xE0E3: [638,134,842,35,807],      // stix-eclipse
    0xE0E5: [690,189,523,72,450],      // stix-dashed solidus
    0xE0E6: [690,189,523,72,450],      // stix-dashed backslash
    0xE0E7: [811,127,772,35,737],      // stix-narrow down-triangle
    0xE0E8: [532,26,1077,55,1022],     // stix-precedes sign followed by plus sign
    0xE0E9: [547,41,685,48,636],       // stix-outline plus sign
    0xE0EA: [661,158,910,45,865],      // stix-diamond with lines from corners
    0xE0EB: [567,58,716,45,671],       // stix-square with lines from corners
    0xE0EC: [862,-120,685,48,637],     // stix-equal with exclamation over
    0xE0ED: [819,312,511,192,319],     // stix-five vertical dots
    0xE0EE: [751,156,926,85,841],      // stix-I-beam shape with bullet overprinted in middle
    0xE0EF: [547,41,686,49,637],       // stix-plus with bullet overprinted in middle
    0xE0F1: [66,0,390,48,342],         // stix-short horizontal line
    0xE0F2: [936,157,1059,38,1033],    // stix-freaked smiley
    0xE0F3: [662,156,1059,196,862],    // stix-neutral smiley
    0xE0F4: [694,168,773,55,718],      // stix-light bulb
    0xE0F5: [672,146,926,55,872],      // stix-gray-filled circle
    0xE0F6: [747,114,909,23,886],      // stix-KernelIcon
    0xE0F7: [727,102,956,22,934],      // stix-MathematicaIcon
    0xE0F8: [474,89,500,163,336],      // stix-AliasDelimiter
    0xE0F9: [680,0,767,88,679],        // stix-ErrorIndicator
    0xE0FA: [474,89,297,62,235],       // stix-AliasIndicator
    0xE0FB: [680,0,1750,88,1662],      // stix-ControlKey
    0xE0FC: [680,0,1625,88,1537],      // stix-ReturnKey
    0xE0FD: [680,0,1625,88,1537],      // stix-EscapeKey
    0xE0FE: [680,0,1625,88,1537],      // stix-CommandKey
    0xE0FF: [680,0,1625,88,1537],      // stix-TabKey
    0xE100: [680,0,2032,88,1944],      // stix-SpaceKey
    0xE101: [680,0,1625,88,1537],      // stix-DeleteKey
    0xE102: [680,0,1608,88,1520],      // stix-AltKey
    0xE103: [680,0,2296,88,2208],      // stix-OptionKey
    0xE104: [409,-253,100,-64,164],    // stix-KeyBar
    0xE105: [680,0,2032,88,1944],      // stix-EnterKey
    0xE106: [680,0,2032,88,1944],      // stix-ShiftKey
    0xE107: [680,0,1625,88,1537],      // stix-Mod1Key
    0xE108: [680,0,1625,88,1537],      // stix-Mod2Key
    0xE109: [781,279,327,10,286],      // stix-LeftModified
    0xE10A: [781,279,250,41,178],      // stix-RightModified
    0xE10C: [384,-122,400,69,330],     // stix-extender for se/nw solid (normal) arrow
    0xE10D: [384,-122,400,69,330],     // stix-extender for sw/ne solid (normal) arrow
    0xE10E: [405,-101,652,193,459],    // stix-extender for vertical double arrow
    0xE10F: [386,-120,315,0,315],      // stix-extender for horizontal double arrow
    0xE110: [432,-28,652,124,528],     // stix-extender for se/nw double arrow
    0xE111: [432,-28,652,124,528],     // stix-extender for sw/ne double arrow
    0xE112: [662,156,926,55,872],      // stix-northeast arrow with dashed stem
    0xE113: [662,156,926,55,872],      // stix-southeast arrow with dashed stem
    0xE114: [662,156,926,54,871],      // stix-northwest arrow with dashed stem
    0xE115: [662,156,926,54,871],      // stix-southwest arrow with dashed stem
    0xE116: [214,-107,511,223,289],    // stix-extender for vertical dashed arrow
    0xE117: [286,-220,229,61,168],     // stix-extender for horizontal dashed arrow
    0xE118: [271,-134,277,70,207],     // stix-extender for se/nw dashed arrow
    0xE119: [271,-134,277,70,207],     // stix-extender for sw/ne dashed arrow
    0xE11A: [662,156,511,59,451],      // stix-up arrow with dotted stem
    0xE11B: [662,156,511,59,451],      // stix-down arrow with dotted stem
    0xE11C: [662,156,926,54,872],      // stix-northeast arrow with dotted stem
    0xE11D: [662,156,926,54,872],      // stix-southeast arrow with dotted stem
    0xE11E: [662,156,926,54,872],      // stix-northwest arrow with dotted stem
    0xE11F: [662,156,926,54,872],      // stix-southwest arrow with dotted stem
    0xE120: [411,-94,511,220,293],     // stix-extender for vertical dotted arrow
    0xE121: [290,-217,311,-3,314],     // stix-extender for horizontal dotted arrow
    0xE122: [382,-123,367,54,313],     // stix-extender for se/nw dotted arrow
    0xE123: [383,-124,367,54,313],     // stix-extender for sw/ne dotted arrow
    0xE124: [662,156,511,59,451],      // stix-up arrow with dot-dash stem
    0xE125: [662,156,511,59,451],      // stix-down arrow with dot-dash stem
    0xE126: [449,-57,926,54,872],      // stix-left arrow with dot-dash stem
    0xE127: [449,-57,926,54,872],      // stix-right arrow with dot-dash stem (E238)
    0xE128: [662,155,926,54,872],      // stix-northeast arrow with dot-dash stem
    0xE129: [662,156,926,55,872],      // stix-southeast arrow with dot-dash stem
    0xE12A: [662,156,926,54,871],      // stix-northwest arrow with dot-dash stem
    0xE12B: [661,156,926,54,872],      // stix-southwest arrow with dot-dash stem
    0xE12C: [404,-101,511,220,293],    // stix-extender for dot-dash up arrow
    0xE12D: [403,-100,511,220,293],    // stix-extender for dot-dash down arrow
    0xE12E: [290,-217,371,14,317],     // stix-extender for dot-dash left arrow
    0xE12F: [290,-217,371,54,357],     // stix-extender for dot-dash right arrow
    0xE130: [373,-134,379,70,309],     // stix-extender for nw dot-dash arrow
    0xE131: [373,-134,379,70,309],     // stix-extender for se dot-dash arrow
    0xE132: [373,-134,379,70,309],     // stix-extender for ne dot-dash arrow
    0xE133: [373,-134,379,70,309],     // stix-extender for sw dot-dash arrow
    0xE134: [486,-20,315,0,315],       // stix-extender for triple horizontal arrow
    0xE135: [405,-101,926,230,696],    // stix-extender for triple vertical arrow
    0xE136: [541,35,315,0,315],        // stix-extender for quadruple horizontal arrow
    0xE137: [405,-101,1033,229,805],   // stix-extender for quadruple vertical arrow
    0xE13A: [943,11,735,67,1302],      // stix-radical with horizontal (for single character under the radical)
    0xE13F: [-126,261,325,-1,326],     // stix-extensible horizontal for curly over and under braces (CMEX10 x3E rotated 90deg)
    0xE142: [955,-342,1820,-25,1830],  // stix-left end of extensible overparen (CMEX10 x40 rotated 90deg)
    0xE143: [955,-342,1820,-10,1845],  // stix-right end of extensible overparen (CMEX10 x30 rotated 90deg)
    0xE144: [352,261,1820,-25,1830],   // stix-left end of extensible underparen (CMEX10 x41 rotated 90deg)
    0xE145: [352,261,1820,-10,1845],   // stix-right end of extensible underparen (CMEX10 x31 rotated 90deg)
    0xE146: [955,-554,1820,-25,1830],  // stix-left end of extensible over square bracket (CMEX10 x34 rotated 90deg)
    0xE147: [955,-554,1820,-10,1845],  // stix-right end of extensible over square bracket (CMEX10 x32 rotated 90deg)
    0xE148: [140,261,1820,-25,1830],   // stix-left end of extensible under square bracket (CMEX10 x35 rotated 90deg)
    0xE149: [140,261,1820,-10,1845],   // stix-right end of extensible under square bracket (CMEX10 x33 rotated 90deg)
    0xE14C: [660,158,857,48,777],      // stix-not left triangle, vertical bar
    0xE14D: [660,158,857,80,809],      // stix-not vertical bar, right triangle
    0xE14E: [661,157,685,44,609],      // stix-not double less-than sign
    0xE14F: [661,157,685,76,641],      // stix-not double greater-than sign
    0xE150: [135,308,735,-25,746],     // stix-horizontal brace, down left piece
    0xE151: [135,308,735,-11,760],     // stix-horizontal brace, down right piece
    0xE152: [444,0,735,-25,746],       // stix-horizontal brace, upper left piece
    0xE153: [444,0,735,-11,760],       // stix-horizontal brace, upper right piece
    0xE17C: [683,10,499,28,471],       // stix-Mathematical sans-serif partial differential
    0xE17D: [674,0,666,31,635],        // stix-Mathematical sans-serif capital alpha
    0xE17E: [662,0,604,74,547],        // stix-Mathematical sans-serif capital beta
    0xE17F: [662,0,535,74,523],        // stix-Mathematical sans-serif capital gamma
    0xE180: [674,0,666,31,635],        // stix-Mathematical sans-serif capital delta
    0xE181: [662,0,583,74,540],        // stix-Mathematical sans-serif capital epsilon
    0xE182: [662,0,637,28,603],        // stix-Mathematical sans-serif capital zeta
    0xE183: [662,0,658,74,584],        // stix-Mathematical sans-serif capital eta
    0xE184: [676,14,714,30,684],       // stix-Mathematical sans-serif capital theta
    0xE185: [662,0,401,45,356],        // stix-Mathematical sans-serif capital iota
    0xE186: [662,0,634,74,630],        // stix-Mathematical sans-serif capital kappa
    0xE187: [674,0,666,31,635],        // stix-Mathematical sans-serif capital lambda
    0xE188: [662,0,843,75,768],        // stix-Mathematical sans-serif capital mu
    0xE189: [662,14,675,74,601],       // stix-Mathematical sans-serif capital nu
    0xE18A: [662,0,643,28,615],        // stix-Mathematical sans-serif capital xi
    0xE18B: [676,14,714,30,684],       // stix-Mathematical sans-serif capital omicron
    0xE18C: [662,0,658,74,584],        // stix-Mathematical sans-serif capital pi
    0xE18D: [662,0,525,74,512],        // stix-Mathematical sans-serif capital rho
    0xE18E: [676,14,714,30,684],       // stix-Mathematical sans-serif capital THETA symbol
    0xE18F: [662,0,624,26,594],        // stix-Mathematical sans-serif capital sigma
    0xE190: [662,0,608,15,593],        // stix-Mathematical sans-serif capital tau
    0xE191: [676,0,690,24,666],        // stix-Mathematical sans-serif capital upsilon
    0xE192: [662,0,716,23,693],        // stix-Mathematical sans-serif capital phi
    0xE193: [662,0,700,31,669],        // stix-Mathematical sans-serif capital chi
    0xE194: [681,0,724,12,712],        // stix-Mathematical sans-serif capital psi
    0xE195: [676,0,744,29,715],        // stix-Mathematical sans-serif capital omega
    0xE196: [463,10,537,28,532],       // stix-Mathematical sans-serif small alpha
    0xE197: [683,215,498,41,471],      // stix-Mathematical sans-serif small beta
    0xE198: [463,216,474,27,455],      // stix-Mathematical sans-serif small gamma
    0xE199: [683,10,499,28,471],       // stix-Mathematical sans-serif small delta
    0xE19A: [463,10,438,22,419],       // stix-Mathematical sans-serif small epsilon
    0xE19B: [683,213,416,33,408],      // stix-Mathematical sans-serif small zeta
    0xE19C: [463,215,494,41,443],      // stix-Mathematical sans-serif small eta
    0xE19D: [683,10,446,21,425],       // stix-Mathematical sans-serif small theta
    0xE19E: [464,10,270,57,269],       // stix-Mathematical sans-serif small iota
    0xE19F: [464,0,472,82,472],        // stix-Mathematical sans-serif small kappa
    0xE1A0: [683,11,489,8,478],        // stix-Mathematical sans-serif small lambda
    0xE1A1: [453,215,487,44,482],      // stix-Mathematical sans-serif small mu
    0xE1A2: [464,14,460,30,427],       // stix-Mathematical sans-serif small nu
    0xE1A3: [683,215,418,33,410],      // stix-Mathematical sans-serif small xi
    0xE1A4: [463,10,499,28,471],       // stix-Mathematical sans-serif small omicron
    0xE1A5: [453,10,507,7,487],        // stix-Mathematical sans-serif small pi
    0xE1A6: [462,216,498,48,470],      // stix-Mathematical sans-serif small rho
    0xE1A7: [463,212,416,33,414],      // stix-Mathematical sans-serif small FINAL sigma
    0xE1A8: [453,10,526,28,542],       // stix-Mathematical sans-serif small sigma
    0xE1A9: [453,10,426,2,410],        // stix-Mathematical sans-serif small tau
    0xE1AA: [463,10,503,41,463],       // stix-Mathematical sans-serif small upsilon
    0xE1AB: [464,216,632,34,600],      // stix-Mathematical sans-serif small phi
    0xE1AC: [463,215,399,-20,440],     // stix-Mathematical sans-serif small chi
    0xE1AD: [461,216,654,12,642],      // stix-Mathematical sans-serif small psi
    0xE1AE: [454,10,624,29,595],       // stix-Mathematical sans-serif small omega
    0xE1AF: [463,10,456,23,432],       // stix-Mathematical sans-serif epsilon symbol
    0xE1B0: [683,12,489,42,491],       // stix-Mathematical sans-serif theta symbol
    0xE1B1: [684,216,622,28,594],      // stix-Mathematical sans-serif phi symbol
    0xE1B2: [463,216,491,28,463],      // stix-Mathematical sans-serif rho symbol
    0xE1B3: [453,10,762,7,739],        // stix-Mathematical sans-serif pi symbol
    0xE28C: [474,-227,0,53,397],       // stix-slash for Polish L
    0xE28D: [734,-484,0,94,460],       // stix-double circumflex
    0xE28E: [622,101,685,48,637],      // stix-equals sign above tilde operator, slash negation
    0xE291: [955,-820,325,-1,326],     // stix-short horizontal extender, high
    0xE292: [662,0,1388,38,1350],      // stix-two summation signs next to each other
    0xE293: [763,260,1797,58,1739],    // stix-two summation signs next to each other
    0xE2FC: [756,218,722,15,707],      // stix-capital Alpha, Greek slashed
    0xE2FE: [756,217,667,17,593],      // stix-capital Beta, Greek slashed
    0xE300: [756,217,587,11,577],      // stix-capital Gamma, Greek slashed
    0xE302: [756,218,722,48,675],      // stix-capital Delta, Greek slashed
    0xE304: [756,217,611,12,597],      // stix-capital Epsilon, Greek slashed
    0xE306: [756,217,612,10,598],      // stix-capital Zeta, Greek slashed
    0xE308: [756,217,722,18,703],      // stix-capital Eta, Greek slashed
    0xE30A: [756,218,722,34,688],      // stix-capital Theta, Greek slashed
    0xE30C: [756,218,333,-24,438],     // stix-capital Iota, Greek slashed
    0xE30E: [756,217,731,33,723],      // stix-capital Kappa, Greek slashed
    0xE310: [756,218,702,15,687],      // stix-capital Lambda, Greek slashed
    0xE312: [756,217,889,12,864],      // stix-capital Mu, Greek slashed
    0xE314: [756,218,722,12,707],      // stix-capital Nu, Greek slashed
    0xE316: [756,217,643,29,614],      // stix-capital Xi, Greek slashed
    0xE318: [756,218,722,34,688],      // stix-capital Omicron, Greek slashed
    0xE31A: [756,217,722,18,703],      // stix-capital Pi, Greek slashed
    0xE31C: [756,218,557,16,565],      // stix-capital Rho, Greek slashed
    0xE31E: [756,217,624,30,600],      // stix-capital Sigma, Greek slashed
    0xE320: [756,218,611,17,593],      // stix-capital Tau, Greek slashed
    0xE322: [756,218,722,29,703],      // stix-capital Upsilon, Greek slashed
    0xE324: [756,217,763,35,728],      // stix-capital Phi, Greek slashed
    0xE326: [756,217,722,10,704],      // stix-capital Chi, Greek slashed
    0xE328: [756,217,743,22,724],      // stix-capital Psi, Greek slashed
    0xE32A: [756,217,744,29,715],      // stix-capital Omega, Greek slashed
    0xE364: [756,240,673,55,665],      // stix-capital stigma, Greek slashed
    0xE368: [756,218,557,8,645],       // stix-capital digamma, Greek slashed
    0xE36C: [773,218,645,-72,675],     // stix-capital koppa, Greek slashed
    0xE370: [756,218,708,7,668],       // stix-capital sampi, Greek slashed
    0xE3C2: [662,156,685,48,637],      // ??
    0xE3C3: [627,135,685,48,637],      // stix-not (vert) almost equal or equal to
    0xE3C4: [627,135,685,48,637],      // stix-not almost equal or equal to
    0xE3C7: [662,156,902,0,863],       // ??
    0xE3C8: [662,156,902,0,863]        // ??
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/NonUnicode/Regular/PrivateUse.js");
