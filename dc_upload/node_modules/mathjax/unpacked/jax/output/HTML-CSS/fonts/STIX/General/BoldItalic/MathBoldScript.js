/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/MathBoldScript.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold-italic'],
  {
    0x1D4D0: [699,21,984,50,955],      // <PERSON><PERSON><PERSON>AT<PERSON><PERSON> BOLD SCRIPT CAPITAL A
    0x1D4D1: [699,21,1060,55,985],     // MATHEMATICAL BOLD SCRIPT CAPITAL B
    0x1D4D2: [699,21,912,60,877],      // MATHEMATICAL BOLD SCRIPT CAPITAL C
    0x1D4D3: [699,21,991,60,906],      // MATHEMATICAL BOLD SCRIPT CAPITAL D
    0x1D4D4: [699,21,826,95,791],      // MATHEMATICAL BOLD SCRIPT CAPITAL E
    0x1D4D5: [699,21,1042,65,1025],    // MATHEMATICAL BOLD SCRIPT CAPITAL F
    0x1D4D6: [699,21,834,82,799],      // MATHEMATICAL BOLD SCRIPT CAPITAL G
    0x1D4D7: [699,21,1171,65,1154],    // MATHEMATICAL BOLD SCRIPT CAPITAL H
    0x1D4D8: [699,21,997,47,977],      // MATHEMATICAL BOLD SCRIPT CAPITAL I
    0x1D4D9: [699,224,906,19,886],     // MATHEMATICAL BOLD SCRIPT CAPITAL J
    0x1D4DA: [699,21,1154,45,1130],    // MATHEMATICAL BOLD SCRIPT CAPITAL K
    0x1D4DB: [699,21,1036,40,1015],    // MATHEMATICAL BOLD SCRIPT CAPITAL L
    0x1D4DC: [699,21,1300,60,1245],    // MATHEMATICAL BOLD SCRIPT CAPITAL M
    0x1D4DD: [699,21,1095,60,1078],    // MATHEMATICAL BOLD SCRIPT CAPITAL N
    0x1D4DE: [699,21,809,72,749],      // MATHEMATICAL BOLD SCRIPT CAPITAL O
    0x1D4DF: [699,21,1025,55,994],     // MATHEMATICAL BOLD SCRIPT CAPITAL P
    0x1D4E0: [699,52,809,72,749],      // MATHEMATICAL BOLD SCRIPT CAPITAL Q
    0x1D4E1: [699,21,1048,55,973],     // MATHEMATICAL BOLD SCRIPT CAPITAL R
    0x1D4E2: [699,21,816,81,781],      // MATHEMATICAL BOLD SCRIPT CAPITAL S
    0x1D4E3: [699,21,1030,65,1025],    // MATHEMATICAL BOLD SCRIPT CAPITAL T
    0x1D4E4: [699,21,964,60,904],      // MATHEMATICAL BOLD SCRIPT CAPITAL U
    0x1D4E5: [699,21,1040,60,1024],    // MATHEMATICAL BOLD SCRIPT CAPITAL V
    0x1D4E6: [699,21,1320,60,1306],    // MATHEMATICAL BOLD SCRIPT CAPITAL W
    0x1D4E7: [699,21,1033,64,1010],    // MATHEMATICAL BOLD SCRIPT CAPITAL X
    0x1D4E8: [699,224,989,60,963],     // MATHEMATICAL BOLD SCRIPT CAPITAL Y
    0x1D4E9: [699,21,996,50,976],      // MATHEMATICAL BOLD SCRIPT CAPITAL Z
    0x1D4EA: [462,14,942,35,865],      // MATHEMATICAL BOLD SCRIPT SMALL A
    0x1D4EB: [699,14,646,60,624],      // MATHEMATICAL BOLD SCRIPT SMALL B
    0x1D4EC: [462,14,764,35,683],      // MATHEMATICAL BOLD SCRIPT SMALL C
    0x1D4ED: [699,14,949,28,912],      // MATHEMATICAL BOLD SCRIPT SMALL D
    0x1D4EE: [462,14,726,35,648],      // MATHEMATICAL BOLD SCRIPT SMALL E
    0x1D4EF: [699,205,768,25,749],     // MATHEMATICAL BOLD SCRIPT SMALL F
    0x1D4F0: [462,224,819,27,771],     // MATHEMATICAL BOLD SCRIPT SMALL G
    0x1D4F1: [699,14,838,55,758],      // MATHEMATICAL BOLD SCRIPT SMALL H
    0x1D4F2: [698,14,558,40,534],      // MATHEMATICAL BOLD SCRIPT SMALL I
    0x1D4F3: [698,224,840,41,823],     // MATHEMATICAL BOLD SCRIPT SMALL J
    0x1D4F4: [699,14,810,55,730],      // MATHEMATICAL BOLD SCRIPT SMALL K
    0x1D4F5: [699,14,650,43,632],      // MATHEMATICAL BOLD SCRIPT SMALL L
    0x1D4F6: [462,14,1137,45,1057],    // MATHEMATICAL BOLD SCRIPT SMALL M
    0x1D4F7: [462,14,851,45,771],      // MATHEMATICAL BOLD SCRIPT SMALL N
    0x1D4F8: [462,14,848,35,780],      // MATHEMATICAL BOLD SCRIPT SMALL O
    0x1D4F9: [462,205,885,25,770],     // MATHEMATICAL BOLD SCRIPT SMALL P
    0x1D4FA: [462,205,913,35,833],     // MATHEMATICAL BOLD SCRIPT SMALL Q
    0x1D4FB: [462,0,677,40,648],       // MATHEMATICAL BOLD SCRIPT SMALL R
    0x1D4FC: [557,14,562,51,449],      // MATHEMATICAL BOLD SCRIPT SMALL S
    0x1D4FD: [669,14,618,47,612],      // MATHEMATICAL BOLD SCRIPT SMALL T
    0x1D4FE: [449,14,842,31,762],      // MATHEMATICAL BOLD SCRIPT SMALL U
    0x1D4FF: [458,14,732,40,670],      // MATHEMATICAL BOLD SCRIPT SMALL V
    0x1D500: [458,14,1012,40,950],     // MATHEMATICAL BOLD SCRIPT SMALL W
    0x1D501: [462,14,820,63,740],      // MATHEMATICAL BOLD SCRIPT SMALL X
    0x1D502: [449,224,784,40,711],     // MATHEMATICAL BOLD SCRIPT SMALL Y
    0x1D503: [493,14,782,61,702]       // MATHEMATICAL BOLD SCRIPT SMALL Z
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/BoldItalic/MathBoldScript.js");
