/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscMathSymbolsA.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x27C0: [584,0,685,50,634],        // THREE DIMENSIONAL ANGLE
    0x27C1: [811,127,1145,35,1110],    // WHITE TRIANGLE CONTAINING SMALL WHITE TRIANGLE
    0x27C2: [662,0,693,52,641],        // PERPENDICULAR
    0x27C3: [529,27,685,60,625],       // OPEN SUBSET
    0x27C4: [529,27,685,61,626],       // OPEN SUPERSET
    0x27C5: [702,198,455,55,400],      // LEFT S-SHAPED BAG DELIMITER
    0x27C6: [702,198,455,55,400],      // RIGHT S-SHAPED BAG DELIMITER
    0x27C7: [536,29,620,31,589],       // OR WITH DOT INSIDE
    0x27C8: [533,25,966,60,906],       // REVERSE SOLIDUS PRECEDING SUBSET
    0x27C9: [533,25,966,60,906],       // SUBSET PRECEDING SOLIDUS
    0x27CB: [662,156,838,0,799],       // ??
    0x27CC: [806,213,325,20,325],      // LONG DIVISION
    0x27CD: [662,156,838,0,799],       // ??
    0x27D0: [744,242,1064,39,1025],    // WHITE DIAMOND WITH CENTRED DOT
    0x27D1: [536,29,620,31,589],       // AND WITH DOT
    0x27D2: [536,31,620,48,572],       // ELEMENT OF OPENING UPWARDS
    0x27D3: [584,0,685,50,634],        // LOWER RIGHT CORNER WITH DOT
    0x27D4: [584,0,685,50,634],        // UPPER LEFT CORNER WITH DOT
    0x27D5: [582,80,1019,40,965],      // LEFT OUTER JOIN
    0x27D6: [582,80,1019,54,979],      // RIGHT OUTER JOIN
    0x27D7: [582,80,1228,40,1188],     // FULL OUTER JOIN
    0x27D8: [718,213,866,50,816],      // LARGE UP TACK
    0x27D9: [718,213,866,50,816],      // LARGE DOWN TACK
    0x27DA: [662,0,1376,64,1312],      // LEFT AND RIGHT DOUBLE TURNSTILE
    0x27DB: [662,0,1376,64,1312],      // LEFT AND RIGHT TACK
    0x27DC: [403,-103,849,50,799],     // LEFT MULTIMAP
    0x27DD: [450,-57,1574,55,1519],    // LONG RIGHT TACK
    0x27DE: [450,-57,1574,55,1519],    // LONG LEFT TACK
    0x27DF: [693,187,502,101,401],     // UP TACK WITH CIRCLE ABOVE
    0x27E0: [795,289,790,45,745],      // LOZENGE DIVIDED BY HORIZONTAL RULE
    0x27E1: [589,87,764,45,719],       // WHITE CONCAVE-SIDED DIAMOND
    0x27E2: [589,87,803,45,758],       // WHITE CONCAVE-SIDED DIAMOND WITH LEFTWARDS TICK
    0x27E3: [589,87,803,45,758],       // WHITE CONCAVE-SIDED DIAMOND WITH RIGHTWARDS TICK
    0x27E4: [662,158,1182,45,1137],    // WHITE SQUARE WITH LEFTWARDS TICK
    0x27E5: [662,158,1182,45,1137],    // WHITE SQUARE WITH RIGHTWARDS TICK
    0x27E6: [717,213,504,188,482],     // MATHEMATICAL LEFT WHITE SQUARE BRACKET
    0x27E7: [717,213,504,22,316],      // MATHEMATICAL RIGHT WHITE SQUARE BRACKET
    0x27EA: [719,213,610,73,545],      // MATHEMATICAL LEFT DOUBLE ANGLE BRACKET
    0x27EB: [719,213,610,65,537],      // MATHEMATICAL RIGHT DOUBLE ANGLE BRACKET
    0x27EC: [719,213,488,178,466],     // MATHEMATICAL LEFT WHITE TORTOISE SHELL BRACKET
    0x27ED: [719,213,488,22,310]       // MATHEMATICAL RIGHT WHITE TORTOISE SHELL BRACKET
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MiscMathSymbolsA.js");
