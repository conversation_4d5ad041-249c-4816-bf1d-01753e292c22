/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscSymbolsAndArrows.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral'],
  {
    0x2B12: [662,157,910,45,865],      // SQUARE WITH TOP HALF BLACK
    0x2B13: [662,157,910,45,865],      // SQUARE WITH BOTTOM HALF BLACK
    0x2B14: [662,157,910,45,865],      // SQUARE WITH UPPER RIGHT DIAGONAL HALF BLOCK
    0x2B15: [662,157,910,45,865],      // SQUARE WITH LOWER LEFT DIAGONAL HALF BLOCK
    0x2B16: [744,242,1064,39,1025],    // DIAMOND WITH LEFT HALF BLACK
    0x2B17: [744,242,1064,39,1025],    // DIAMOND WITH RIGHT HALF BLACK
    0x2B18: [744,242,1064,39,1025],    // DIAMOND WITH TOP HALF BLACK
    0x2B19: [744,242,1064,39,1025],    // DIAMOND WITH BOTTOM HALF BLACK
    0x2B1A: [662,157,910,45,865],      // DOTTED SQUARE
    0x2B1B: [780,180,1040,40,1000],    // BLACK LARGE SQUARE
    0x2B1C: [780,180,1040,40,1000],    // WHITE LARGE SQUARE
    0x2B1D: [332,-172,240,50,190],     // BLACK VERY SMALL SQUARE
    0x2B1E: [332,-172,240,50,190],     // WHITE VERY SMALL SQUARE
    0x2B1F: [690,105,910,36,874],      // BLACK PENTAGON
    0x2B20: [690,105,910,36,874],      // WHITE PENTAGON
    0x2B21: [680,178,910,82,828],      // WHITE HEXAGON
    0x2B22: [680,178,910,82,828],      // BLACK HEXAGON
    0x2B23: [633,127,926,24,902],      // HORIZONTAL BLACK HEXAGON
    0x2B24: [785,282,1207,70,1137],    // BLACK LARGE CIRCLE
    0x2B25: [581,96,779,45,734],       // BLACK MEDIUM DIAMOND
    0x2B26: [581,96,779,45,734],       // WHITE MEDIUM DIAMOND
    0x2B27: [609,105,544,40,504],      // BLACK MEDIUM LOZENGE
    0x2B28: [609,105,544,40,504],      // WHITE MEDIUM LOZENGE
    0x2B29: [488,-16,523,26,497],      // BLACK SMALL DIAMOND
    0x2B2A: [488,-16,357,26,331],      // BLACK SMALL LOZENGE
    0x2B2B: [488,-16,357,26,331],      // WHITE SMALL LOZENGE
    0x2B2C: [500,-4,842,50,792],       // BLACK HORIZONTAL ELLIPSE
    0x2B2D: [500,-4,842,50,792],       // WHITE HORIZONTAL ELLIPSE
    0x2B2E: [623,119,596,50,546],      // BLACK VERTICAL ELLIPSE
    0x2B2F: [623,119,596,50,546],      // WHITE VERTICAL ELLIPSE
    0x2B30: [448,-57,926,70,856],      // LEFT ARROW WITH SMALL CIRCLE
    0x2B31: [739,232,926,60,866],      // THREE LEFTWARDS ARROWS
    0x2B32: [569,61,1200,52,1147],     // LEFT ARROW WITH CIRCLED PLUS
    0x2B33: [449,-58,1574,55,1519],    // LONG LEFTWARDS SQUIGGLE ARROW
    0x2B34: [450,-57,926,56,871],      // LEFTWARDS TWO-HEADED ARROW WITH VERTICAL STROKE
    0x2B35: [450,-57,926,55,871],      // LEFTWARDS TWO-HEADED ARROW WITH DOUBLE VERTICAL STROKE
    0x2B36: [450,-57,926,55,871],      // LEFTWARDS TWO-HEADED ARROW FROM BAR
    0x2B37: [449,-57,1412,55,1357],    // LEFTWARDS TWO-HEADED TRIPLE-DASH ARROW
    0x2B38: [449,-57,926,55,873],      // LEFTWARDS ARROW WITH DOTTED STEM
    0x2B39: [450,-57,926,55,871],      // LEFTWARDS ARROW WITH TAIL WITH VERTICAL STROKE
    0x2B3A: [450,-57,926,55,871],      // LEFTWARDS ARROW WITH TAIL WITH DOUBLE VERTICAL STROKE
    0x2B3B: [449,-57,926,55,871],      // LEFTWARDS TWO-HEADED ARROW WITH TAIL
    0x2B3C: [450,-57,926,55,871],      // LEFTWARDS TWO-HEADED ARROW WITH TAIL WITH VERTICAL STROKE
    0x2B3D: [450,-57,926,50,876],      // LEFTWARDS TWO-HEADED ARROW WITH TAIL WITH DOUBLE VERTICAL STROKE
    0x2B3E: [449,-57,926,55,871],      // LEFTWARDS ARROW THROUGH X
    0x2B3F: [449,-57,926,55,871],      // WAVE ARROW POINTING DIRECTLY LEFT
    0x2B40: [565,-57,926,55,871],      // EQUALS SIGN ABOVE LEFTWARDS ARROW
    0x2B41: [508,-57,926,55,871],      // REVERSE TILDE OPERATOR ABOVE LEFTWARDS ARROW
    0x2B42: [449,141,926,55,871],      // LEFTWARDS ARROW ABOVE REVERSE ALMOST EQUAL TO
    0x2B43: [532,26,926,45,871],       // RIGHTWARDS ARROW THROUGH LESS-THAN
    0x2B44: [532,26,926,45,871],       // RIGHTWARDS ARROW THROUGH SUBSET
    0x2B45: [701,195,928,55,873],      // LEFTWARDS QUADRUPLE ARROW
    0x2B46: [701,195,928,55,873],      // RIGHTWARDS QUADRUPLE ARROW
    0x2B47: [508,-57,926,55,871],      // REVERSE TILDE OPERATOR ABOVE RIGHTWARDS ARROW
    0x2B48: [449,141,926,55,871],      // RIGHTWARDS ARROW ABOVE REVERSE ALMOST EQUAL TO
    0x2B49: [508,-57,926,55,871],      // TILDE OPERATOR ABOVE LEFTWARDS ARROW
    0x2B4A: [449,141,926,55,871],      // LEFTWARDS ARROW ABOVE ALMOST EQUAL TO
    0x2B4B: [449,2,926,55,871],        // LEFTWARDS ARROW ABOVE REVERSE TILDE OPERATOR
    0x2B4C: [449,2,926,55,871],        // RIGHTWARDS ARROW ABOVE REVERSE TILDE OPERATOR
    0x2B50: [619,30,794,60,734],       // WHITE MEDIUM STAR
    0x2B51: [619,30,794,60,734],       // BLACK MEDIUM STAR
    0x2B52: [597,13,700,35,665],       // WHITE SMALL STAR
    0x2B53: [712,126,865,45,840],      // BLACK RIGHT-POINTING PENTAGON
    0x2B54: [712,127,865,45,840]       // WHITE RIGHT-POINTING PENTAGON
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Regular/MiscSymbolsAndArrows.js");
