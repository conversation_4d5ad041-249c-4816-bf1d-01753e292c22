/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MathBold.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold'],
  {
    0x1D400: [690,0,722,9,689],        // MATHEMATICAL BOLD CAPITAL A
    0x1D401: [676,0,667,16,619],       // MATHEMATICAL BOLD CAPITAL B
    0x1D402: [691,19,722,49,687],      // MATHEMATICAL BOLD CAPITAL C
    0x1D403: [676,0,722,14,690],       // MATHEMATICAL BOLD CAPITAL D
    0x1D404: [676,0,667,16,641],       // MATHEMATICAL BOLD CAPITAL E
    0x1D405: [676,0,611,16,583],       // MATHEMATICAL BOLD CAPITAL F
    0x1D406: [691,19,778,37,755],      // MATHEMATICAL BOLD CAPITAL G
    0x1D407: [676,0,778,21,759],       // MATHEMATICAL BOLD CAPITAL H
    0x1D408: [676,0,389,20,370],       // MATHEMATICAL BOLD CAPITAL I
    0x1D409: [676,96,500,3,478],       // MATHEMATICAL BOLD CAPITAL J
    0x1D40A: [676,0,778,30,769],       // MATHEMATICAL BOLD CAPITAL K
    0x1D40B: [676,0,667,19,638],       // MATHEMATICAL BOLD CAPITAL L
    0x1D40C: [676,0,944,14,921],       // MATHEMATICAL BOLD CAPITAL M
    0x1D40D: [676,18,722,16,701],      // MATHEMATICAL BOLD CAPITAL N
    0x1D40E: [691,19,778,35,743],      // MATHEMATICAL BOLD CAPITAL O
    0x1D40F: [676,0,611,16,600],       // MATHEMATICAL BOLD CAPITAL P
    0x1D410: [691,176,778,35,743],     // MATHEMATICAL BOLD CAPITAL Q
    0x1D411: [676,0,722,26,716],       // MATHEMATICAL BOLD CAPITAL R
    0x1D412: [692,19,556,35,513],      // MATHEMATICAL BOLD CAPITAL S
    0x1D413: [676,0,667,31,636],       // MATHEMATICAL BOLD CAPITAL T
    0x1D414: [676,19,722,16,701],      // MATHEMATICAL BOLD CAPITAL U
    0x1D415: [676,18,722,16,701],      // MATHEMATICAL BOLD CAPITAL V
    0x1D416: [676,15,1000,19,981],     // MATHEMATICAL BOLD CAPITAL W
    0x1D417: [676,0,722,16,699],       // MATHEMATICAL BOLD CAPITAL X
    0x1D418: [676,0,722,15,699],       // MATHEMATICAL BOLD CAPITAL Y
    0x1D419: [676,0,667,28,634],       // MATHEMATICAL BOLD CAPITAL Z
    0x1D41A: [473,14,500,25,488],      // MATHEMATICAL BOLD SMALL A
    0x1D41B: [676,14,556,17,521],      // MATHEMATICAL BOLD SMALL B
    0x1D41C: [473,14,444,25,430],      // MATHEMATICAL BOLD SMALL C
    0x1D41D: [676,14,556,25,534],      // MATHEMATICAL BOLD SMALL D
    0x1D41E: [473,14,444,25,427],      // MATHEMATICAL BOLD SMALL E
    0x1D41F: [691,0,333,14,389],       // MATHEMATICAL BOLD SMALL F
    0x1D420: [473,206,500,28,483],     // MATHEMATICAL BOLD SMALL G
    0x1D421: [676,0,556,15,534],       // MATHEMATICAL BOLD SMALL H
    0x1D422: [691,0,278,15,256],       // MATHEMATICAL BOLD SMALL I
    0x1D423: [691,203,333,-57,263],    // MATHEMATICAL BOLD SMALL J
    0x1D424: [676,0,556,22,543],       // MATHEMATICAL BOLD SMALL K
    0x1D425: [676,0,278,15,256],       // MATHEMATICAL BOLD SMALL L
    0x1D426: [473,0,833,15,814],       // MATHEMATICAL BOLD SMALL M
    0x1D427: [473,0,556,21,539],       // MATHEMATICAL BOLD SMALL N
    0x1D428: [473,14,500,25,476],      // MATHEMATICAL BOLD SMALL O
    0x1D429: [473,205,556,19,524],     // MATHEMATICAL BOLD SMALL P
    0x1D42A: [473,205,556,34,536],     // MATHEMATICAL BOLD SMALL Q
    0x1D42B: [473,0,444,28,434],       // MATHEMATICAL BOLD SMALL R
    0x1D42C: [473,14,389,25,361],      // MATHEMATICAL BOLD SMALL S
    0x1D42D: [630,12,333,19,332],      // MATHEMATICAL BOLD SMALL T
    0x1D42E: [461,14,556,16,538],      // MATHEMATICAL BOLD SMALL U
    0x1D42F: [461,14,500,21,485],      // MATHEMATICAL BOLD SMALL V
    0x1D430: [461,14,722,23,707],      // MATHEMATICAL BOLD SMALL W
    0x1D431: [461,0,500,12,484],       // MATHEMATICAL BOLD SMALL X
    0x1D432: [461,205,500,16,482],     // MATHEMATICAL BOLD SMALL Y
    0x1D433: [461,0,444,21,420],       // MATHEMATICAL BOLD SMALL Z
    0x1D7CE: [688,13,500,24,476],      // MATHEMATICAL BOLD DIGIT ZERO
    0x1D7CF: [688,0,500,65,441],       // MATHEMATICAL BOLD DIGIT ONE
    0x1D7D0: [688,0,500,17,478],       // MATHEMATICAL BOLD DIGIT TWO
    0x1D7D1: [688,14,500,16,468],      // MATHEMATICAL BOLD DIGIT THREE
    0x1D7D2: [688,0,500,19,476],       // MATHEMATICAL BOLD DIGIT FOUR
    0x1D7D3: [676,8,500,22,470],       // MATHEMATICAL BOLD DIGIT FIVE
    0x1D7D4: [688,13,500,28,475],      // MATHEMATICAL BOLD DIGIT SIX
    0x1D7D5: [676,0,500,17,477],       // MATHEMATICAL BOLD DIGIT SEVEN
    0x1D7D6: [688,13,500,28,472],      // MATHEMATICAL BOLD DIGIT EIGHT
    0x1D7D7: [688,13,500,26,473]       // MATHEMATICAL BOLD DIGIT NINE
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Bold/MathBold.js");
