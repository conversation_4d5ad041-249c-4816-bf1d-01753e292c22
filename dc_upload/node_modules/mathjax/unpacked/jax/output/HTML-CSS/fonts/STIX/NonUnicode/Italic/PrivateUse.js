/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Italic/PrivateUse.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXNonUnicode-italic'],
  {
    0xE09C: [756,218,753,37,787],      // stix-capital A italic double-slashed
    0xE09D: [756,218,706,42,732],      // stix-capital E italic double-slashed
    0xE09E: [756,218,624,42,724],      // stix-capital F italic double-slashed
    0xE0B3: [681,207,500,-141,504],    // stix-small fj ligature
    0xE154: [653,0,671,3,606],         // stix-oblique open face capital letter A
    0xE155: [653,0,686,17,676],        // stix-oblique open face capital letter B
    0xE156: [653,0,639,17,664],        // stix-oblique open face capital letter E
    0xE157: [653,0,469,18,664],        // stix-oblique open face capital letter F
    0xE158: [666,18,702,35,702],       // stix-oblique open face capital letter G
    0xE159: [653,0,320,21,350],        // stix-oblique open face capital letter I
    0xE15A: [653,18,562,16,595],       // stix-oblique open face capital letter J
    0xE15B: [653,0,700,17,730],        // stix-oblique open face capital letter K
    0xE15C: [653,0,608,18,524],        // stix-oblique open face capital letter L
    0xE15D: [653,0,858,25,892],        // stix-oblique open face capital letter M
    0xE15E: [666,18,723,35,713],       // stix-oblique open face capital letter O
    0xE15F: [666,18,624,24,669],       // stix-oblique open face capital letter S
    0xE160: [653,0,463,30,682],        // stix-oblique open face capital letter T
    0xE161: [653,14,648,33,716],       // stix-oblique open face capital letter U
    0xE162: [653,0,492,75,678],        // stix-oblique open face capital letter V
    0xE163: [653,0,810,100,963],       // stix-oblique open face capital letter W
    0xE164: [653,0,650,-24,770],       // stix-oblique open face capital letter X
    0xE165: [653,0,458,42,658],        // stix-oblique open face capital letter Y
    0xE166: [441,11,566,40,521],       // stix-oblique open face small letter a
    0xE167: [683,11,598,27,558],       // stix-oblique open face small letter b
    0xE168: [441,11,504,40,480],       // stix-oblique open face small letter c
    0xE169: [683,0,331,28,519],        // stix-oblique open face small letter f
    0xE16A: [441,220,599,9,572],       // stix-oblique open face small letter g
    0xE16B: [683,0,588,27,543],        // stix-oblique open face small letter h
    0xE16C: [683,0,545,25,530],        // stix-oblique open face small letter k
    0xE16D: [683,0,306,27,356],        // stix-oblique open face small letter l
    0xE16E: [441,0,857,27,812],        // stix-oblique open face small letter m
    0xE16F: [441,0,588,27,543],        // stix-oblique open face small letter n
    0xE170: [441,11,534,40,494],       // stix-oblique open face small letter o
    0xE171: [441,218,628,5,588],       // stix-oblique open face small letter p
    0xE172: [441,218,604,40,574],      // stix-oblique open face small letter q
    0xE173: [442,0,332,27,467],        // stix-oblique open face small letter r
    0xE174: [441,11,502,34,468],       // stix-oblique open face small letter s
    0xE175: [633,10,323,20,328],       // stix-oblique open face small letter t
    0xE176: [428,13,588,40,556],       // stix-oblique open face small letter u
    0xE177: [428,0,395,45,462],        // stix-oblique open face small letter v
    0xE178: [428,0,639,56,690],        // stix-oblique open face small letter w
    0xE179: [428,0,557,14,554],        // stix-oblique open face small letter x
    0xE17A: [428,218,471,5,538],       // stix-oblique open face small letter y
    0xE17B: [428,0,540,21,530],        // stix-oblique open face small letter z
    0xE1B4: [676,14,500,86,578],       // stix-Mathematical sans-serif italic digit 0
    0xE1B5: [677,0,500,223,469],       // stix-Mathematical sans-serif italic digit 1
    0xE1B6: [676,0,500,35,574],        // stix-Mathematical sans-serif italic digit 2
    0xE1B7: [676,14,500,44,544],       // stix-Mathematical sans-serif italic digit 3
    0xE1B8: [676,0,500,52,547],        // stix-Mathematical sans-serif italic digit 4
    0xE1B9: [676,14,500,49,626],       // stix-Mathematical sans-serif italic digit 5
    0xE1BA: [684,14,500,83,617],       // stix-Mathematical sans-serif italic digit 6
    0xE1BB: [662,8,500,146,616],       // stix-Mathematical sans-serif italic digit 7
    0xE1BC: [676,14,500,81,560],       // stix-Mathematical sans-serif italic digit 8
    0xE1BD: [676,21,500,51,579],       // stix-Mathematical sans-serif italic digit 9
    0xE1BE: [683,10,536,45,527],       // stix-Mathematical sans-serif italic partial differential
    0xE1BF: [674,0,660,28,632],        // stix-Mathematical sans-serif italic capital alpha
    0xE1C0: [662,0,662,60,627],        // stix-Mathematical sans-serif italic capital beta
    0xE1C1: [662,0,562,60,665],        // stix-Mathematical sans-serif italic capital gamma
    0xE1C2: [674,0,660,28,632],        // stix-Mathematical sans-serif italic capital delta
    0xE1C3: [662,0,639,60,664],        // stix-Mathematical sans-serif italic capital epsilon
    0xE1C4: [662,0,698,25,760],        // stix-Mathematical sans-serif italic capital zeta
    0xE1C5: [662,0,700,60,735],        // stix-Mathematical sans-serif italic capital eta
    0xE1C6: [676,14,780,75,755],       // stix-Mathematical sans-serif italic capital theta
    0xE1C7: [662,0,433,50,503],        // stix-Mathematical sans-serif italic capital iota
    0xE1C8: [662,0,631,60,715],        // stix-Mathematical sans-serif italic capital kappa
    0xE1C9: [674,0,664,20,624],        // stix-Mathematical sans-serif italic capital lambda
    0xE1CA: [662,0,890,60,918],        // stix-Mathematical sans-serif italic capital mu
    0xE1CB: [662,14,724,60,752],       // stix-Mathematical sans-serif italic capital nu
    0xE1CC: [662,0,722,47,754],        // stix-Mathematical sans-serif italic capital xi
    0xE1CD: [676,14,780,75,755],       // stix-Mathematical sans-serif italic capital omicron
    0xE1CE: [662,0,700,60,735],        // stix-Mathematical sans-serif italic capital pi
    0xE1CF: [662,0,538,60,624],        // stix-Mathematical sans-serif italic capital rho
    0xE1D0: [676,14,780,75,755],       // stix-Mathematical sans-serif italic capital THETA symbol
    0xE1D1: [662,0,654,21,706],        // stix-Mathematical sans-serif italic capital sigma
    0xE1D2: [662,0,585,72,659],        // stix-Mathematical sans-serif italic capital tau
    0xE1D3: [676,0,593,83,725],        // stix-Mathematical sans-serif italic capital upsilon
    0xE1D4: [662,0,736,52,736],        // stix-Mathematical sans-serif italic capital phi
    0xE1D5: [662,0,722,20,795],        // stix-Mathematical sans-serif italic capital chi
    0xE1D6: [681,0,712,105,805],       // stix-Mathematical sans-serif italic capital psi
    0xE1D7: [676,0,795,39,795],        // stix-Mathematical sans-serif italic capital omega
    0xE1D8: [463,10,586,47,616],       // stix-Mathematical sans-serif italic small alpha
    0xE1D9: [683,215,535,-12,559],     // stix-Mathematical sans-serif italic small beta
    0xE1DA: [463,216,503,84,527],      // stix-Mathematical sans-serif italic small gamma
    0xE1DB: [683,10,497,30,537],       // stix-Mathematical sans-serif italic small delta
    0xE1DC: [463,10,494,35,484],       // stix-Mathematical sans-serif italic small epsilon
    0xE1DD: [683,213,429,32,454],      // stix-Mathematical sans-serif italic small zeta
    0xE1DE: [463,215,493,38,486],      // stix-Mathematical sans-serif italic small eta
    0xE1DF: [683,10,518,65,511],       // stix-Mathematical sans-serif italic small theta
    0xE1E0: [464,10,296,56,268],       // stix-Mathematical sans-serif italic small iota
    0xE1E1: [464,0,472,38,517],        // stix-Mathematical sans-serif italic small kappa
    0xE1E2: [683,11,536,18,502],       // stix-Mathematical sans-serif italic small lambda
    0xE1E3: [453,215,561,-9,536],      // stix-Mathematical sans-serif italic small mu
    0xE1E4: [464,14,376,41,416],       // stix-Mathematical sans-serif italic small nu
    0xE1E5: [683,215,434,43,464],      // stix-Mathematical sans-serif italic small xi
    0xE1E6: [463,10,533,45,505],       // stix-Mathematical sans-serif italic small omicron
    0xE1E7: [453,10,565,45,589],       // stix-Mathematical sans-serif italic small pi
    0xE1E8: [462,216,534,-33,510],     // stix-Mathematical sans-serif italic small rho
    0xE1E9: [463,212,436,52,500],      // stix-Mathematical sans-serif italic small FINAL sigma
    0xE1EA: [453,10,607,45,625],       // stix-Mathematical sans-serif italic small sigma
    0xE1EB: [453,10,468,42,486],       // stix-Mathematical sans-serif italic small tau
    0xE1EC: [463,10,514,61,490],       // stix-Mathematical sans-serif italic small upsilon
    0xE1ED: [464,216,665,55,641],      // stix-Mathematical sans-serif italic small phi
    0xE1EE: [463,215,514,-72,552],     // stix-Mathematical sans-serif italic small chi
    0xE1EF: [461,216,654,75,705],      // stix-Mathematical sans-serif italic small psi
    0xE1F0: [454,10,630,50,636],       // stix-Mathematical sans-serif italic small omega
    0xE1F1: [463,10,462,45,467],       // stix-Mathematical sans-serif italic epsilon symbol
    0xE1F2: [683,12,534,45,525],       // stix-Mathematical sans-serif italic theta symbol
    0xE1F3: [684,216,648,48,630],      // stix-Mathematical sans-serif italic phi symbol
    0xE1F4: [463,216,536,38,518],      // stix-Mathematical sans-serif italic rho symbol
    0xE1F5: [453,10,795,40,811],       // stix-Mathematical sans-serif italic pi symbol
    0xE262: [460,11,570,56,514],       // stix-old style digit 0
    0xE266: [460,0,570,100,415],       // stix-old style digit 1
    0xE26A: [460,0,570,59,487],        // stix-old style digit 2
    0xE26E: [461,217,570,40,513],      // stix-old style digit 3
    0xE272: [450,217,570,17,542],      // stix-old style digit 4
    0xE276: [450,218,570,23,536],      // stix-old style digit 5
    0xE27A: [668,10,570,28,553],       // stix-old style digit 6
    0xE27E: [450,217,570,40,543],      // stix-old style digit 7
    0xE282: [668,10,570,50,519],       // stix-old style digit 8
    0xE286: [460,217,570,23,526],      // stix-old style digit 9
    0xE294: [756,218,753,37,754],      // stix-capital A italic slashed
    0xE296: [756,218,698,42,686],      // stix-capital B italic slashed
    0xE298: [756,218,678,52,716],      // stix-capital C italic slashed
    0xE29A: [756,218,830,42,793],      // stix-capital D italic slashed
    0xE29C: [756,218,706,42,724],      // stix-capital E italic slashed
    0xE29E: [756,217,624,42,724],      // stix-capital F italic slashed
    0xE2A0: [756,217,768,52,766],      // stix-capital G italic slashed
    0xE2A2: [756,218,825,42,863],      // stix-capital H italic slashed
    0xE2A4: [756,218,429,-7,467],      // stix-capital I italic slashed
    0xE2A6: [756,218,530,60,568],      // stix-capital J italic slashed
    0xE2A8: [756,218,766,42,804],      // stix-capital K italic slashed
    0xE2AA: [756,218,696,42,654],      // stix-capital L italic slashed
    0xE2AC: [756,218,969,42,1007],     // stix-capital M italic slashed
    0xE2AE: [756,218,799,42,837],      // stix-capital N italic slashed
    0xE2B0: [756,218,764,52,739],      // stix-capital O italic slashed
    0xE2B2: [756,217,581,14,710],      // stix-capital P italic slashed
    0xE2B4: [756,217,764,52,739],      // stix-capital Q italic slashed
    0xE2B6: [756,218,699,42,682],      // stix-capital R italic slashed
    0xE2B8: [756,218,557,52,576],      // stix-capital S italic slashed
    0xE2BA: [756,218,532,19,647],      // stix-capital T italic slashed
    0xE2BC: [756,218,706,67,771],      // stix-capital U italic slashed
    0xE2BE: [756,218,605,56,730],      // stix-capital V italic slashed
    0xE2C0: [756,218,831,62,956],      // stix-capital W italic slashed
    0xE2C2: [756,218,737,27,755],      // stix-capital X italic slashed
    0xE2C4: [756,218,492,1,647],       // stix-capital Y italic slashed
    0xE2C6: [756,218,686,62,714],      // stix-capital Z italic slashed
    0xE2C8: [756,240,565,42,533],      // stix-lowercase a italic slashed
    0xE2CA: [756,240,530,47,530],      // stix-lowercase b italic slashed
    0xE2CC: [756,240,477,33,501],      // stix-lowercase c italic slashed
    0xE2CE: [756,240,586,14,581],      // stix-lowercase d italic slashed
    0xE2D0: [756,240,490,8,475],       // stix-lowercase e italic slashed
    0xE2D2: [756,240,582,-4,704],      // stix-lowercase f italic slashed
    0xE2D4: [756,240,515,22,513],      // stix-lowercase g italic slashed
    0xE2D6: [756,240,577,47,545],      // stix-lowercase h italic slashed
    0xE2D8: [756,217,326,-9,454],      // stix-lowercase i italic slashed
    0xE2DA: [755,240,550,-54,653],     // stix-lowercase j italic slashed
    0xE2DC: [756,240,554,57,591],      // stix-lowercase k italic slashed
    0xE2DE: [756,217,335,-14,449],     // stix-lowercase l italic slashed
    0xE2E0: [756,240,823,32,791],      // stix-lowercase m italic slashed
    0xE2E2: [756,240,565,32,545],      // stix-lowercase n italic slashed
    0xE2E4: [756,240,533,42,519],      // stix-lowercase o italic slashed
    0xE2E6: [756,217,581,-24,613],     // stix-lowercase p italic slashed
    0xE2E8: [756,240,521,40,523],      // stix-lowercase q italic slashed
    0xE2EA: [756,240,436,32,507],      // stix-lowercase r italic slashed
    0xE2EC: [756,240,466,26,494],      // stix-lowercase s italic slashed
    0xE2EE: [756,217,353,-22,441],     // stix-lowercase t italic slashed
    0xE2F0: [756,240,537,21,505],      // stix-lowercase u italic slashed
    0xE2F2: [756,218,506,72,545],      // stix-lowercase v italic slashed
    0xE2F4: [756,217,775,72,793],      // stix-lowercase w italic slashed
    0xE2F6: [756,240,566,32,584],      // stix-lowercase x italic slashed
    0xE2F8: [756,218,530,32,575],      // stix-lowercase y italic slashed
    0xE2FA: [756,240,499,40,507],      // stix-lowercase z italic slashed
    0xE32C: [756,218,613,42,612],      // stix-small alpha, Greek slashed
    0xE32E: [756,218,595,-47,644],     // stix-small beta, Greek slashed
    0xE330: [756,218,514,-58,634],     // stix-small gamma, Greek slashed
    0xE332: [756,218,536,40,522],      // stix-small delta, Greek slashed
    0xE334: [756,218,478,29,491],      // stix-small epsilon, Greek slashed
    0xE336: [756,218,440,11,482],      // stix-small zeta, Greek slashed
    0xE338: [756,218,512,32,536],      // stix-small eta, Greek slashed
    0xE33A: [756,218,529,20,519],      // stix-small theta, Greek slashed
    0xE33C: [756,217,326,-10,453],     // stix-small iota, Greek slashed
    0xE33E: [756,218,546,57,558],      // stix-small kappa, Greek slashed
    0xE340: [756,218,557,52,619],      // stix-small lambda, Greek slashed
    0xE342: [756,217,630,0,696],       // stix-small mu, Greek slashed
    0xE344: [756,218,466,32,495],      // stix-small nu, Greek slashed
    0xE346: [756,218,454,9,468],       // stix-small xi, Greek slashed
    0xE348: [756,240,533,27,498],      // stix-small omicron, Greek slashed
    0xE34A: [756,217,591,14,710],      // stix-small pi, Greek slashed
    0xE34C: [756,218,584,32,591],      // stix-small rho, Greek slashed
    0xE34E: [756,218,468,1,460],       // stix-terminal sigma, Greek slashed
    0xE350: [756,218,534,42,560],      // stix-small sigma, Greek slashed
    0xE352: [756,218,448,32,537],      // stix-small tau, Greek slashed
    0xE354: [756,218,514,32,545],      // stix-small upsilon, Greek slashed
    0xE356: [756,218,663,-2,690],      // stix-small phi, Greek slashed
    0xE358: [756,218,632,4,700],       // stix-small chi, Greek slashed
    0xE35A: [756,218,668,32,736],      // stix-small psi, Greek slashed
    0xE35C: [756,217,733,42,758],      // stix-small omega, Greek slashed
    0xE35E: [756,218,602,32,590],      // stix-curly or open theta, Greek slashed
    0xE360: [756,218,666,42,778],      // stix-curly or open small phi, Greek slashed
    0xE362: [756,217,889,32,897],      // stix-rounded small pi (pomega), Greek slashed
    0xE366: [756,240,444,7,482],       // stix-small stigma, Greek slashed
    0xE36A: [756,240,528,-57,648],     // stix-small digamma, Greek slashed
    0xE36E: [756,240,457,31,445],      // stix-small koppa, Greek slashed
    0xE372: [756,240,528,8,715],       // stix-small sampi, Greek slashed
    0xE374: [756,240,533,-16,559],     // stix-rounded small kappa, Greek slashed
    0xE376: [756,218,533,42,525],      // stix-rounded small rho, Greek slashed
    0xE378: [756,218,533,35,506],      // stix-partial sign, slashed
    0xE37A: [756,218,477,42,539],      // stix-rounded small epsilon, Greek, slashed
    0xE37D: [756,218,710,-50,694],     // stix-capital C script slashed
    0xE37F: [683,10,606,10,601],       // stix-small d italic with straight bar through it
    0xE381: [683,10,554,39,540],       // stix-small k italic with straight bar through it
    0xE383: [579,10,353,6,323],        // stix-small t italic with straight bar through it
    0xE385: [460,10,326,15,278],       // stix-small Greek iota with straight bar through it
    0xE387: [668,0,490,30,502],        // stix-small Greek lambda with straight bar through it
    0xE389: [668,0,490,30,478]         // LATIN SMALL LETTER LAMBDA WITH STROKE
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/NonUnicode/Italic/PrivateUse.js");
