/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/SpacingModLetters.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold'],
  {
    0x2B0: [842,-335,378,6,365],       // MODIFIER LETTER SMALL H
    0x2B1: [848,-336,378,7,365],       // MODIFIER LETTER SMALL H WITH HOOK
    0x2B2: [868,-179,300,25,273],      // MODIFIER LETTER SMALL J
    0x2B3: [699,-335,270,12,266],      // MODIFIER LETTER SMALL R
    0x2B4: [690,-326,292,10,264],      // MODIFIER LETTER SMALL TURNED R
    0x2B5: [690,-163,319,10,342],      // MODIFIER LETTER SMALL TURNED R WITH HOOK
    0x2B6: [684,-345,404,20,397],      // MODIFIER LETTER SMALL CAPITAL INVERTED R
    0x2B7: [681,-331,550,23,528],      // MODIFIER LETTER SMALL W
    0x2B8: [690,-179,380,16,374],      // MODIFIER LETTER SMALL Y
    0x2B9: [684,-421,208,30,216],      // MODIFIER LETTER PRIME
    0x2BA: [684,-421,356,19,364],      // MODIFIER LETTER DOUBLE PRIME
    0x2BB: [685,-350,333,39,223],      // MODIFIER LETTER TURNED COMMA
    0x2BC: [686,-351,333,39,223],      // MODIFIER LETTER APOSTROPHE
    0x2BD: [686,-351,250,39,223],      // MODIFIER LETTER REVERSED COMMA
    0x2BE: [662,-382,334,65,250],      // MODIFIER LETTER RIGHT HALF RING
    0x2BF: [662,-382,334,65,250],      // MODIFIER LETTER LEFT HALF RING
    0x2C0: [690,-240,353,30,333],      // MODIFIER LETTER GLOTTAL STOP
    0x2C1: [690,-240,353,30,333],      // MODIFIER LETTER REVERSED GLOTTAL STOP
    0x2C2: [760,-414,317,30,297],      // MODIFIER LETTER LEFT ARROWHEAD
    0x2C3: [760,-414,317,30,297],      // MODIFIER LETTER RIGHT ARROWHEAD
    0x2C4: [720,-453,317,-14,332],     // MODIFIER LETTER UP ARROWHEAD
    0x2C5: [720,-453,317,-14,332],     // MODIFIER LETTER DOWN ARROWHEAD
    0x2C6: [704,-528,333,-2,335],      // MODIFIER LETTER CIRCUMFLEX ACCENT
    0x2C7: [704,-528,333,-2,335],      // CARON
    0x2C8: [720,-455,279,112,167],     // MODIFIER LETTER VERTICAL LINE
    0x2C9: [637,-565,370,20,350],      // MODIFIER LETTER MACRON
    0x2CA: [713,-528,266,20,258],      // MODIFIER LETTER ACUTE ACCENT
    0x2CB: [713,-528,266,20,258],      // MODIFIER LETTER GRAVE ACCENT
    0x2CC: [70,195,278,112,167],       // MODIFIER LETTER LOW VERTICAL LINE
    0x2CD: [-88,160,370,20,350],       // MODIFIER LETTER LOW MACRON
    0x2CE: [-7,192,333,15,253],        // MODIFIER LETTER LOW GRAVE ACCENT
    0x2CF: [-7,192,333,80,318],        // MODIFIER LETTER LOW ACUTE ACCENT
    0x2D0: [474,-4,333,79,254],        // MODIFIER LETTER TRIANGULAR COLON
    0x2D1: [474,-294,333,79,254],      // MODIFIER LETTER HALF TRIANGULAR COLON
    0x2D2: [378,-62,333,65,268],       // MODIFIER LETTER CENTRED RIGHT HALF RING
    0x2D3: [378,-62,333,65,268],       // MODIFIER LETTER CENTRED LEFT HALF RING
    0x2D4: [206,-4,333,51,281],        // MODIFIER LETTER UP TACK
    0x2D5: [206,-4,333,51,281],        // MODIFIER LETTER DOWN TACK
    0x2D6: [227,-9,334,61,273],        // MODIFIER LETTER PLUS SIGN
    0x2D7: [150,-84,334,61,273],       // MODIFIER LETTER MINUS SIGN
    0x2D8: [691,-528,333,15,318],      // BREVE
    0x2D9: [666,-537,333,102,231],     // DOT ABOVE
    0x2DA: [750,-537,333,60,273],      // RING ABOVE
    0x2DB: [44,173,333,90,319],        // OGONEK
    0x2DC: [674,-547,333,-16,349],     // SMALL TILDE
    0x2DD: [713,-528,333,-13,425],     // DOUBLE ACUTE ACCENT
    0x2DE: [481,-186,292,0,302],       // MODIFIER LETTER RHOTIC HOOK
    0x2DF: [744,-506,260,10,250],      // MODIFIER LETTER CROSS ACCENT
    0x2E0: [684,-190,420,10,410],      // MODIFIER LETTER SMALL GAMMA
    0x2E1: [842,-335,190,5,186],       // MODIFIER LETTER SMALL L
    0x2E2: [695,-320,300,19,278],      // MODIFIER LETTER SMALL S
    0x2E3: [690,-335,380,12,376],      // MODIFIER LETTER SMALL X
    0x2E4: [855,-335,328,20,323],      // MODIFIER LETTER SMALL REVERSED GLOTTAL STOP
    0x2E5: [676,0,405,40,368],         // MODIFIER LETTER EXTRA-HIGH TONE BAR
    0x2E6: [676,0,405,40,368],         // MODIFIER LETTER HIGH TONE BAR
    0x2E7: [676,0,405,40,368],         // MODIFIER LETTER MID TONE BAR
    0x2E8: [676,0,405,40,368],         // MODIFIER LETTER LOW TONE BAR
    0x2E9: [676,0,405,40,368],         // MODIFIER LETTER EXTRA-LOW TONE BAR
    0x2EC: [70,167,314,5,309],         // MODIFIER LETTER VOICING
    0x2ED: [720,-528,395,5,390],       // MODIFIER LETTER UNASPIRATED
    0x2F7: [-108,235,333,-16,349]      // ??
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Bold/SpacingModLetters.js");
