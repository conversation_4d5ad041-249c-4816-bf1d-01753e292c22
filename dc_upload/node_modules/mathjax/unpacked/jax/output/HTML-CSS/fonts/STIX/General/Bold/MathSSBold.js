/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MathSSBold.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-bold'],
  {
    0x1D5D4: [690,0,690,25,665],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL A
    0x1D5D5: [676,0,636,80,594],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL B
    0x1D5D6: [691,19,723,49,688],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL C
    0x1D5D7: [676,0,709,80,674],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL D
    0x1D5D8: [676,0,635,80,597],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL E
    0x1D5D9: [676,0,582,80,570],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL F
    0x1D5DA: [691,19,746,37,671],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL G
    0x1D5DB: [676,0,715,80,635],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL H
    0x1D5DC: [676,0,440,65,375],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL I
    0x1D5DD: [676,96,481,15,406],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL J
    0x1D5DE: [676,0,712,80,707],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL K
    0x1D5DF: [676,0,603,80,587],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL L
    0x1D5E0: [676,0,913,80,833],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL M
    0x1D5E1: [676,18,724,80,644],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL N
    0x1D5E2: [692,18,778,35,743],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL O
    0x1D5E3: [676,0,581,80,569],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL P
    0x1D5E4: [691,176,779,35,754],     // MATHEMATICAL SANS-SERIF BOLD CAPITAL Q
    0x1D5E5: [676,0,670,80,657],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL R
    0x1D5E6: [691,19,554,35,511],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL S
    0x1D5E7: [676,0,641,14,627],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL T
    0x1D5E8: [676,19,699,75,624],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL U
    0x1D5E9: [676,18,690,25,665],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL V
    0x1D5EA: [676,15,997,30,967],      // MATHEMATICAL SANS-SERIF BOLD CAPITAL W
    0x1D5EB: [676,0,740,40,700],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL X
    0x1D5EC: [676,0,694,20,674],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL Y
    0x1D5ED: [676,0,653,25,623],       // MATHEMATICAL SANS-SERIF BOLD CAPITAL Z
    0x1D5EE: [473,14,489,23,428],      // MATHEMATICAL SANS-SERIF BOLD SMALL A
    0x1D5EF: [676,13,512,55,487],      // MATHEMATICAL SANS-SERIF BOLD SMALL B
    0x1D5F0: [473,14,462,25,442],      // MATHEMATICAL SANS-SERIF BOLD SMALL C
    0x1D5F1: [676,14,518,25,463],      // MATHEMATICAL SANS-SERIF BOLD SMALL D
    0x1D5F2: [473,13,452,25,433],      // MATHEMATICAL SANS-SERIF BOLD SMALL E
    0x1D5F3: [691,0,340,14,374],       // MATHEMATICAL SANS-SERIF BOLD SMALL F
    0x1D5F4: [473,206,504,28,490],     // MATHEMATICAL SANS-SERIF BOLD SMALL G
    0x1D5F5: [676,0,510,55,455],       // MATHEMATICAL SANS-SERIF BOLD SMALL H
    0x1D5F6: [688,0,245,50,195],       // MATHEMATICAL SANS-SERIF BOLD SMALL I
    0x1D5F7: [688,203,324,-57,269],    // MATHEMATICAL SANS-SERIF BOLD SMALL J
    0x1D5F8: [676,0,519,55,506],       // MATHEMATICAL SANS-SERIF BOLD SMALL K
    0x1D5F9: [676,0,235,55,180],       // MATHEMATICAL SANS-SERIF BOLD SMALL L
    0x1D5FA: [473,0,776,55,721],       // MATHEMATICAL SANS-SERIF BOLD SMALL M
    0x1D5FB: [473,0,510,55,455],       // MATHEMATICAL SANS-SERIF BOLD SMALL N
    0x1D5FC: [473,14,501,25,476],      // MATHEMATICAL SANS-SERIF BOLD SMALL O
    0x1D5FD: [473,205,512,55,487],     // MATHEMATICAL SANS-SERIF BOLD SMALL P
    0x1D5FE: [473,205,512,25,457],     // MATHEMATICAL SANS-SERIF BOLD SMALL Q
    0x1D5FF: [473,0,411,55,406],       // MATHEMATICAL SANS-SERIF BOLD SMALL R
    0x1D600: [473,13,385,25,357],      // MATHEMATICAL SANS-SERIF BOLD SMALL S
    0x1D601: [630,12,386,7,371],       // MATHEMATICAL SANS-SERIF BOLD SMALL T
    0x1D602: [461,15,518,55,463],      // MATHEMATICAL SANS-SERIF BOLD SMALL U
    0x1D603: [461,14,462,15,447],      // MATHEMATICAL SANS-SERIF BOLD SMALL V
    0x1D604: [461,14,701,17,684],      // MATHEMATICAL SANS-SERIF BOLD SMALL W
    0x1D605: [461,0,506,20,486],       // MATHEMATICAL SANS-SERIF BOLD SMALL X
    0x1D606: [461,205,472,18,455],     // MATHEMATICAL SANS-SERIF BOLD SMALL Y
    0x1D607: [461,0,441,21,417],       // MATHEMATICAL SANS-SERIF BOLD SMALL Z
    0x1D7EC: [688,13,500,24,476],      // MATHEMATICAL SANS-SERIF BOLD DIGIT ZERO
    0x1D7ED: [688,0,500,82,334],       // MATHEMATICAL SANS-SERIF BOLD DIGIT ONE
    0x1D7EE: [688,0,500,20,474],       // MATHEMATICAL SANS-SERIF BOLD DIGIT TWO
    0x1D7EF: [688,13,500,18,479],      // MATHEMATICAL SANS-SERIF BOLD DIGIT THREE
    0x1D7F0: [688,0,500,19,484],       // MATHEMATICAL SANS-SERIF BOLD DIGIT FOUR
    0x1D7F1: [676,13,500,13,483],      // MATHEMATICAL SANS-SERIF BOLD DIGIT FIVE
    0x1D7F2: [688,13,500,26,475],      // MATHEMATICAL SANS-SERIF BOLD DIGIT SIX
    0x1D7F3: [676,0,500,35,471],       // MATHEMATICAL SANS-SERIF BOLD DIGIT SEVEN
    0x1D7F4: [688,13,500,28,472],      // MATHEMATICAL SANS-SERIF BOLD DIGIT EIGHT
    0x1D7F5: [688,13,500,26,475]       // MATHEMATICAL SANS-SERIF BOLD DIGIT NINE
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Bold/MathSSBold.js");
