/*************************************************************
 *
 *  MathJax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathScript.js
 *
 *  Copyright (c) 2009-2020 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.Hub.Insert(
  MathJax.OutputJax['HTML-CSS'].FONTDATA.FONTS['STIXGeneral-italic'],
  {
    0x1D49C: [674,15,855,31,846],      // <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SCRIPT CAPITAL A
    0x1D49E: [687,15,797,37,781],      // MATHEMATICAL SCRIPT CAPITAL C
    0x1D49F: [687,15,885,36,818],      // MATHEMATICAL SCRIPT CAPITAL D
    0x1D4A2: [687,15,773,83,740],      // MATHEMATICAL SCRIPT CAPITAL G
    0x1D4A5: [674,177,802,9,792],      // MATHEMATICAL SCRIPT CAPITAL J
    0x1D4A6: [687,15,1009,40,1004],    // MATHEMATICAL SCRIPT CAPITAL K
    0x1D4A9: [687,15,970,38,956],      // MATHEMATICAL SCRIPT CAPITAL N
    0x1D4AA: [680,15,692,82,663],      // MATHEMATICAL SCRIPT CAPITAL O
    0x1D4AB: [687,15,910,38,886],      // MATHEMATICAL SCRIPT CAPITAL P
    0x1D4AC: [680,38,692,82,663],      // MATHEMATICAL SCRIPT CAPITAL Q
    0x1D4AE: [680,15,743,67,701],      // MATHEMATICAL SCRIPT CAPITAL S
    0x1D4AF: [687,15,912,43,907],      // MATHEMATICAL SCRIPT CAPITAL T
    0x1D4B0: [687,15,842,36,805],      // MATHEMATICAL SCRIPT CAPITAL U
    0x1D4B1: [687,15,932,35,922],      // MATHEMATICAL SCRIPT CAPITAL V
    0x1D4B2: [687,15,1078,35,1070],    // MATHEMATICAL SCRIPT CAPITAL W
    0x1D4B3: [687,15,891,36,873],      // MATHEMATICAL SCRIPT CAPITAL X
    0x1D4B4: [687,226,926,91,916],     // MATHEMATICAL SCRIPT CAPITAL Y
    0x1D4B5: [687,15,932,59,912],      // MATHEMATICAL SCRIPT CAPITAL Z
    0x1D4B6: [441,11,819,30,758],      // MATHEMATICAL SCRIPT SMALL A
    0x1D4B7: [687,12,580,47,559],      // MATHEMATICAL SCRIPT SMALL B
    0x1D4B8: [441,11,662,30,589],      // MATHEMATICAL SCRIPT SMALL C
    0x1D4B9: [687,11,845,30,827],      // MATHEMATICAL SCRIPT SMALL D
    0x1D4BB: [687,209,685,27,673],     // MATHEMATICAL SCRIPT SMALL F
    0x1D4BD: [687,11,753,38,690],      // MATHEMATICAL SCRIPT SMALL H
    0x1D4BE: [653,11,496,83,484],      // MATHEMATICAL SCRIPT SMALL I
    0x1D4BF: [653,219,730,9,718],      // MATHEMATICAL SCRIPT SMALL J
    0x1D4C0: [687,11,726,40,666],      // MATHEMATICAL SCRIPT SMALL K
    0x1D4C1: [687,11,579,48,571],      // MATHEMATICAL SCRIPT SMALL L
    0x1D4C2: [441,11,1038,49,978],     // MATHEMATICAL SCRIPT SMALL M
    0x1D4C3: [441,11,761,49,701],      // MATHEMATICAL SCRIPT SMALL N
    0x1D4C5: [441,209,773,23,694],     // MATHEMATICAL SCRIPT SMALL P
    0x1D4C6: [441,209,780,30,743],     // MATHEMATICAL SCRIPT SMALL Q
    0x1D4C7: [444,0,580,48,572],       // MATHEMATICAL SCRIPT SMALL R
    0x1D4C8: [531,11,515,62,412],      // MATHEMATICAL SCRIPT SMALL S
    0x1D4C9: [658,11,551,30,532],      // MATHEMATICAL SCRIPT SMALL T
    0x1D4CA: [424,11,753,30,693],      // MATHEMATICAL SCRIPT SMALL U
    0x1D4CB: [441,11,618,30,582],      // MATHEMATICAL SCRIPT SMALL V
    0x1D4CC: [441,11,888,30,852],      // MATHEMATICAL SCRIPT SMALL W
    0x1D4CD: [441,11,752,65,675],      // MATHEMATICAL SCRIPT SMALL X
    0x1D4CE: [424,219,658,30,617],     // MATHEMATICAL SCRIPT SMALL Y
    0x1D4CF: [478,11,691,52,617]       // MATHEMATICAL SCRIPT SMALL Z
  }
);

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir + "/General/Italic/MathScript.js");
